#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile dev-requirements.in
#
    # via -r dev-requirements.in
antlr4-python3-runtime==4.11.1
    # via
    #   -r requirements.txt
    #   latex2sympy2
atomicwrites==1.3.0
    # via pytest
attrs==19.3.0
    # via pytest
autopep8==1.4.4
    # via -r dev-requirements.in
click==7.0
    # via pip-tools
coverage==4.5.4
    # via pytest-cov
more-itertools==7.2.0
    # via pytest
mpmath==1.3.0
    # via
    #   -r requirements.txt
    #   sympy
packaging==19.2
    # via pytest
pip-tools==4.2.0
    # via -r dev-requirements.in
pluggy==0.13.0
    # via pytest
py==1.8.0
    # via pytest
pycodestyle==2.5.0
    # via
    #   -r dev-requirements.in
    #   autopep8
pyparsing==2.4.4
    # via packaging
pytest==5.2.2
    # via
    #   -r dev-requirements.in
    #   pytest-cov
pytest-cov==2.8.1
    # via -r dev-requirements.in
six==1.13.0
    # via
    #   packaging
    #   pip-tools
sympy==1.12
    # via
    #   -r requirements.txt
    #   latex2sympy2
wcwidth==0.1.7
    # via pytest

# THIS MUST BE MAINTAINED AS-IS
-e .