T__0=1
T__1=2
T__2=3
T__3=4
T__4=5
T__5=6
T__6=7
T__7=8
T__8=9
T__9=10
T__10=11
T__11=12
T__12=13
T__13=14
T__14=15
T__15=16
T__16=17
T__17=18
T__18=19
T__19=20
T__20=21
T__21=22
T__22=23
T__23=24
T__24=25
T__25=26
T__26=27
T__27=28
T__28=29
T__29=30
T__30=31
T__31=32
T__32=33
T__33=34
T__34=35
T__35=36
T__36=37
T__37=38
T__38=39
T__39=40
T__40=41
T__41=42
T__42=43
T__43=44
WS=45
DOLLAR_SIGN=46
ADD=47
SUB=48
MUL=49
DIV=50
L_PAREN=51
R_PAREN=52
L_GROUP=53
R_GROUP=54
L_BRACE=55
R_BRACE=56
L_BRACE_VISUAL=57
R_BRACE_VISUAL=58
L_BRACE_CMD=59
R_BRACE_CMD=60
L_BRACKET=61
R_BRACKET=62
L_BRACK=63
R_BRACK=64
BAR=65
L_VERT=66
R_VERT=67
VERT=68
NORM=69
L_FLOOR=70
R_FLOOR=71
LL_CORNER=72
LR_CORNER=73
L_CEIL=74
R_CEIL=75
UL_CORNER=76
UR_CORNER=77
L_LEFT=78
R_RIGHT=79
ML_LEFT=80
MR_RIGHT=81
FUNC_LIM=82
LIM_APPROACH_SYM=83
FUNC_INT=84
FUNC_SUM=85
FUNC_PROD=86
FUNC_LOG=87
FUNC_LN=88
FUNC_EXP=89
FUNC_SIN=90
FUNC_COS=91
FUNC_TAN=92
FUNC_CSC=93
FUNC_SEC=94
FUNC_COT=95
FUNC_ARCSIN=96
FUNC_ARCCOS=97
FUNC_ARCTAN=98
FUNC_ARCCSC=99
FUNC_ARCSEC=100
FUNC_ARCCOT=101
FUNC_SINH=102
FUNC_COSH=103
FUNC_TANH=104
FUNC_ARSINH=105
FUNC_ARCOSH=106
FUNC_ARTANH=107
FUNC_ARCSINH=108
FUNC_ARCCOSH=109
FUNC_ARCTANH=110
FUNC_ARSINH_NAME=111
FUNC_ARCSINH_NAME=112
FUNC_ARCOSH_NAME=113
FUNC_ARCCOSH_NAME=114
FUNC_ARTANH_NAME=115
FUNC_ARCTANH_NAME=116
FUNC_GCD_NAME=117
FUNC_LCM_NAME=118
FUNC_FLOOR_NAME=119
FUNC_CEIL_NAME=120
FUNC_SQRT=121
FUNC_GCD=122
FUNC_LCM=123
FUNC_FLOOR=124
FUNC_CEIL=125
FUNC_MAX=126
FUNC_MIN=127
FUNC_DET=128
FUNC_EYE_NAME=129
FUNC_ZEROS_NAME=130
FUNC_ONES_NAME=131
FUNC_COLS_NAME=132
FUNC_ROWS_NAME=133
FUNC_DIAG_NAME=134
FUNC_NORM_NAME=135
FUNC_RANK_NAME=136
FUNC_TRACE_NAME=137
FUNC_RREF_NAME=138
FUNC_HSTACK_NAME=139
FUNC_VSTACK_NAME=140
FUNC_ORTHOGONALIZE_NAME=141
FUNC_NULLSPACE_NAME=142
FUNC_DIAGONALIZE_NAME=143
FUNC_EIGENVALS_NAME=144
FUNC_EIGENVECTORS_NAME=145
FUNC_SVD_NAME=146
CMD_TIMES=147
CMD_CDOT=148
CMD_DIV=149
CMD_FRAC=150
CMD_BINOM=151
CMD_CHOOSE=152
CMD_MOD=153
CMD_MATHIT=154
CMD_OPERATORNAME=155
MATRIX_TYPE_MATRIX=156
MATRIX_TYPE_PMATRIX=157
MATRIX_TYPE_BMATRIX=158
MATRIX_TYPE_DET=159
MATRIX_TYPES=160
CMD_MATRIX_START=161
CMD_MATRIX_END=162
CMD_DET_START=163
CMD_DET_END=164
MATRIX_DEL_COL=165
MATRIX_DEL_ROW=166
UNDERSCORE=167
CARET=168
COLON=169
SEMICOLON=170
COMMA=171
PERIOD=172
DIFFERENTIAL=173
EXP_E=174
E_NOTATION_E=175
LETTER_NO_E=176
MATRIX_XRIGHTARROW=177
TRANSFORM_EXCHANGE=178
NUMBER=179
E_NOTATION=180
IN=181
ASSIGNMENT=182
EQUAL=183
LT=184
LTE=185
GT=186
GTE=187
UNEQUAL=188
BANG=189
PERCENT_NUMBER=190
GREEK_CMD=191
OTHER_SYMBOL_CMD=192
SYMBOL=193
VARIABLE=194
'\\acute'=1
'\\bar'=2
'\\overline'=3
'\\breve'=4
'\\check'=5
'\\widecheck'=6
'\\dot'=7
'\\ddot'=8
'\\grave'=9
'\\hat'=10
'\\tilde'=11
'\\widetilde'=12
'\\vec'=13
'\\overrightarrow'=14
'\\bm'=15
'\\boldsymbol'=16
'\\text'=17
'\\textit'=18
'\\mathbb'=19
'\\mathbin'=20
'\\mathbf'=21
'\\mathcal'=22
'\\mathclap'=23
'\\mathclose'=24
'\\mathellipsis'=25
'\\mathfrak'=26
'\\mathinner'=27
'\\mathnormal'=28
'\\mathop'=29
'\\mathopen'=30
'\\mathord'=31
'\\mathpunct'=32
'\\mathrel'=33
'\\mathring'=34
'\\mathrlap'=35
'\\mathrm'=36
'\\mathscr'=37
'\\mathsf'=38
'\\mathsterling'=39
'\\mathtt'=40
'^T'=41
'^{T}'=42
'^{\\top}'=43
'\''=44
'\\$'=46
'+'=47
'-'=48
'*'=49
'('=51
')'=52
'\\lgroup'=53
'\\rgroup'=54
'{'=55
'}'=56
'\\{'=57
'\\}'=58
'\\lbrace'=59
'\\rbrace'=60
'['=61
']'=62
'\\lbrack'=63
'\\rbrack'=64
'|'=65
'\\lvert'=66
'\\rvert'=67
'\\vert'=68
'\\|'=69
'\\lfloor'=70
'\\rfloor'=71
'\\llcorner'=72
'\\lrcorner'=73
'\\lceil'=74
'\\rceil'=75
'\\ulcorner'=76
'\\urcorner'=77
'\\left'=78
'\\right'=79
'\\mleft'=80
'\\mright'=81
'\\lim'=82
'\\int'=84
'\\sum'=85
'\\prod'=86
'\\log'=87
'\\ln'=88
'\\exp'=89
'\\sin'=90
'\\cos'=91
'\\tan'=92
'\\csc'=93
'\\sec'=94
'\\cot'=95
'\\arcsin'=96
'\\arccos'=97
'\\arctan'=98
'\\arccsc'=99
'\\arcsec'=100
'\\arccot'=101
'\\sinh'=102
'\\cosh'=103
'\\tanh'=104
'\\arsinh'=105
'\\arcosh'=106
'\\artanh'=107
'\\arcsinh'=108
'\\arccosh'=109
'\\arctanh'=110
'arsinh'=111
'arcsinh'=112
'arcosh'=113
'arccosh'=114
'artanh'=115
'arctanh'=116
'gcd'=117
'lcm'=118
'floor'=119
'ceil'=120
'\\sqrt'=121
'\\gcd'=122
'\\lcm'=123
'\\floor'=124
'\\ceil'=125
'\\max'=126
'\\min'=127
'\\det'=128
'eye'=129
'zeros'=130
'ones'=131
'cols'=132
'rows'=133
'diag'=134
'norm'=135
'rank'=136
'rref'=138
'hstack'=139
'vstack'=140
'nullspace'=142
'\\times'=147
'\\cdot'=148
'\\div'=149
'\\frac'=150
'\\choose'=152
'\\mod'=153
'\\mathit'=154
'\\operatorname'=155
'matrix'=156
'pmatrix'=157
'bmatrix'=158
'vmatrix'=159
'&'=165
'\\\\'=166
'_'=167
'^'=168
':'=169
';'=170
','=171
'.'=172
'E'=175
'\\in'=181
'='=182
'<'=184
'>'=186
'!'=189
