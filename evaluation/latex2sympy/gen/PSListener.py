# Generated from PS.g4 by ANTLR 4.11.1
from antlr4 import *

# This class defines a complete listener for a parse tree produced by PSParser.


class PSListener(ParseTreeListener):

    # Enter a parse tree produced by PSParser#accent_symbol.
    def enterAccent_symbol(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#accent_symbol.
    def exitAccent_symbol(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#math.

    def enterMath(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#math.
    def exitMath(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#transpose.

    def enterTranspose(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#transpose.
    def exitTranspose(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#transform_atom.

    def enterTransform_atom(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#transform_atom.
    def exitTransform_atom(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#transform_scale.

    def enterTransform_scale(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#transform_scale.
    def exitTransform_scale(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#transform_swap.

    def enterTransform_swap(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#transform_swap.
    def exitTransform_swap(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#transform_assignment.

    def enterTransform_assignment(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#transform_assignment.
    def exitTransform_assignment(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#elementary_transform.

    def enterElementary_transform(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#elementary_transform.
    def exitElementary_transform(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#elementary_transforms.

    def enterElementary_transforms(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#elementary_transforms.
    def exitElementary_transforms(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#matrix.

    def enterMatrix(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#matrix.
    def exitMatrix(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#det.

    def enterDet(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#det.
    def exitDet(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#matrix_row.

    def enterMatrix_row(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#matrix_row.
    def exitMatrix_row(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#relation.

    def enterRelation(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#relation.
    def exitRelation(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#relation_list.

    def enterRelation_list(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#relation_list.
    def exitRelation_list(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#relation_list_content.

    def enterRelation_list_content(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#relation_list_content.
    def exitRelation_list_content(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#equality.

    def enterEquality(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#equality.
    def exitEquality(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#expr.

    def enterExpr(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#expr.
    def exitExpr(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#additive.

    def enterAdditive(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#additive.
    def exitAdditive(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#mp.

    def enterMp(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#mp.
    def exitMp(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#mp_nofunc.

    def enterMp_nofunc(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#mp_nofunc.
    def exitMp_nofunc(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#unary.

    def enterUnary(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#unary.
    def exitUnary(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#unary_nofunc.

    def enterUnary_nofunc(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#unary_nofunc.
    def exitUnary_nofunc(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#postfix.

    def enterPostfix(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#postfix.
    def exitPostfix(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#postfix_nofunc.

    def enterPostfix_nofunc(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#postfix_nofunc.
    def exitPostfix_nofunc(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#postfix_op.

    def enterPostfix_op(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#postfix_op.
    def exitPostfix_op(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#eval_at.

    def enterEval_at(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#eval_at.
    def exitEval_at(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#eval_at_sub.

    def enterEval_at_sub(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#eval_at_sub.
    def exitEval_at_sub(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#eval_at_sup.

    def enterEval_at_sup(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#eval_at_sup.
    def exitEval_at_sup(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#exp.

    def enterExp(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#exp.
    def exitExp(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#exp_nofunc.

    def enterExp_nofunc(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#exp_nofunc.
    def exitExp_nofunc(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#comp.

    def enterComp(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#comp.
    def exitComp(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#comp_nofunc.

    def enterComp_nofunc(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#comp_nofunc.
    def exitComp_nofunc(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#group.

    def enterGroup(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#group.
    def exitGroup(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#norm_group.

    def enterNorm_group(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#norm_group.
    def exitNorm_group(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#abs_group.

    def enterAbs_group(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#abs_group.
    def exitAbs_group(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#floor_group.

    def enterFloor_group(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#floor_group.
    def exitFloor_group(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#ceil_group.

    def enterCeil_group(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#ceil_group.
    def exitCeil_group(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#accent.

    def enterAccent(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#accent.
    def exitAccent(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#atom_expr_no_supexpr.

    def enterAtom_expr_no_supexpr(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#atom_expr_no_supexpr.
    def exitAtom_expr_no_supexpr(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#atom_expr.

    def enterAtom_expr(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#atom_expr.
    def exitAtom_expr(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#atom.

    def enterAtom(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#atom.
    def exitAtom(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#mathit.

    def enterMathit(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#mathit.
    def exitMathit(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#mathit_text.

    def enterMathit_text(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#mathit_text.
    def exitMathit_text(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#frac.

    def enterFrac(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#frac.
    def exitFrac(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#binom.

    def enterBinom(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#binom.
    def exitBinom(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#func_normal_functions_single_arg.

    def enterFunc_normal_functions_single_arg(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#func_normal_functions_single_arg.
    def exitFunc_normal_functions_single_arg(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#func_normal_functions_multi_arg.

    def enterFunc_normal_functions_multi_arg(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#func_normal_functions_multi_arg.
    def exitFunc_normal_functions_multi_arg(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#func_operator_names_single_arg.

    def enterFunc_operator_names_single_arg(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#func_operator_names_single_arg.
    def exitFunc_operator_names_single_arg(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#func_operator_names_multi_arg.

    def enterFunc_operator_names_multi_arg(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#func_operator_names_multi_arg.
    def exitFunc_operator_names_multi_arg(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#func_normal_single_arg.

    def enterFunc_normal_single_arg(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#func_normal_single_arg.
    def exitFunc_normal_single_arg(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#func_normal_multi_arg.

    def enterFunc_normal_multi_arg(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#func_normal_multi_arg.
    def exitFunc_normal_multi_arg(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#func.

    def enterFunc(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#func.
    def exitFunc(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#args.

    def enterArgs(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#args.
    def exitArgs(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#func_common_args.

    def enterFunc_common_args(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#func_common_args.
    def exitFunc_common_args(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#limit_sub.

    def enterLimit_sub(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#limit_sub.
    def exitLimit_sub(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#func_single_arg.

    def enterFunc_single_arg(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#func_single_arg.
    def exitFunc_single_arg(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#func_single_arg_noparens.

    def enterFunc_single_arg_noparens(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#func_single_arg_noparens.
    def exitFunc_single_arg_noparens(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#func_multi_arg.

    def enterFunc_multi_arg(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#func_multi_arg.
    def exitFunc_multi_arg(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#func_multi_arg_noparens.

    def enterFunc_multi_arg_noparens(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#func_multi_arg_noparens.
    def exitFunc_multi_arg_noparens(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#subexpr.

    def enterSubexpr(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#subexpr.
    def exitSubexpr(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#supexpr.

    def enterSupexpr(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#supexpr.
    def exitSupexpr(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#subeq.

    def enterSubeq(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#subeq.
    def exitSubeq(self, ctx):
        pass

    # Enter a parse tree produced by PSParser#supeq.

    def enterSupeq(self, ctx):
        pass

    # Exit a parse tree produced by PSParser#supeq.
    def exitSupeq(self, ctx):
        pass
