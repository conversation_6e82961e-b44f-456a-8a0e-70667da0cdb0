#!/bin/bash
set -ex

PROMPT_TYPE=$1
MODEL_NAME_OR_PATH=$2
ENABLE_THINKING=${3:-false}  # Third parameter for enable_thinking, defaults to false
NUM_GPUS=${4:-1}  # Fourth parameter for number of GPUs, defaults to 1
USE_VLLM=${5:-true}  # Fifth parameter for using vLLM, defaults to true

# part after / is clean model name
OUTPUT_DIR=${MODEL_NAME_OR_PATH##*/}
SPLIT="test"
NUM_TEST_SAMPLE=-1

# Determine if this is a Qwen3 model and set appropriate parameters
if [[ "$MODEL_NAME_OR_PATH" == *"qwen3"* ]] || [[ "$MODEL_NAME_OR_PATH" == *"Qwen3"* ]]; then
    IS_QWEN3=true
    # For Qwen3 thinking mode, use recommended parameters
    if [[ "$ENABLE_THINKING" == "true" ]]; then
        TEMPERATURE=0.6
        TOP_P=0.95
        THINKING_FLAG="--enable_thinking"
        echo "Using Qwen3 thinking mode parameters"
    else
        TEMPERATURE=0.7
        TOP_P=0.8
        THINKING_FLAG=""
        echo "Using Qwen3 non-thinking mode parameters"
    fi
else
    IS_QWEN3=false
    TEMPERATURE=0
    TOP_P=1
    THINKING_FLAG=""
    echo "Using standard parameters for non-Qwen3 model"
fi

echo "Model: ${MODEL_NAME_OR_PATH}"
echo "Prompt Type: ${PROMPT_TYPE}"
echo "Is Qwen3: ${IS_QWEN3}"
echo "Enable Thinking: ${ENABLE_THINKING}"
echo "Temperature: ${TEMPERATURE}"
echo "Top P: ${TOP_P}"
echo "Number of GPUs: ${NUM_GPUS}"
echo "Use vLLM: ${USE_VLLM}"

# Target datasets: math, aime24, minerva_math, olympiadbench
DATA_NAME="math,aime24,minerva_math,olympiadbench"
echo "Evaluating datasets: ${DATA_NAME}"

# Disable flash attention to avoid compatibility issues
export DISABLE_FLASH_ATTN=1
# Set HuggingFace cache directory
export HF_HOME=/data_x/junkim100/.cache/huggingface

# Set up GPU configuration for single GPU usage
# vLLM doesn't support multi-GPU inference, so we use one GPU per evaluation
# Use the GPU that was set by the parent script, or default to GPU 0
if [ -z "$CUDA_VISIBLE_DEVICES" ]; then
    export CUDA_VISIBLE_DEVICES="0"
    echo "Using default GPU: 0"
else
    echo "Using GPU: $CUDA_VISIBLE_DEVICES"
fi

# Add vLLM flag if enabled
if [ "$USE_VLLM" = "true" ]; then
    VLLM_FLAG="--use_vllm"
    echo "Using vLLM for faster inference"

    # Set GPU memory utilization for vLLM (default 0.8 if not set)
    if [ -z "$VLLM_GPU_MEMORY_UTILIZATION" ]; then
        export VLLM_GPU_MEMORY_UTILIZATION=0.8
    fi
    echo "vLLM GPU memory utilization: $VLLM_GPU_MEMORY_UTILIZATION"
else
    VLLM_FLAG=""
    echo "Using HuggingFace transformers"
fi

TOKENIZERS_PARALLELISM=false \
python3 -u math_eval.py \
    --model_name_or_path ${MODEL_NAME_OR_PATH} \
    --data_name ${DATA_NAME} \
    --output_dir ${OUTPUT_DIR} \
    --split ${SPLIT} \
    --prompt_type ${PROMPT_TYPE} \
    --num_test_sample ${NUM_TEST_SAMPLE} \
    --seed 0 \
    --temperature ${TEMPERATURE} \
    --n_sampling 1 \
    --top_p ${TOP_P} \
    --start 0 \
    --end -1 \
    --save_outputs \
    --max_tokens_per_call 15000 \
    --overwrite \
    ${VLLM_FLAG} \
    ${THINKING_FLAG}

echo "Evaluation completed for ${MODEL_NAME_OR_PATH}"
