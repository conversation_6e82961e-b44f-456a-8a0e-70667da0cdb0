{"question": "A car is being driven, in a straight line and at a uniform speed, towards the base of a vertical tower. The top of the tower is observed from the car and, in the process, it takes 10 minutes for the angle of elevation to change from 45\u00b0 to 60\u00b0. After how much more time will this car reach the base of the tower?", "options": ["A)5(\u221a3 + 1)", "B)6(\u221a3 + \u221a2)", "C)7(\u221a3 \u2013 1)", "D)8(\u221a3 \u2013 2)", "E)None of these"], "rationale": "Explanation :\nLet the height of the building be h. Initially, he was at an angle of 450. tan 45 = h/distance between car and tower. h = distance between car and tower (since tan 45 = 1).\nNow, after 10 minutes, it travelled a certain distance, and angle changed to 600.\ntan 60 = h/x x = h/\u221a3\nSo, in 10 minutes, it has travelled a distance of h \u2013 x = h - h/\u221a3.\n10 minutes = h *( 1 \u2013 1\u221a3)\nh can be travelled in 10 / (1 \u2013 1\u221a3).\nTo travel a distance of x, which is h/\u221a3, it takes :\nh = 10 / (1 \u2013 1/\u221a3)\nh / \u221a3 = 10/ \u221a3 * (1 \u2013 1/\u221a3). Multiply numerator and denominator by 1 + \u221a3 ( conjugate of 1 - \u221a3). We get, x = h/\u221a3 = 10 (1 + \u221a3) / 2 = 5* (1 + \u221a3)\nSo, it takes 5(1 + \u221a3) minutes to reach the base of the tower.\nAnswer : A", "correct": "A"}
{"question": "The original price of an item is discounted 22%. A customer buys the item at this discounted price using a $20-off coupon. There is no tax on the item, and this was the only item the customer bought. If the customer paid $1.90 more than half the original price of the item, what was the original price of the item?", "options": ["A)$61", "B)$65", "C)$67.40", "D)$70", "E)$78.20"], "rationale": "Let x be the original price of the item\nDiscounted price = 0.78x\nPayment made by the customer after using the $20 coupon = 0.78x - 20\n0.78x - 20 = x/2 + 1.9\nx = 78.20\nAnswer: E", "correct": "E"}
{"question": "Find out which of the following values is the multiple of X, if it is divisible by 9 and 12?", "options": ["A)36", "B)15", "C)17", "D)5", "E)7"], "rationale": "9=3*3\n12=3*4\nThe number should definitely have these factors 3*3*4\n36 is the number that has these factors\nSo, 36 is the multiple of X\nAnswer is A", "correct": "A"}
{"question": "If the probability that Stock A will increase in value during the next month is 0.56, and the probability that Stock B will increase in value during the next month is 0.74. What is the greatest value for the probability that neither of these two events will occur?", "options": ["A)0.22", "B)0.26", "C)0.37", "D)0.46", "E)0.63"], "rationale": "The probability that stock A does not increase is 0.44, and the probability that stock B does not increase is 0.26. Now, how can the probability that both do not increase be more than individual probability of not increasing for each? So the probability that both do not increase can not be more than 0.26. Basically the probability that both do not increase is between 0 and 0.26.", "correct": "B"}
{"question": "A trader sold an article at a profit of 20% for Rs.360. What is the cost price of the article?", "options": ["A)270", "B)300", "C)280", "D)320", "E)315"], "rationale": "Cost Price = Selling Price / (100+Profit%) \u00d7 100 => 360 / (100+20) \u00d7 100 => 360 / 120 \u00d7 100 = Rs.300\nOption B", "correct": "B"}
{"question": "20 marbles were pulled out of a bag of only white marbles, painted black, and then put back in. Then, another 20 marbles were pulled out, of which 1 was black, after which they were all returned to the bag. If the percentage of black marbles pulled out the second time represents their percentage in the bag, how many marbles in total Q does the bag currently hold?", "options": ["A)40", "B)200", "C)380", "D)400", "E)3200"], "rationale": "We know that there are 20 black marbles in the bag and this number represent 1/20 th of the number of all marbles in the bag, thus there are total Q of 20*20=400 marbles.\nAnswer: D.", "correct": "D"}
{"question": "Find the total no. of distinct bike no.'s that can beformed using 2 letters followed by 2 no.'s. How many letters need to be distinct?", "options": ["A)74453", "B)64543", "C)74325", "D)65000", "E)97656"], "rationale": "Out of 26 alphabets two distinct letters can be chosen in 26P2 ways. Coming to the numbers part, there are 10 ways to choose the first digit and similarly, there are another 10 ways to choose the second digit. Hence, there are in total 10X10 = 100 ways.\nCombined with letters there are 6P2 X 100 ways = 65000 ways to choose vehicle numbers.\nD", "correct": "D"}
{"question": "A train running at a speed of 100 miles/hour, takes 10 hours to reach its destination. After covering quarter of the distance, it starts raining and the train has to be slowed to speed of 75 miles/hour. What is the total journey duration?", "options": ["A)10", "B)11.5", "C)12.5", "D)13.5", "E)15"], "rationale": "Distance to destination = 100 X 10 = 1000 miles.\nDistance remaining when it starts to rain = 1000 - 250 = 750 miles.\nSpeed for remaining distance = 75 miles / hour.\nTime taken to cover remaining distance = 750 / 75 = 10 hours.\nTotal duration of the journey = 2.5 + 10 = 12.5 hours.\nThe correct option is C.", "correct": "C"}
{"question": "Of the 200 students in a school, at least 45% attended the prom night and at least 35% took part in the debating session. What is the maximum number of students who could have neither attended the prom night nor the debating session?", "options": ["A)27", "B)81", "C)90", "D)99", "E)110"], "rationale": "To maximize the number of students who did neither, we should minimize the number of students who debated or attended the prom.\nLet's assume that all 35% of students who debated also attended the prom.\nThen 35% did both, 10% only attended prom, and 55% did neither.\n0.55*200 = 110\nThe answer is E.", "correct": "E"}
{"question": "A sales person gets a 10% commission on each sale he makes. How many sales of $250 each must he make in order to reach a salary of at least $1000?", "options": ["A)15", "B)24", "C)25", "D)40", "E)52"], "rationale": "10% of 250 = 25.\nTotal salary required = 1000\nEarning from single sale = 25\n# of sales = 1000/25 =40\nSo 40 sales\nD is the correct choice", "correct": "D"}
{"question": "A company produces 420 units of a particular computer component every month, at a production cost to the company of $110 per component, and sells all of the components by the end of each month. What is the minimum selling price per component that will guarantee that the yearly profit (revenue from sales minus production costs) will be at least $626,400 ?", "options": ["A)226", "B)230", "C)240", "D)260", "E)280"], "rationale": "450*12(x-110)=626400\nwhere x is a selling cost of one item\nx-110, is a profit from one item\n450 - number of items produced and sold per month\n12 - is a number of month in a year\nSimplifying the equation will lead to x-110=116, then x = 230\nB", "correct": "B"}
{"question": "At a certain factory, 10 percent of the staplers produced on Monday were defective and 2 percent of the non-defective staplers were rejected by mistake. If 72 of the non-defective staplers were rejected, what was the number of staplers produced that day?", "options": ["A)4,000", "B)4,200", "C)4,500", "D)4,800", "E)5,000"], "rationale": "We're told that 10% of staplers in a factory are defective.\nX = Total staplers\n0.1X = defective staplers\n0.9X = normal staplers\nNext, we're told that 2% of the normal staplers were rejected by mistake and that this = 72 staplers.\n0.9X(0.02) = 72\n0.018X = 72\n18X = 72,000\nX = 4,000\nFinal Answer:\nA", "correct": "A"}
{"question": "Machine A puts out a yo-yo every 6 minutes. Machine B puts out a yo-yo every 9 minutes. After how many minutes will they have produced 10 yo-yos?", "options": ["A)24 minutes", "B)32 minutes", "C)36 minutes", "D)64 minutes", "E)72 minutes"], "rationale": "A's speed = 3 yo-yos every 18 minutes\nB's speed = 2 yo-yos every 18 minutes\nA + B's speed = 3 + 2 = 5 yo-yos every 18 minutes\nboth together will finish 10 yo-yos in 36 minutes\ncorrect option is C", "correct": "C"}
{"question": "Add: +45 and -30", "options": ["A)-30", "B)+30", "C)0", "D)15", "E)-15"], "rationale": "45 - 30 = 15\nANSWER : D", "correct": "D"}
{"question": "In how many ways can the letters of the word \"PROBLEC\" be rearranged to make 7 letter words such that none of the letters repeat?", "options": ["A)2!", "B)3!", "C)7!", "D)8!", "E)9!"], "rationale": "There are seven positions to be filled.\nThe first position can be filled using any of the 7 letters contained in PROBLEM.\nThe second position can be filled by the remaining 6 letters as the letters should not repeat.\nThe third position can be filled by the remaining 5 letters only and so on.\n758\nTherefore, the total number of ways of rearranging the 7 letter word = 7*6*5*4*3*2*1 = 7! Ways.\nC", "correct": "C"}
{"question": "Let A and B be independent events with P (A) = 0.2 and P(B) = 0.8. Find P(A/B)?", "options": ["A)0.2", "B)0.4", "C)0.6", "D)1.2", "E)1.5"], "rationale": "P(A/B) = P (A n B)/P(B)\nHere, P (A n B) = 0.16\nP(A/B) = 0.16/0.8 = 0.2\nA", "correct": "A"}
{"question": "Consider there is an staircase elevator and you are coming down. If you walk 20 steps and stop, then you reach bottom in 10 minutes. If you walk 10 steps and stop, you reach to the ground in 20 minutes. What is the speed of the elevator?", "options": ["A)1 step/minute", "B)2 step/minute", "C)3 step/minute", "D)4 step/minute", "E)None of the above"], "rationale": "Let total number of steps in the elevator be n and let the speed be e\nElevator covered n-20 steps in 10 mins\n(n-20)/e=10.................1\nElevator covered n-10 steps in 20 mins\n(n-10)/e=20......................2\nFrom (1) and (2)\nn=30\ne=1 step/min\nHence (A) is correct answer.", "correct": "A"}
{"question": "Last year, a Home Appliance Store sold an average(arithmetic mean) of 42 microwave ovens per month. In the first 10 months of this year,the store has sold an average(arithmetic mean) of only 20 microwave ovens per month. What was the average number of microwave ovens sold per month during the entire 22 months period ?", "options": ["A)21", "B)30", "C)31", "D)32", "E)None of the above"], "rationale": "42\u00d712+20\u00d710 /12+10=504+200/22=704/22=32\nAnswer D", "correct": "D"}
{"question": "An exam is given in a certain class. The average (arithmetic mean) of the highest score and the lowest score is equal to x. If the average score for the entire class is equal to y and there are z students in the class, where z > 5, then in terms of x, y, and z, what is the average score for the class excluding the highest and lowest scorers?", "options": ["A)(zy \u2013 2x)/z", "B)(zy \u2013 2)/z", "C)(zx \u2013 y)/(z \u2013 2)", "D)(zy \u2013 2x)/(z -2)", "E)(zy \u2013 x)/(z + 2)"], "rationale": "Highest: H\nLowest: L\nNumber of students in the class: Z\nNumber of students in the class excluding the highest and lowest : Z-2\nAverage of Highest and Lowest: (H + L)/2=X => H+L=2X\nAverage of Entire Class : (H+L+Others)/Z=Y => Others= ZY-2X\nAverage of the others in the class: (ZY-2X)/(Z-2)\nAnswer: D", "correct": "D"}
{"question": "[5 + ? \u00d7 19 - 15 - 7]/[13 \u00d7 13 - 156] = 6", "options": ["A)4", "B)4.5", "C)5", "D)5.5", "E)6.5"], "rationale": "(? \u00d7 19 - 17)/(169 - 156) = 6\n=> ? \u00d7 19 - 17 = 13 \u00d7 6 = 76\n=> ? \u00d7 19 = 78 + 17 = 95\n? = 95/19 = 5\nAnswer: Option C", "correct": "C"}
{"question": "A grocer makes a 25% profit on the selling price for each bag of flour it sells. If he sells each bag for $100 and makes $3,000 in profit, how many bags did he sell?", "options": ["A)12", "B)16", "C)24", "D)30", "E)40"], "rationale": "Profit on one bag: 100*1.25= 125\nNumber of bags sold = 3000/125 = 24\nAnswer is C.", "correct": "C"}
{"question": "Alex and Jacob works at a toy shop that make toys. Alex takes 7 hours to make a toy, and Jacob takes 9 hours to make a toy. During a month, both of them makes 35 toys in total. If both of them have worked for almost similar number of hours how many toys have been prepared by Jacob?", "options": ["A)15", "B)16", "C)17", "D)18", "E)19"], "rationale": "Lets say Alex has worked for x hrs., and Jacob has worked for y hrs. So, number of toys prepared by Alex is x/7, and Jacob is y/9. Since total number of toys prepared by both of them is 35.\n=> x/7 + y/9 = 35.\n=> 9x + 7y = (35)(63)\n=> 7y = (35)(63) - 9x\n=> y = (5)(63) - (9/7)x\n=> y = 315 - (9/7)x\n=> x is to be a multiple of 7. Also, we need to minimize the difference between x & y. Here are some possible values,\nx = 126, y = 315 - (9/7)126 = 153\nx = 133, y = 315 - (9/7)133 = 144\nx = 140, y = 315 - (9/7)140 = 135\nx = 147, y = 315 - (9/7)147 = 126\nAs we can see minimum difference between x and y is when x is 140 hrs. and y is 135 hrs. Thus total toys created by Jacob = y/9 = 135/9 = 15.\nAnswer: A", "correct": "A"}
{"question": "John likes to have lightly flavored tea every evening. In a 50% strong milk tea, he replaces 15% of it with milk twice. Then, he replaces 10 percent of the resultant solution with more milk.\nWhat is the final concentration of tea John drinks?", "options": ["A)15.38%", "B)42%", "C)39.86%", "D)22.35%", "E)32.51%"], "rationale": "Imagine starting out with 100 ml of 50% milk tea.\nIn step 1, 15% of the tea is replaced with milk. Thus, 85% of the original tea remains. Since this is done twice, we have a concentration of 50x0.85x0.85% (=36.125%) of tea solution.\nFinally, 10% of this solution is replaced with milk again. So, the final concentration of tea is 36.125*0.9%\nThis equals 32.51% of tea solution.\nAnswer: E", "correct": "E"}
{"question": "In a class 1/16 of the students study math, 1/10 of the students study bio, 1/8 of the students study english. The total number of students is a 4 digit number. Find the diffrence between maximum number of students and minimum number of students.", "options": ["A)8880", "B)8870", "C)8890", "D)7890", "E)6780"], "rationale": "LCM of 16,10,8 = 80\nthe largest 4 digit number divisible by 80 = 9920\nThe smallest 4 digit number divisible by 80 = 1040\nSo, required difference = 9920-1040= 8880\nANSWER:A", "correct": "A"}
{"question": "On a normal day Bill usually averages about 15 mph when riding his bicycle. On a windy day, his speed is reduced by 4 mph. How far can Bill travel on a windy day in 21 minutes? Round to the nearest hundredth.", "options": ["A)2 miles", "B)2.25 miles", "C)3.25 miles", "D)3.85 miles", "E)2.85 miles"], "rationale": "15 mph - 4 mph= 11 mph\n11 mph x (21/60)= 3.85 miles\nAnswer D", "correct": "D"}
{"question": "A retailer sold an appliance for 40 percent above cost, which represented a gross profit of $20.00. For what price did the retailer sell the appliance?", "options": ["A)$27.30", "B)$51.00", "C)$63.00", "D)$70.00", "E)$91.00"], "rationale": "Let the cost be A. Then the selling price is A+0.4*A.\nSo the profit is 0.4 * A.\n0.4*A=20 ---> A=50.\nSo the selling price is 50+20=70.\nThe answer is (D).", "correct": "D"}
{"question": "At 6% per annum simple interest, Rahul borrowed Rs. 500. What amount will he pay to clear the debt after 4 years", "options": ["A)750", "B)700", "C)620", "D)600", "E)None of these"], "rationale": "We need to calculate the total amount to be paid by him after 4 years, so it will be Principal + simple interest.\nSo,\n=>500+500\u22176\u22174 /100=>Rs.620\nOption C", "correct": "C"}
{"question": "A computer routine was developed to generate two numbers (x,y) the first being a random number between 0 and 100 inclusive, and the second being less than or equal to the square root of the first. Each of the following pair satisfies the routine except", "options": ["A)(99,10)", "B)(85,9)", "C)(50,7)", "D)(1,1)", "E)(1,0)"], "rationale": "99 is generated\nWe don't know what the square root of 99 is because we would need a calculator, but we know the square root of 100 is 10, so the square root of 99 has to be less than 10.\nANSWER:A", "correct": "A"}
{"question": "A jeep travels a certain distance taking 6 hours in the forward journey. During the return journey, it increased its speed by 12km/hr and took 4 hours. What is the distance travelled by the jeep?", "options": ["A)126km", "B)144km", "C)127km", "D)228km", "E)128km"], "rationale": "Let 'x' be the distance and 'y' be the speed of the forward journey. Then, we have 6v=d and 4(v+12)=d\n=> v=d/6 and v=d/4 - 12\n=> d/6 = d/4 - 12\n=> d/12 = 12\n=> d=144\nAnswer: B", "correct": "B"}
{"question": "When I was 2 years old, my brother was half my age. Now I am 60 years old, how old is my brother?", "options": ["A)A)59", "B)B)69", "C)C)79", "D)D)89", "E)E)99"], "rationale": "Half of 2 is 1. =>2+58=60-> 1+58=59\nAnswer A", "correct": "A"}
{"question": "The original retail price of an appliance was 60 percent more than its wholesale cost. If the appliance was actually sold for 20 percent less than the original retail price, then it was sold for what percent more than its wholesale cost?", "options": ["A)20%", "B)28%", "C)36%", "D)40%", "E)42%"], "rationale": "wholesale cost = 100;\noriginal price = 100*1.6 = 160;\nactual price = 160*0.8 = 128.\nAnswer: B.", "correct": "B"}
{"question": "On a map, the length of the road from Town F to Town G is measured to be 20 inches. On this map, 1/4 inch represents an actual distance of 10 miles. What is the actual distance, in miles, from Town F to Town G along this road?", "options": ["A)800", "B)720", "C)960", "D)1140", "E)1160"], "rationale": "Here we are given a ratio: 1/4 inch on the map = 10 miles, so 1 inch on the map = 40 miles. If the map-distance between the towns is 20 inches, then the actual distance must be 20 x 40 = 800\nAnswer: A.", "correct": "A"}
{"question": "When folded into two equal halves a rectangular sheet had a perimeter of 48cm for each part folded along one set of sides and the same is 66cm when folded along the other set of sides. Find the area of the sheet.", "options": ["A)1584", "B)1120", "C)792", "D)1320", "E)1200"], "rationale": "Let the sheet be folded along its breadth and its perimeter = 48cm\nTherefore, (l/2 + b) = 48 ... (i)\nNow, let the sheet be folded along its length, and the perimeter = 66cm\n(l + b/2)= 66 \u2026... (ii)\nSolving (i) and (ii), we get,\nl = 56cm, b = 20cm\nArea = l*b\nArea = 1120 cm2\nANSWER IS B", "correct": "B"}
{"question": "Suppose you can travel from a place M to a place N by 3 buses, from place N to place O by 4 buses, from place O to place P by 1 buses and from place P to place Q by 3 buses. In how many ways can you travel from M to Q ?", "options": ["A)24", "B)36", "C)72", "D)84", "E)None"], "rationale": "The bus from M to N can be selected in 3 ways. The bus from N to O can be selected in 4 ways. The bus from O to P can be selected in 1 way. The bus from P to Q can be selected in 3 ways. So, by the General Counting Principle, one can travel from M to Q in 3*4*1*3= 36 ways\nAnswer B", "correct": "B"}
{"question": "A rectangular solid, 3 x 4 x 15, is inscribed in a sphere, so that all eight of its vertices are on the sphere. What is the diameter of the sphere?", "options": ["A) 13.3542", "B) 15.8113", "C) 18.3451", "D) 19.5667", "E) 20.8888"], "rationale": "In an inscribed rectangle in a sphere, we will have a line joining the opposite vertices as the diameter.\nAccording to the Pythagoras theorem, sides 3, 4 give diagonal as 5 ==> with 5 and 15, we get 5sqrt(10).\n5sqrt(10) or 15.8113 is the diameter of the sphere.\nanswer = B", "correct": "B"}
{"question": "A starts travel towards south 3km, then travel 5 km towards east, and again travels 3 km to north, and finally travels 2km towards west. In the end how far from is A from home?", "options": ["A)3km", "B)2km", "C)4km", "D)5km", "E)6km"], "rationale": "3s,5e,3n,2w\n5-2=3e\n3-3=0\n3km\nANSWER:A", "correct": "A"}
{"question": "While selling a watch, a shopkeeper gives a discount of 5%. If he gives a discount of 7%, he earns Rs. 15 less as profit. The marked price of the watch is:", "options": ["A)Rs. 697.50", "B)Rs. 712.50", "C)Rs. 787.50", "D)Rs. 750", "E)Rs. 780"], "rationale": "If he increases the discount by 2%, then his profit is 15 less. Let the marked price be X.\n.02x = 15\nx = 750 marked price\nANSWER:D", "correct": "D"}
{"question": "A student instead of finding the value of 7/8 of a number, found the value of 7/18 of the number. If his answer differed from the actual one by 770, find the that number.", "options": ["A)1584", "B)2520", "C)1728", "D)1656", "E)None"], "rationale": "According to the question,\n=> [7/8 - 7/18 ]x = 770\n=> 7*10*x /18*8 = 770\n=> x = 11*18*8\n=> 1584.\nAnswer : A", "correct": "A"}
{"question": "The monthly salary S of a shop assistant is the sum of a fixed salary of $500 plus 5% of all monthly sales. What should the monthly sales be so that her monthly salary reaches $1500?", "options": ["A)$50000", "B)$40000", "C)$30000", "D)$20000", "E)None of these"], "rationale": "Let S be the total monthly salary and x be the monthly sales, hence\nS = 500 + 5% * x\nFind sales x so that S = 1500, hence\n1500 = 500 + 5% * x = 500 + 0.05 x\nSolve for x\nx = (1500 - 500) / 0.05 = $20000\nAnswer D", "correct": "D"}
{"question": "An aeroplane flies along the four sides of a square at the speeds of 200, 400, 600 and 800km/hr. Find the average speed of the plane around the field?", "options": ["A)384", "B)562", "C)458", "D)156", "E)452"], "rationale": "Let the each side of the square is x km\naverage speed of plane is y km/hr\n(x/200)+(x/400)+(x/600)+(x/800) = 4x/y\n25x/2400 = 4x/y\ny= 384 km/hr\nAnswer is A", "correct": "A"}
{"question": "Jack buys 18 sharpeners (white and brown) for rs. 100. If he pays 1 rupee more for each white than brown sharpeners. How many of white and how many brown sharpeners did he buy?", "options": ["A)10,8", "B)9,8", "C)7,8", "D)5,6", "E)11,12"], "rationale": "Total cost=100\nnumber of sharp=18\ncost of white=cost of brown+1\n100/18=5.5...-(1)\nalso 100%18=10...-(2)\nas cost of white is 1 more than that of brown\nfrom 1 int. value will be 5\nnow remainder is 10 so 10 sharp. will be of cost (5+1)\n=> 10*(5+1)+8*5\n=>10*6+8*5\n=60+40\n100\nwhite=10\nbrown=8\nANSWER:A", "correct": "A"}
{"question": "Hoses A and B spout water at different constant rates, and hose A can fill a certain pool in 8 hours. Hose A filled the pool alone for the first 2 hours and the two hoses, working together, then finished filling the pool in another 3 hours. How many hours would it have taken hose B, working alone, to fill the entire pool?", "options": ["A)8", "B)15", "C)12", "D)6", "E)3"], "rationale": "Since hose A can fill the pool in 8 hours, then in 2 + 3 = 5 hours it will fill 5/8th of the pool. Thus the remaining 3/8th is filled by hose B in 3 hours. This means that hose B,working alone, to fill the entire pool will need 3*8/3 = 8 hours.\nAnswer: A.", "correct": "A"}
{"question": "If 120 is reduced to 96, what is the reduction percent?", "options": ["A)30%", "B)40%", "C)20%", "D)10%", "E)5%"], "rationale": "reduction = 120 \u2013 96 = 24\n\u2234 Reduction percent = (24/120)\u00d7100% =20%\nAnswer:C", "correct": "C"}
{"question": "I know a 5 digit number having a property that with a 1 after it, it is three times as large as it would be with a 1 before it.\nWhat is that number?", "options": ["A)42857", "B)32456", "C)76523", "D)24567", "E)43566"], "rationale": "Let the number be x\n10x +1 = 3(100,000 + x)\n=> x = 42857.", "correct": "A"}
{"question": "At Daifu university, 24% of all students are members of both a chess club and a swim team. If 20% of members of the swim team are not members of the chess club, what percentage of all Daifu students are members of the swim team?", "options": ["A)20%", "B)30%", "C)40%", "D)50%", "E)60%"], "rationale": "Assume there are total of 100 students. 24 students are members of both clubs. We are told that:20% of members of the swim team are not members of the chess club, thus if S is a # of members of the swim team then 0.2S is # of members of only the swim teem:\n24+0.2S=S --> S=30.\nAnswer: B.", "correct": "B"}
{"question": "If the population of a city increases by 5 % annually, what will be the population of the city in 2 years time if its current population is 78000?", "options": ["A)81900", "B)85995", "C)85800", "D)90000", "E)None of these"], "rationale": "The % change in population of city in two years time is 1.05*1.05 = 1.1025 = 10.25%\nTherefore, after 2 years the population of the city will be 1.1025 * 78000 = 85995\nANSWER B", "correct": "B"}
{"question": "Two cars start at the same time from opposite ends of a highway that is 50 miles long. One car is riding at 12 mph and the second car is riding at 13 mph. How long after they begin will they meet?", "options": ["A) 1", "B) 1.25", "C) 1.50", "D) 1.75", "E) 2"], "rationale": "Time they will meet = total distance/ relative speed= 50/12+13 = 50/25 = 2\nAnswer is E", "correct": "E"}
{"question": "A shopkeeper employed a servant at a monthly salary of 1500. In addition to it, he agreed to pay him a commission of 15% on the monthly sale. How much sale in Rupees should the servant do if he wants his monthly income as 6000?", "options": ["A)30000", "B)415000", "C)31500", "D)50000", "E)None of these"], "rationale": "Servant\u2019s commission amount\n= 6000 \u2013 1500 = 4500\ni.e.,15% = 4500\nor,100% = 4500\u204415 \u00d7 100 = 30000\nAnswer A", "correct": "A"}
{"question": "A man borrows Rs.360 If he pays it back in 12 monthly installments of Rs.31.50, what is his interest rate?", "options": ["A)1.5%", "B)4.5%", "C)10%", "D)5%", "E)12%"], "rationale": "Instead of paying monthly 360/12 = 30Rs, the man pays 31.50Rs. Therefore, the interest rate is 1.5/30 = 0.5/10 = 5/100 = 5%.\nAnswer D", "correct": "D"}
{"question": "The price of a product is reduced by 30% . By what percentage should it be increased to make it 100%", "options": ["A)41.86%", "B)42.86%", "C)43.86%", "D)44.86%", "E)45.86%"], "rationale": "If initial price is Rs 100 and reduced price is Rs 70.\nThen, to make it 100 again, price should increase by 100*30/70= 300/7 % or 42.86% approx\nANSWER:B", "correct": "B"}
{"question": "I have a money pouch containing Rs. 700. There are equal number of 25 paise coins, 50 paise coins and one rupee coins.\nHow many of each are there?", "options": ["A)453", "B)651", "C)400", "D)487", "E)286"], "rationale": "25 paise + 50 paise + 100 paise = 175 paise and Rs. 700 = 70,000 paise\n70,000/175 = 400", "correct": "C"}
{"question": "A man spends Rs. 3500 per month and saves 12 1/2% of his income. His monthly income is ?", "options": ["A)Rs. 4400", "B)Rs. 4270", "C)Rs. 4000", "D)Rs. 3937.50", "E)None of these"], "rationale": "87 1/2% of P = 3500\n\u21d2 {(175/2) x P} / 100 = 3500\n\u2235 P = (3500 x 2 x 100) / 175 = 4000\nCorrect Option: C", "correct": "C"}
{"question": "Five dozen toys are packed in a box and 98 boxes are kept in a tempo. How many tempos can lift 29400 toys in one round ?", "options": ["A)4", "B)5", "C)7", "D)6", "E)8"], "rationale": "Five dozen = 5 x 12 = 60\n\u21d2 No of toys can be kept in 1 box = 60\n\u2234 No of toys can be kept in 98 boxes = 60 x 98 = 5880\n\u2234 29400 toys can be lifted by = 29400 / 5880 = 5 tempos\nOption: B", "correct": "B"}
{"question": "There are 10 oranges in a basket. Find the no. of ways in which 2 oranges are chosen from the basket?", "options": ["A)45", "B)90", "C)120", "D)150", "E)180"], "rationale": "Required number of ways = 10C2 = 10*9/2 = 45\nAnswer is A", "correct": "A"}
{"question": "A company contracts to paint 3 houses. Mr.Brown can paint a house in 6 days while Mr.Black would take 8 days and Mr.Blue 12 days. After 8 days Mr.Brown goes on vacation and Mr. Black begins to work for a period of 6 days. How many days will it take Mr.Blue to complete the contract?", "options": ["A)7", "B)8", "C)10", "D)11", "E)12"], "rationale": "let x is amount of work to be done to paint one house.\nSo Brown's one day work is x/6, black's can do x/8 work in\none day and blue is x/12.\nTotal houses is 3, so tatal work to be done is 3x.\n3x= 8*(x/6) + 6*(x/8) + y*(x/12)\nfinally y = 11.\nblue will complete the remaining work in 11 days.\nANSWER:D", "correct": "D"}
{"question": "Train A leaves a station every 16 minutes and Train B leaves every 17 minutes. If both trains just left the station simultaneously, how long until they do so again?", "options": ["A)272 minutes", "B)304 minutes", "C)190 minutes", "D)70 minutes", "E)35 minutes"], "rationale": "We have to find the LCM:\n17 is a prime number which means the LCM of 16 and 17 has to be 16*17=272\nCorrect answer is A.", "correct": "A"}
{"question": "A hollow cube of size 5cm is taken, with the thickness of 1cm. It is made of smaller cubes of size 1cm .If the outer surface of the cube is painted how many faces of the smaller cubes remain unpainted?", "options": ["A)438", "B)550", "C)500", "D)450", "E)498"], "rationale": "Volume of Big Cube considering it is not hollow = L3 = 5*5*5 = 125 cm3\nSize of hollow cube (considering 1 cm thickness on two faces of large cube = 5 - 2 = 3cm\nVolume of hollow cube = 3*3*3 = 27 cm3\nSo Total Volume filled up by smaller cubes = Volume of Larger Cube - Volume of hollow cube\n= 125 - 27\n= 98 cm3\nVolume of 1 small cube = 1*1*1 = 1 cm3\nTotal number of small cubes in the larger cube = 98 / 1 = 98\nand Number of faces of 98 small cubes (6 faces each cube has) = 98*6 = 588 faces\nTotal Surface area of 6 faces of larger cube painted = 6*L2 = 6*5*5 = 150cm2\nSurface area of one face of small cube = 1*1 = 1cm2\nNumber of faces of small cube painted = 150/1 = 150 faces\nHence number of faces of the smaller cubes remain unpainted= 588-150\n= 438\nanswer.A", "correct": "A"}
{"question": "In a chocolate store, all chocolates are either vanilla or cocoa flavored only. 10% of the chocolates are cocoa flavored, 90% of the rest are squashed. What percentage of the chocolates are both vanilla flavored and not squashed?", "options": ["A)1%", "B)2%", "C)5%", "D)9%", "E)10%"], "rationale": "If 10% of chocolates are cocoa flavored, then 90% are vanilla flavored.\n90% of 90% are squashed, i.e. 81% are squashed.\nVanilla flavored and non squashed= 90-81= 9%\nD is the answer", "correct": "D"}
{"question": "There is well of depth 30m and frog is at bottom of the well. He jumps 3m up one day and falls back 2m down the same day. How many days will it take for the frog to come out of the well?", "options": ["A)25 days", "B)26 days", "C)27 days", "D)28 days", "E)29 days"], "rationale": "frog jumps 3 m up day & falls back 2 m down at night\nso,frog will be 3-2=1 m up in a day.\nThus, in 27 days it will be 27 m up\non 28 th day it will be at top i.e 27+3 = 30 m & will not fall down.\nANSWER:D", "correct": "D"}
{"question": "The sum of the 5 consecutive two digit odd numbers when divided by 10 becomes a perfect square, which of the following can be one of these 5 numbers?", "options": ["A)47", "B)91", "C)41", "D)67", "E)44"], "rationale": "perfect square:- 1,4,9,16,25,36\nsum=square*10=10,40,90,160,250,360\nsum of 4 odd consecutive numbers is multiple of 4\nso the only number left are 40,160,360\nsum/4=40/4=10 is not possible\nsum/4=360/4=90 is not possible\nsum/4=160/4=40 is the only option available i.e 41\nANSWER:C", "correct": "C"}
{"question": "In a class, 8% of total students are interested in Football. 4/5 of total students are interested in Cricket. 10% of total students are interested in Basketball and remaining 20 students are not interested in any games. How many students are there in the class?", "options": ["A)850", "B)800", "C)900", "D)950", "E)1000"], "rationale": "Let x is total no. of students\n8x/100+4x/5+10x/100+20=x\nBy solving this\nx=1000\nANSWER:E", "correct": "E"}
{"question": "Q is as much younger than R as he is older than T. If the sum of the ages of R and T is 50 years. What is definitely the difference between R and Q's age?", "options": ["A)22", "B)27", "C)29", "D)Cannot be determined", "E)None of the above"], "rationale": "R - Q = R - T\nQ = T.\nAlso R + T = 50; R + Q = 50\nSo, (R - Q) cannot be determined.\nAnswer:D", "correct": "D"}
{"question": "Calculate the maximum distance you can travel with $8.50 on a cab which charges $3.50 for the first quarter-mile and 10 cents for each additional quarter mile.", "options": ["A)11.75 miles", "B)12.75 miles", "C)17.75 miles", "D)14.75 miles", "E)10.75 miles"], "rationale": "Subtract the fee for te first quarter mile $8.50 - $3.50 = $5\nCalculate how many extra additional quarter miles---$5/10 cents => 50 quarter miles => 12.5 miles\nTotal distance is 12.5 miles + 1/4 (first quarter mile)\n12.75 miles\nAnswer: B", "correct": "B"}
{"question": "In IPL season, Sachin current batting average is 51. In the finals, he scores 78 runs, then is batting average will be 54. Find out the total number of matches played by Sachin in this season.", "options": ["A)6", "B)8", "C)9", "D)10", "E)11"], "rationale": "Let total number of matches = x\nthen, total runs 54*x\ntotal runs before final = 51*(x-1)\nruns in the final match\n54*x - 51*(x-1) = 78\nx= 9\nANSWER:C", "correct": "C"}
{"question": "Amy is organizing her bookshelves and finds that she has 10 different types of books. She then codes each book with either a single letter or a pair of two different letters. If each type of book is uniquely represented by either a single letter or pair of letters, what is the smallest number of letters Amy will need to create the codes for all 10 types of books? (Assume the order of letters in a pair does not matter.)", "options": ["A)3", "B)4", "C)5", "D)10", "E)20"], "rationale": "The question asks for the smallest value of n, such that (n + nC2) = 10 (n represents the number of letters. In this equation, n by itself is for single-letter codes and nC2 is for two-letter codes).\nAt this point, you'd need to pick numbers, since there's really no easy way to solve nC2 = (10 \u2013 n) without a calculator.\nLooking at the answer choices, you can eliminate 10 and 20, so you can quickly narrow down the values you need to test. (i.e. (10 \u2013 n) suggests n can not be less than 10.)\nAs a general rule, whenever you're asked for the smallest value that satisfies a condition, start by testing the smallest number in the answers. Conversely, if you're asked for the largest value, start with the greatest answer.\nPlug-in n=4 to (n + nC2) = (4 + 4C2) = 4 + (4x3 /2) = (4 + 6) = 10 ANS:D", "correct": "D"}
{"question": "A rectangular piece of 150 sq m has a length which is 1m more than the 4 times the breadth. What is the perimeter of the piece?", "options": ["A)60 m", "B)61 m", "C)62 m", "D)63 m", "E)64 m"], "rationale": "Let its breadth be = x m.\nSo length will be = (4x+1) m.\nNow,\nx * (4x+1) = 150\nor, 4x^2+x-150 = 0\nor, (4x+25)(x-6) = 0\nEither 4x = -25 or x = 6\nAs breadth can not take negetive value so x = 6\nSo its length is 4*6+1 = 25\nSo perimeter will be 2*(25+6)=62 mLet its breadth be = x m.\nSo length will be = (4x+1) m.\nNow,\nx * (4x+1) = 150\nor, 4x^2+x-150 = 0\nor, (4x+25)(x-6) = 0\nEither 4x = -25 or x = 6\nAs breadth can not take negetive value so x = 6\nSo its length is 4*6+1 = 25\nSo perimeter will be 2*(25+6)=62 m\nANSWER:C", "correct": "C"}
{"question": "One gram of a certain health food contains 9 percent of the minimum daily requirement of vitamin E and 8 percent of the minimum daily requirement of vitamin A. If vitamins E and A are to be obtained from no other source, how many grams of the health food must be eaten daily to provide at least the minimum daily requirement of both vitamins?", "options": ["A)8.5", "B)10.5", "C)12.5", "D)14.5", "E)16.5"], "rationale": "100% / 8% = 12.5\n12.5 grams of the health food provides 12.5(8%) = 100% of the vitamin A requirement and more than 100% of the vitamin E requirement.\nThe answer is C.", "correct": "C"}
{"question": "Assistants are needed to prepare for preparation. Each helper can make either 2 large cakes or 35 small cakes/hr. The kitchen is available for 3 hours and 20 large cakes & 700 small cakes are needed. How many helpers are required?", "options": ["A)8", "B)10", "C)12", "D)15", "E)19"], "rationale": "20 large cakes will require the equivalent of 10 helpers working for one hour. 700 small cakes will require the equivalent of 20 helpers working for one hour. This means if only one hour were available we would need 30 helpers. But since three hours are available we can use 10 helpers.\nB", "correct": "B"}
{"question": "R, S, T, and U are points on a line, and U is the midpoint of line segment ST. If the lengths of line segments RS, RT, and ST are 5, 17, and 22, respectively. What is the length of line segment RU?", "options": ["A)6", "B)7", "C)8", "D)9", "E)10"], "rationale": "Since SR + RT = 22 = ST, then R is somewhere between S and T.\nSince ST is 22, then SU is 11 because U is the midpoint of ST.\nSince SR < SU, then R is somewhere between S and U.\nThen SR + RU = SU.\n5 + RU = 11\nRU = 6\nThe answer is A.", "correct": "A"}
{"question": "Six pita breads contain the same amount of falafel as do two rolls. Three rolls contain the same amount of falafel as five baguettes do. Two baguettes contain the same amount of falafel as how many pita breads?", "options": ["A)12/25", "B)3/2", "C)3", "D)2", "E)25/3"], "rationale": "6P = 2R\n3R = 5B\n2B = ?P\nThus, P : R : B = 18 : 6 : 12\nP : B = 18 : 12\n= 3 : 2\nThus P = 3\nAnswer : C", "correct": "C"}
{"question": "A shopkeeper in order to promote his new shop put a discount of 20% on all the items for one day. Now he must sell the items at original price the other day. By what percentage must he increase the price to original?", "options": ["A)21%", "B)20%", "C)25%", "D)33%", "E)18%"], "rationale": "Suppose every item is priced at $100. On 20% discount, the price will become $80. Now he must add $20 to each item for original price which is 25% of $80.", "correct": "C"}
{"question": "The bus fare for two persons for travelling between Agra and Aligarh id four-thirds the train fare between the same places for one person. The total fare paid by 6 persons travelling by bus and 8 persons travelling by train between the two places is Rs.1512. Find the train fare between the two places for one person?", "options": ["A)126", "B)77", "C)88", "D)66", "E)54"], "rationale": "Let the train fare between the two places for one person be Rs.t\nBus fare between the two places for two persons Rs.4/3 t\n=> 6/2 (4/3 t) + 8(t) = 1512\n=> 12t = 1512 => t = 126.\nAnswer:A", "correct": "A"}
{"question": "A rectangle has a length of 8 centimeters and a width of 3 centimeters. Find the perimeter.", "options": ["A)18cm", "B)22cm", "C)20cm", "D)30cm", "E)28cm"], "rationale": "Perimeter = 2(8 cm) + 2(3 cm) = 16 cm + 6 cm = 22 cm\nanswer:B.", "correct": "B"}
{"question": "Suppose you want to arrange your English, Hindi, Mathematics, History, Geography and Science books on a shelf. In how many ways can you do it ?", "options": ["A)520", "B)720", "C)920", "D)None", "E)Cannot be determined"], "rationale": "We have to arrange 6 books. The number of permutations is 6*5*4*3*2*1= 720\nAnswer : B", "correct": "B"}
{"question": "A straight picket fence is composed of x pickets each of which is 1/2 inch wide. If there are 6 inches of space between each pair of pickets, which of the following represents the length of fence in feet?", "options": ["A)13x/2", "B)13x/2 - 6", "C)13x/24", "D)(13x+1)/24", "E)(13x-12)/24"], "rationale": "Number of pickets = x\nSize of pickets = 1/2\nlength of pickets = 1/2x\nIf there are x pickets, it implies that there are x -1 spaces between the picket\nLength of space = 6\ntotal number of length = 1/2 x + 6(x-1) in inches\ntotal length in feet =( 1/2 x + 6(x-1))/12\nSimplify to get (13X-12)/24\nANSWER:E", "correct": "E"}
{"question": "A ship went on a voyage. After it had traveled 180 miles a plane started with 10 times the speed of the ship. Find the distance when they meet from starting point.", "options": ["A)238", "B)289", "C)200", "D)287", "E)187"], "rationale": "Let the speed of the ship = m miles/hr. and plane took 't' hours to meet the ship\nThen, m\u00d7t is the distance ship traveled after plane started\nSo we have, mt + 180 = 10mt\n\u21d2 9mt = 180\n\u21d2 mt = 20\nHence distance = 180 + 20 = 200 miles\nAnswer:C", "correct": "C"}
{"question": "In a large forest, 300 deer were caught, tagged, and returned during 2001. During 2002, 500 deer were caught at random, of which only 20 had tags from the previous year. If the percent of deer in the forest that had tags during the second year and were caught in the 500 deer sample is representative of the percent of the total deer population in the forest with tags, what is the total deer population in the forest (assuming no change in population between 2001 and 2002)?", "options": ["A)300", "B)500", "C)5000", "D)6000", "E)7500"], "rationale": "Let N = the total number of deer in the forest.\nDuring the first year, the percent of deer in the entire population with tags was: 300/N\n20/500 is the percent of deer caught during the second year that had tags. Since this sample percent matches the percent for the entire population (i.e., the total number of tagged deer divided by the total number of deer), the two ratios are equal.\nEquating these two percents:\nSample = Population\n(20/500)=(300/N)\nN = (300/1)*(500/20)\nN=7500\nAnswer E", "correct": "E"}
{"question": "In a railway station, there are two trains going. One in the harbor line and one in the main line, each having a frequency of 10 minutes. The main line service starts at 5 o'clock and the harbor line starts at 5.02 A.M. A man goes to the station every day to catch the first train that comes. What is the probability of the man catching the first train?", "options": ["A)0.9", "B)0.8", "C)0.6", "D)0.65", "E)1.5"], "rationale": "For each 10 min interval, if man comes in first 2 min, he'll catch the 1st train, if he comes in next 8 min, he'll catch the 2nd train.\nHence, for harbor line = (2/10) = 0.2 and for main line 0.8.\nAnswer:B", "correct": "B"}
{"question": "The average (arithmetic mean) of the weight of 10 vehicles is 12.2 tons. The average weight of the group of vehicles increased by 2.6 tons after a new heavy duty truck was added to the group? What is the weight in tons of the heavy duty truck?", "options": ["A)40.8", "B)41.6", "C)42.2", "D)43.5", "E)44.8"], "rationale": "The new average is 14.8 tons.\nOn average, the ten trucks are 2.6 tons below the average for a total weighting of 26 tons.\nTherefore, the added truck must be 14.8 + 26 = 40.8 tons\nThe answer is A.", "correct": "A"}
{"question": "Boomtown urban planners expect the city\u2019s population to increase by 10% per year over the next two years. If that projection were to come true, the population two years from now would be exactly double the population of one year ago. Which of the following is closest to the percent population increase in Boomtown over the last year?", "options": ["A)20%", "B)40%", "C)50%", "D)65%", "E)75%"], "rationale": "Population now - 100;\nPopulation one year from now - 110;\nPopulation two years from now - 121;\nSince the population two years from now (121) is exactly double the population one year ago then the population one year ago was 121/2=60.5.\nNow, the question asks about the population increase over the last year, so from 60.5 (last year) to 100 (now): percent increase=difference/original*100=(100-60.5)/60.5*100=39.5/60.5*100=~2/3*100=~65%.\nAnswer: D.", "correct": "D"}
{"question": "Arjun and Sajal are friends, each has some money. If Arun gives $30 to Sajal, the Sajal will have twice the money left with Arjun. But, if Sajal gives $10 to Arjun, Arjun will have thrice as much as is left with Sajal. How much money does each have?", "options": ["A)62, 35", "B)62, 34", "C)34, 62", "D)42, 62", "E)62, 42"], "rationale": "Suppose Arun has $X and Sajal has $Y. then,\n2(x-30)= y+30 => 2x-y =90 \u2026(i)\nand x +10 =3(y-10) => x-3y = - 40 \u2026(ii)\nSolving (i) and (ii), we get x =62 and y =34.\nArun has $62 and Sajal has $34.\nAnswer B.", "correct": "B"}
{"question": "Julie\u2019s yard is rectangular. One side of the yard is 100 feet wide. The total area of the yard is 3,000 square feet. What is the length of the other side of the yard?", "options": ["A)30 feet", "B)20 feet", "C)10 feet", "D)50 feet", "E)60 feet"], "rationale": "Area = length x width. Divide area by width to find the missing side.\n3000 \u00f7100 = 30\nThe other side is 30 feet.\nCorrect answer A", "correct": "A"}
{"question": "The greatest common factor of two positive integers is 11. The least common multiple of these two integers is 7700. If one of the integers is 350, what is the other?", "options": ["A)242", "B)308", "C)352", "D)412", "E)456"], "rationale": "GCF*LCM = product of 2 numbers\n11*7700 = product of 2 numbers\nother number = 11*7700/350 = 242\nAnswer is A", "correct": "A"}
{"question": "A square piece of cloth is trimmed by 4 feet on one edge to form a rectangular piece, which is then cut diagonally in half to create two triangles. If the area of each of triangle is 70 square feet, what was the perimeter (in feet) of the original piece of square cloth?", "options": ["A)56", "B)58", "C)60", "D)62", "E)64"], "rationale": "Let x be the length of one side of the original square.\nThe area of the rectangle is x(x-4)=140.\nx=14.\nThe perimeter of the square was 4*14=56 feet.\nThe answer is A.", "correct": "A"}
{"question": "The length of the ribbon was originally 30 cm. It was reduced in the ratio 5 : 3. What is its length now?", "options": ["A)18", "B)30", "C)6", "D)15", "E)12"], "rationale": "Length of ribbon originally = 30 cm\nLet the original length be 5x and reduced length be 3x.\nBut 5x = 30 cm\nx = 30/5 cm = 6 cm\nTherefore, reduced length = 3 cm\n= 3 \u00d7 6 cm = 18 cm\nAnswer:A", "correct": "A"}
{"question": "M = abc is a three digit number and N = cba, if M > N and M - N + 396c = 990. Then how many values of M are more than 300.", "options": ["A)20", "B)30", "C)40", "D)200", "E)None"], "rationale": "From the given data,\nabc \u2013 cba + 396c = 990\n100a + 10b + c \u2013 (100c + 10b + a) + 396c = 990\n99a \u2013 99c + 396c = 990\nObserve that each term is divisible by 99. So on dividing the above expression by 99, we get\na \u2013 c + 4c = 10\na + 3c = 10\nFor c = 1, a = 7\nc = 2, a = 4\nc = 3, a = 1\n'b' can take any value from 0 to 9\nWe have to find the value of M more than 300. So minimum value of 'a' should be 4.\nSo total possibilities are 402, 412, ...., 492 = 10 values\n701, 711, ....., 791 = 10 values\nSo total values = 20.\nCorrect option: A", "correct": "A"}
{"question": "there are more than 501 students in a school such that 20% of them exactly took physics and 28% of them exactly took math. What could be the least possible no of students in the school?", "options": ["A)550", "B)570", "C)600", "D)700", "E)none of these"], "rationale": "20% means 1/5 and 28% means 7/25,taking the lcm of the denominators 5 and 25 we get 25,the least multiple of 25 which is greater than 501 is 525. So, answer is none\nANSWER:E", "correct": "E"}
{"question": "If Raj was one-third as old as Rahim 5 years back and Raj is 17 years old now, How old is Rahim now?", "options": ["A)37", "B)41", "C)40", "D)42", "E)43"], "rationale": "Raj\u2019s age today = 17 decades,\nHence, 5 decades back, he must be 12 years old.\nRahim must be 36 years old, Because (3\u00d712).\n5 years back Rahim must be 41 years old today. Because (36+5).", "correct": "B"}
{"question": "A cow is tethered in the middle of a field with a 14 feet long rope. If the cow grazes 10 sq.ft. per day, then approximately what time will be taken by the cow to graze the whole field?", "options": ["A)51 days", "B)61 days", "C)71 days", "D)81 days", "E)91 days"], "rationale": "Area of the field grazed = [22/7*14*14]sq.ft. = 616 sq.ft.\nNumber of days taken to graze the field = 616/10 days\n=> 61 days\nANSWER:B", "correct": "B"}
{"question": "A book was sold for Rs 27.50 with a profit of 10%. If it were sold for Rs. 25.75, then would have been percentage of profit and loss ?", "options": ["A)2% Profit", "B)3% Profit", "C)2% Loss", "D)3% Loss", "E)4% Loss"], "rationale": "S.P.=(100+gain%100\u2217C.P)\nSo, C.P. = (100/110\u221725.75)\nWhen S.P. = 25.75 then\nProfit=25.75\u221225=Re.0.75\nProfit%=0.75/25\u2217100=3%\nAnswer is B", "correct": "B"}
{"question": "In how many ways can a teacher in a kindergarten school arrange a group of 3 children (Susan, Tim and Zen) on 3 identical chairs in a straight line so that Susan is on the left of Tim?", "options": ["A)7", "B)3", "C)2", "D)1", "E)6"], "rationale": "Total ways in which 3 children can be arranged on 3 chairs = 3*2*1 = 6\nBut in half cases Susan will be left of Tim and in other half of cases Tim will be on left of Susan\ni.e. Desired cases in which Susan is on the left of Tim = (1/2)*6 = 3\nB", "correct": "B"}
{"question": "The telephone bill of a certain establishment is party fixed and partly varies as the number of calls consumed. When in a certain month 540 calls made the bill is Rs.1800. In another month 620 calls are consumed then the bill becomes Rs.2040. In another month 500 units are consumed due to more\nholidays. The bill for that month would be :", "options": ["A)Rs.1560", "B)Rs.1680", "C)Rs.1840", "D)Rs.1950", "E)Rs.1690"], "rationale": "Let the fixed amount be Rs. X and the cost of each unit be Rs. Y.\nThen, 540y + x = 1800 \u2026. And 620y + x = 2040\nOn subtracting (i) from (ii), we get 80y = 240 -> y = 3\nPutting y = 3 in (i) we get :\n540 * 3 + x = 1800 x = (1800-1620) = 180\n. : Fixed charges = Rs.180, Charge per unit = Rs.3.\nTotal charges for consuming 500 units = 180 +(500*3) = Rs.1680\nAnswer:B", "correct": "B"}
{"question": "Two balls A and B rotate along a circular track. Ball A makes 2 full rotations in 26 minutes. Ball B makes 5 full rotation in 35 minutes. If they start rotating now from the same point, when will they be at the same starting point again?", "options": ["A)1 hour and 31 minutes", "B)2 hour and 31 minutes", "C)3 hour and 31 minutes", "D)4 hour and 31 minutes", "E)5 hour and 31 minutes"], "rationale": "If ball A makes 2 rotations in 26 minutes, it makes 1 rotation in 13 minutes. If ball B makes 5 rotations in 35 minutes, it makes 1 rotation in 7 minutes.\nThe two balls start rotating now and makes several rotations before they are at the SAME starting points. Ball A would have done a WHOLE number X of rotations and ball B would have done a WHOLE number Y of rotations. Also they would have rotated during the same period of time T. Hence\nT = 13 X = 7 Y\nHence 13 X = 7 Y\nSolve the above for X\nX = 7 Y / 13\nWe want the time when they are FIRST at the same starting point. Therefore X and Y are the smallest whole numbers of the equation X = 7 Y / 13. The smallest value of Y that gives X as a whole number is 13. Hence\nX = 7 (13) / 13 = 7\nTime T is given by\nT = 13 X = 13 * 7 = 91 minutes = 1 hour and 31 minutes\ncorrect answer A", "correct": "A"}
{"question": "A bookshelf contains 45 books, 30 of which are hardcover and 20 of which are fiction. What is the maximum number of books that are both hardcover and fiction?", "options": ["A)10", "B)15", "C)18", "D)20", "E)30"], "rationale": "Total Books = 45\nHard Cover = 30\nNon hardcover = 15\nFiction = 20\nNon-Fiction = 25\nMaximum number of Hardcover fiction will be 20( Assuming All the Fiction Books are Hard Cover )\nHence, the correct answer will be (D)", "correct": "D"}
{"question": "A newspaper costs $4 on Sunday and $1 the rest of the days of the week. If a hotel orders twice as many papers on Sunday as it does the rest of the days of the week and pays $210 per week for newspapers, how many newspapers does it buy on Monday?", "options": ["A)15", "B)30", "C)45", "D)60", "E)75"], "rationale": "Number of paper bought on monday = x\n# of paper bought on sunday = 2x\nTotal cost = 210 = 6*x(rest of the day cost)+8*x (sunday cost)\n14x = 210\nx = 15\nAns A", "correct": "A"}
{"question": "A number of friends decided to go on a picnic and planned to spend Rs. 96 on eatables. Four of them, however, did not turn up. As a consequence, the remaining ones had to contribute Rs. 4 extra, each. The number of those who attended the picnic was", "options": ["A)8", "B)12", "C)16", "D)24", "E)25"], "rationale": "Let the number of persons be x. Then,\n96/x-4-96/x=4 => x=12\nSo, required number =x-4=8.\nAnswer is A", "correct": "A"}
{"question": "A wire in the shape of rectangle of length 27 cm and breadth 17 cm is rebent to form a square. What will be the measure of each side?", "options": ["A)9", "B)11", "C)22", "D)25", "E)31"], "rationale": "Perimeter of rectangle = 2 (27 + 17) cm\n= 88cm\nPerimeter of square of side x cm = 4x\nTherefore, perimeter of rectangle = Perimeter of Square\n88 cm = 4x\nx = 22\nTherefore, each side of square = 22 cm\nANSWER : OPTION C", "correct": "C"}
{"question": "A man divides Rs 8600 among 5 sons, 4 daughters and 2 nephews. If each daughter receives four times as much as each nephew, and each son receives five as much as each nephew. How much does each daughter receive ?", "options": ["A)Rs 400", "B)Rs 500", "C)Rs 600", "D)Rs 700", "E)Rs 800"], "rationale": "If each nephew got Rs x, then\n2x+16x+25x = 8600\nx= 200\nEach daughter got 4*200 = Rs 800\nANSWER:E", "correct": "E"}
{"question": "Silu and Meenu were walking on the road.\nSilu said, \"I weigh 51 Kgs. How much do you weigh?\"\nMeenu replied that she wouldn't reveal her weight directly as she is overweight.\nBut she said, \"I weigh 29 Kgs plus half of my weight. \"How much does Meenu weigh?", "options": ["A)12", "B)28", "C)27", "D)58", "E)91"], "rationale": "It is given that Meenu weighs 29 Kgs plus half of her own weight.\nIt means that 29 Kgs is the other half. So she weighs 58 Kgs.\nSolving mathematically, let's assume that her weight is A Kgs.\nA = 29 + A/2\n2 \u00d7 A = 58 + A\nA = 58 Kgs.\nAnswer:D", "correct": "D"}
{"question": "Roy was suffering from severe headaches. He went to see his doctor and the doctor gave him 5 tablets asking him to take one tablet every 15 minutes.\nHow much time will it take Roy to consume all the 5 tablets?", "options": ["A)45 Min", "B)75 Min", "C)90 Min", "D)120 Min", "E)60 Min"], "rationale": "Tablet 1 will be taken in 0 min.\nTablet 2 will be taken in 15 min.\nTablet 3 will be taken in 30 min.\nTablet 4 will be taken in 45 min.\nTablet 5 will be taken in 60 min.", "correct": "E"}
{"question": "In a bag of red and green sweets, the ratio of red sweets to green sweets is 3:4. If the bag contains 120 green sweets, how many red sweets are there?", "options": ["A)90", "B)80", "C)95", "D)100", "E)85"], "rationale": "Let x = red sweets\nWrite the items in the ratio as a fraction.\nred/green=3/4=x/120\n3 \u00d7 120 = 4 \u00d7 x\n360 = 4x\nx=360/4=90\nAnswer:A", "correct": "A"}
{"question": "A club consists of members whose ages are in A.P. The common difference being 3 months. If the youngest member of the club is just 7 years old and the sum of the ages of all the members is 250, then number of members in the club are :", "options": ["A)18", "B)20", "C)25", "D)26", "E)27"], "rationale": "Let, n be the number of members in the club.Then,\n250 =(n x [2\u00d77+(n+1)\u00d73/12]) / 2.\nn =25.\nHence, the number of members in the club is 25.\nAnswer : C", "correct": "C"}
{"question": "M men agree to purchase a gift for Rs. D. If 3 men drop out how much more will each have to contribute towards the purchase of the gift?", "options": ["A)D/(M-3)", "B)MD/3", "C)M/(D-3)", "D)3D/(M2-3M)", "E)None of these"], "rationale": "Initial contribution = D/m\nAfter 3 men drop out, then the contribution = D/M-3\nthe extra amount to pay = (D/m-3)-D/m\n=D(m-m+3)/)(m^2-3*m)\n= 3D/(m^2-3*m)\nANSWER:D", "correct": "D"}
{"question": "At what price should the Karan mark a sewing machine that costs him Rs. 1200/- so that even after offering a 20% discount, he makes 20% profit?", "options": ["A)1,879", "B)1,875", "C)1,876", "D)1,872", "E)1,800"], "rationale": "Cost of a sewing machine = Rs. 1200/-\nBy giving 20% discount on the marked price of a sewing machine, the cost price is :\n100/80 * 1200 = Rs. 1500/- By making a profit of 20% on the cost price of a sewing machine, the marked price of the sewing machine is:\n120/100 \u00c3\u2014 1500 = Rs. 1,800/-\nANSWER: 3", "correct": "E"}
{"question": "Train \u2018A\u2019 leaves Mumbai Central for Lucknow at 11 am, running at the speed of 40 kmph. Train \u2018B\u2019 leaves Mumbai Central for Lucknow by the same route at 2 pm on the same day, running at the speed of 72 kmph. At what time will the two trains meet each other?", "options": ["A)12 am on the next day", "B)5 am on the next day", "C)5 pm on the next day", "D)2 pm on the next day", "E)None of these"], "rationale": "Distance covered by train A before the train B leaves\nMumbai Central = 40 \u00d7 3 = 120 km\nTime taken to cross each other = 120\u204412 = 10 hours\nRequired time = 2pm + 10 = 12 am on the next day\nAnswer A", "correct": "A"}
{"question": "Mark told John \"If you give me half your money I will have Rs.75. John said, \"if you give me one third of your money, I will have Rs.75/-. How much money did John have ?", "options": ["A)22", "B)60", "C)28", "D)26", "E)18"], "rationale": "Let the money with Mark and John are M and J, respectively.\nNow\nM + J/2 = 75\nM/3 + J = 75\nSolving we get M = 45, and J = 60.\nAnswer:B", "correct": "B"}
{"question": "The number of water lilies on a certain lake doubles every two days. If there is exactly one water lily on the lake, it takes 60 days for the lake to be fully covered with water lilies. In how many days will the lake be fully covered with lilies, if initially there were 64 water lilies on it?", "options": ["A)15", "B)28", "C)30", "D)53", "E)59"], "rationale": "Starting from 1 Water Lilly it takes 60 days.\nIf there are already two present, it can be taken as the first day is over.\nIt will take 59 more days.\nNotice that we are told thatthe number of water lilies on a certain lake doubles every two days, thus if initially there were 64 water lilies instead of one, we can consider that 7 days are over and therefore only 53 days are left.\nAnswer: D.", "correct": "D"}
{"question": "x men working x hours per day can do x units of a work in x days. How much work can be completed by y men working y hours per day in y days?", "options": ["A)x2/y2 units", "B)y3/x2 units", "C)x3/y2 units", "D)y2/x2 units", "E)None of these"], "rationale": "Amount of work completed by 1 man in 1 day, working 1 hours a day = x/x3=1/x2\nAmount of work y men in y days, working y hours a day = y3 \u00d7 (1/x2) = y3/x2 . Answer : Option B", "correct": "B"}
{"question": "ABCDE is a regular pentagon with F at its center. How many different quadrilaterals can be formed by joining 4 of the points A,B,C,D,E and F?", "options": ["A)12", "B)10", "C)5", "D)15", "E)20"], "rationale": "The number of polygons with k sides that can be formed by joining them is nCk\nfor quadrilaterals k=4\nit has 6 sides n=6\n6C4=15\nAnswer is D", "correct": "D"}
{"question": "Points A, B, C, D lie in this order on the circumference of a circle. Minor arc AC is 160\u00b0, and minor arc BD is 150\u00b0. If B bisects minor arc AC, then what is the measure of minor arc AD?", "options": ["A)80\u00b0", "B)130\u00b0", "C)140\u00b0", "D)160\u00b0", "E)220\u00b0"], "rationale": "B bisects minor arc AC means Arc BC is 80 degrees. Now , we have arc BD = 150, therefore CD = 70.\nNow, Arc AC = 160 , CD = 70 => Arc AD = 360-230 = 130\nAnswer B", "correct": "B"}
{"question": "If 75 percent of the employees of a certain company take a winter vacation, 40 percent take a winter and a summer vacation, and 20 percent take neither a winter nor a summer vacation, what Q percent of the employees take a summer vacation but not a winter vacation?", "options": ["A)5%", "B)15%", "C)25%", "D)35%", "E)45%"], "rationale": "Winter = 75\nBoth = 40\nNeither = 20\nWinter + Summer - Both + Neither = 100\n75 + Summer - 40 + 20 = 100\nSummer = 45\nSummer but not winter Q= Summer only = Summer - Both(i.e. summer overlap with winter) = 45 - 40 = 5\nAnswer: A", "correct": "A"}
{"question": "The cross-section of a canal is shaped like a trapezium. If the canal is 10 m wide at the top and 6 m wide at the bottom and the area of cross-section is 640 square meters, the depth of cannel is?", "options": ["A)26", "B)28", "C)21", "D)80", "E)23"], "rationale": "1/2 * d (10 + 6)\n= 640\nd = 80\nAnswer: D", "correct": "D"}
{"question": "During one season, a tennis team won 20 matches and lost 30% of their matches. What was the number of matches that the team lost?", "options": ["A)70", "B)30", "C)3", "D)7", "E)5"], "rationale": "Knowing that the team lost 30 % of their matches, it has won 70 % of their matches\nTotal matches = 20 / (70/ 100) = 14\nHence number of matches that the team lost = 20 x 14/100 = 3=C", "correct": "C"}
{"question": "A point on the edge of a fan blade that is rotating in a plane 10 centimeters from the center of the fan. What is the distance traveled, in centimeters, by this point after 30 seconds when the fan runs at the rate of 300 revolutions per minutes?", "options": ["A)750pi", "B)1500pi", "C)1875pi", "D)3000pi", "E)7500pi"], "rationale": "60 seconds - 300 revolutions\n30 seconds - 150 revolutions\ndistance travelled in 1 revolution = 2*pi*r\ndistance travelled in 150 revolutions = 300*pi*r\n= 3000pi\nAnswer is D.", "correct": "D"}
{"question": "If n is such that 36 \u2264 n \u2264 72, then x = (n2 + 2\u221an(n + 4) + 16) / (n+ 4\u221an+ 4) satisfies", "options": ["A)20 < x < 54", "B)23 < x < 58", "C)25 < x < 64", "D)28 < x < 60", "E)None of these"], "rationale": "36 \u2264 n \u2264 72\nx = (n2 + 2\u221an(n + 4) + 16) / (n+ 4\u221an+ 4)\nPut x = 36,\nx = (362 + 2\u221a36(36 + 4) + 16) / (36+ 4\u221a36+ 4)\ni.e which is least value for n = 28.\nAnswer : D", "correct": "D"}
{"question": "At its maximum speed, a space shuttle can travel 700m high in 40 seconds. It will also take 5 seconds to pass a point. What then is the length of the space shuttle?", "options": ["A)50 m", "B)75 m", "C)100 m", "D)125 m", "E)150 m"], "rationale": "Let the length of the space shuttle be x metres and its speed be y m/sec. Then, x / y = 1 \u21d2 y = x / 5\n\u2234 (x + 700) / 40 = x / 5 \u21d4 x = 100 m. Answer C", "correct": "C"}
{"question": "A starts a business with Rs.40,000. After 2 months, B joined him with Rs.60,000. C joined them after some more time with Rs.120,000. At the end of the year, out of a total profit of Rs.375,000, C gets Rs.150,000 as his share. How many months after B joined the business, did C join?", "options": ["A)2 months", "B)4 months", "C)23 months", "D)24 months", "E)84 months"], "rationale": "Assume that C was there in the business for x months\nA:B:C = 40000*12 : 60000*10 : 120000*x\n= 40*12 : 60*10 : 120x = 40 : 5*10 : 10x\n=8 : 10 : 2x\n= 4 : 5 : x\nC's share = 375000*x/(9+x) = 150000\n=> 375x/(9+x) = 150\n=> 15x = 6(9+x)\n=> 5x = 18 + 2x\n=> 3x = 18\n=> x = 18/3 = 6\nIt means C was there in the business for 6 months. Given that B joined the business\nafter 2 months. Hence C joined after 4 months after B joined\nAnswer is B", "correct": "B"}
{"question": "A paper is in a square form whose one side is 20 cm. Two semi circles are drawn on its opposites as diameters. If these semi circles are cut down what is the area of the remaining paper?", "options": ["A)8.75", "B)8.79", "C)8.75", "D)8.71", "E)8.72"], "rationale": "(5 * 3.5)/2 = 8.75\nAnswer:C", "correct": "C"}
{"question": "An athlete runs M miles in 4 hours, then rides a bike N miles in the same number of hours. Which of the following represents the average speed, in miles per hour, for these two activities combined?", "options": ["A)M + N / 8", "B)2M + N / 8", "C)M + N / 4", "D)M + 3N / 8", "E)M + N / 5"], "rationale": "M + N / 8\nformular for avg speed is total distance / total time\nTime spent running = 4 and the time spent biking = 4\ntotal time is 4 + 4 = 8\nTotal distance is M+ N\nThus A", "correct": "A"}
{"question": "8 man work for 6 days to complete a work. How many men are required to complete same work in 1/2 day.", "options": ["A)93 men", "B)94 men", "C)95 men", "D)96 men", "E)97 men"], "rationale": "To complete a work for 6 days, 8 men are required.\nFor completing a work in 1 day = 6*8\n= 48 men\nFor completing a work in half a day (1/2) = 48*2\n= 96 men\nANSWER:D", "correct": "D"}
{"question": "64 boys and 40 girls form a group for social work. During their membership drive, the same number of boys and girls joined the group. How many members does the group have now, if the ratio of boys to girls is 4:3?", "options": ["A)277", "B)288", "C)200", "D)277", "E)168"], "rationale": "Let us say x boys and x girls joined the group.\n(64 + x)/(40 + x) = 4/3\n192 + 3x = 160 + 4x => x = 32\nNumber of members in the group = 64 + x + 40 + x\n= 104 + 2x = 168.\nAnswer:E", "correct": "E"}
{"question": "A cyclist travels at 12 miles per hour. How many minutes will it take to travel 48 miles?", "options": ["A)1", "B)240", "C)30", "D)60", "E)120"], "rationale": "At 12 miles per hour, to cover 48 miles the cyclist will need 4 hours or 240 minutes.\nAnswer: B.", "correct": "B"}
{"question": "Kevin drove from A to B at a constant speed of 70 mph. Once he reached B, he turned right around with pause, and returned to A at a constant speed of 90 mph. Exactly 3 hours before the end of his trip, he was still approaching B, only 70 miles away from it. What is the distance between A and B?", "options": ["A)180", "B)90", "C)270", "D)360", "E)None of the above"], "rationale": "In the last 70 miles of his approach to B, Kevin was traveling at 70 mph, so he traveled that distance in 1 hr, or 60 minutes. That means, when he arrived at B, 60 minutes had elapsed, and he took (3 hr) \u2013 (1 hr) = 2 hr to drive the distance D at 90 mph.\nD = RT = (90 mph)[ (2 hr] = 180 mi\nAnswer = (A)", "correct": "A"}
{"question": "30 is subtracted from a number, it is reduced to its one third. What is the value of 50% of that number?", "options": ["A)22.5", "B)84", "C)21", "D)24", "E)25"], "rationale": "2/3 x = 30 => x = 45\n45 * 1/2 = 22.5\nANSWER:A", "correct": "A"}
{"question": "If a man rows at the rate of 4 kmph in still water and his rate against the current is 2 kmph, then the man's rate along the current is:", "options": ["A)15 kmph", "B)6 kmph", "C)12 kmph", "D)14 kmph", "E)6 kmph"], "rationale": "The speed of the current is 4-2=2 kmph. Thus, if the man navigates along the current his speed is 6kmph. Answer: E", "correct": "E"}
{"question": "The sum of the digits of a three digit number is 17, and the sum of the squares of its digits is 109. If we subtract 495 from the number, we shall get a number consisting of the same digits written in the reverse order. Find the number.", "options": ["A)368", "B)377", "C)288", "D)997", "E)112"], "rationale": "Sum of the squares should be equal to 109. Only Options B and D satisfying. When we subtract 495, only 863 becomes 368\nAnswer:A", "correct": "A"}
{"question": "X and Y are two alloys which were made by mixing zinc and copper in the ratio 6:9 and 7:11, respectively. If 40 grams of alloy X and 60 grams of alloy Y are melted and mixed to form alloy Z, what is the ratio of zinc and copper in the alloy Z ?", "options": ["A)69:91", "B)59:91", "C)59:90", "D)59:91", "E)69:101"], "rationale": "The ratio of zinc and copper in mixture 1 is 6/9 and in mixture 2 is 7/11.\n40 grams of mixture 1 contains 6*40/15=16 grams of zinc and 24 grams of copper\n60 grams of mixture 2 contains 7*60/18=77/3 grams of zinc and 110/3 grams of copper\nThus, ratio =(16+77/3)/(24+110/3) =59/91\nANSWER:B", "correct": "B"}
{"question": "The nefarious bandit Hoopsmot decides to go in with his criminal partner Smolapon to purchase a number of senators. Hoopsmot contributes $16,000 to their bribery pool, and Smolapon contributes just $4,000. Their total allows them to influence 30 senators. How many senators of these can be considered Hoopsmot's?", "options": ["A)18", "B)20", "C)22", "D)24", "E)26"], "rationale": "A = 16000\nB = 4000\nA share 16 parts & B share 4 parts\nTotal 20 parts -----> 30\n----> 1 part -------> 1.5\nA share = 16 parts -----> 24\nD", "correct": "D"}
{"question": "The difference between the squares of two numbers is 256000 and the sum of the numbers is 1000. The numbers are", "options": ["A)600, 400", "B)628, 372", "C)640, 360", "D)None of these", "E)Cannot be determined"], "rationale": "Let the numbers be x and y.\nThen, x^2 - y^2 = 256000 and x + y = 1000.\nOn dividing we get : x - y = 256.\n\u2039=\u203aSolving x + y = 1000 and x - y = 256,\n\u2039=\u203awe get : x = 628 and y = 372.\nAnswer B", "correct": "B"}
{"question": "An astronaut weighing 211 pounds on Earth would weigh 182 pounds on Venus. The weight of the astronaut on Venus would be approximately what percent of the astronaut\u2019s weight on Earth?", "options": ["A)50%", "B)60%", "C)70%", "D)86%", "E)90%"], "rationale": "Weight of astronaut on Earth = 211 pounds\nWeight of astronaut on Venus = 182 pounds\nWeight of astronaut on Venus as a percentage of Weight of astronaut on Earth = (182/211)*100 = 86%\nAnswer D", "correct": "D"}
{"question": "A man walks at 5 kmph for 6 hrs and at 4 kmph for 12 hrs. His average speed is", "options": ["A)4 1/3 km/h", "B)7 2/3 km/h", "C)9 \u00bd km/h", "D)8 km/h", "E)81 km/h"], "rationale": "Avg speed = total distance/total time\n= 5*6 + 4*12 / 18\n=4 1/3 km/h", "correct": "A"}
{"question": "[(272 - 32) (124 + 176)] / (17 x 15 - 15) = ?", "options": ["A)0", "B)2.25", "C)300", "D)400", "E)None of these"], "rationale": "Given expression = [(272 - 32) (124 + 176)] / (17 x 15 - 15)\n= (240 x 300 ) / 240\n= 300\nCorrect Option: C", "correct": "C"}
{"question": "Everyone in the family earns money each month. If the total income of a family per month is $9000 and the median income is $3000, how many members are there in the family?", "options": ["A)2", "B)3", "C)4", "D)5", "E)6"], "rationale": "There must be more than two members.\nIf there are four members, then the middle two average $3000 for a total of $6000, and the highest earner must earn at least $3000 which puts the total at $9000 minimum. The lowest earner pushes the total past $9000 so there can not be four family members.\nThere must be three family members.\nThe answer is B.", "correct": "B"}
{"question": "The bus fare of one adult is Rs. 140 from Ranchi to Patna and bus fare of a child is half the fare of one adult between the same places. What is the total bus fare of 4 adults and 3 children between same places?", "options": ["A)Rs. 666", "B)Rs. 670", "C)Rs. 700", "D)Rs. 570", "E)Rs. 770"], "rationale": "Fare for Adult = Rs. 140.\nFare of Child = Half of the Adult = Rs. 70.\nSo,\nTotal fare = 4 *140 + 3 *70 = 560 +210 = Rs. 770.\nANSWER : E", "correct": "E"}
{"question": "An organization decided to raise Rs. 6 lakh by collecting equal contribution from each of its employees. If each of them had contributed Rs. 60 extra, the contribution would have been Rs. 6.24 lakh. How many employees are there in that organization?", "options": ["A)300", "B)200", "C)400", "D)100", "E)500"], "rationale": "Required number of employees = (624000 - 600000)/60=24000/60=400\nAnswer is C.", "correct": "C"}
{"question": "If there are 5,000 voters out of which 20% are not eligible to vote and there are two candidates contesting. The winning candidate won by 15% of the votes. What is the total number of votes he got ?", "options": ["A)3267", "B)2678", "C)2797", "D)2300", "E)2781"], "rationale": "Number of voters eligible for voting = 5000 \u00d7 0.8 = 4000\nNumber of extra votes gotten by the winning candidate = 4000 \u00d7 0.15 = 600\nLet the number of votes won by winning candidate = x.\n\u21d2 x \u2013 (4000 \u2013 x) = 600\n\u21d2 x = 2300\nAnswer: D", "correct": "D"}
{"question": "For bringing each copper coin from the bottom of a river, a coin-diver gets 20 cents, and for each brass coin she gets 25 cents. If after one dive, she got $3.40. What is the minimum number of copper coins that she brought?", "options": ["A)4", "B)3", "C)2", "D)1", "E)0"], "rationale": "Let's subtract $0.20 until we find a multiple of $0.25.\n$3.40 - $0.20*2 = $3.00, which is a multiple of $0.25.\nThe answer is C.", "correct": "C"}
{"question": "Ram and Krishna start from A and B, respectively, at the same time and travel towards each other at constant speeds of 20m/s and 40m/s, respectively, along the same route. Ram meets Krishna at point C on the road after 10 seconds. Find the total distance between A to B.", "options": ["A)700 meters", "B)1000 meters", "C)700 kilometers", "D)555 meters", "E)600 meters"], "rationale": "Vr=20m/s, Vk=40m/s\ndistance A-C = 20*10=200m\ndistance B-C = 40*10=400m\nTherefore, distance A-C = 200+400=600m.\noption E", "correct": "E"}
{"question": "Car \u2018X\u2019 covers a distance of 320 kms in 8 hours and car \u2018Y\u2019 covers a distance of 415 kms in 5 hrs. What is the difference in the speed of the two cars?", "options": ["A)42kms/hr", "B)41km/hr", "C)43kms/hr", "D)45kms/hr", "E)None of these"], "rationale": "The speed of Car \u2019X\u2019=320kms/8hr=40kms/hr\nThe speed of car \u2019Y\u2019=415kms/5hr=83kms/hr\nthe difference is 43km/hr\nANSWER:C", "correct": "C"}
{"question": "Winson runs from his home to his school at an average speed of 10 miles/hr, and then walks home along the same route at an average speed of 5 miles/hr. If the whole journey took one hour, how many miles is his home from his school?", "options": ["A)9", "B)6", "C)4", "D)3", "E)2"], "rationale": "Suppose x is the distance then\ngoing time + coming time = total time = 1 hour\nx/10 + x/5 = 1\nx = 1.5=2 miles\nAnswer E.", "correct": "E"}
{"question": "A sporting goods store carries only yellow and white golf balls. At the beginning of the day it had 600 golf balls in stock, and by the end of the day it had sold 80% of its inventory of golf balls. If the store sold an equal number of yellow and white golf balls, and in doing so sold all of its white golf balls, how many yellow golf balls did the store have to begin the day?", "options": ["A)80", "B)120", "C)240", "D)320", "E)360"], "rationale": "Since the store sold an equal number of white and yellow balls, 80%/2 = 40% of the inventory at the start of the day was white balls. Then 60% of the inventory consisted of yellow balls.\n0.6(600) = 360\nThe answer is E.", "correct": "E"}
{"question": "A flagstaff 17.5 metre high casts a shadow of length 40.25 metre. The height of building, which casts a shadow of length 28.75 metre under similar conditions will be :", "options": ["A)12 metre", "B)12.5 metre", "C)13.5 metre", "D)14 metre", "E)15 metre"], "rationale": "Less shadow, Less Height (Direct Proportion)\nSo, let height of building be x metre\nthen,\n40.25:17.5::28.75:x\n=>x=17.5\u221728.75/ 40.25\n=>x=12.5\nOption B", "correct": "B"}
{"question": "Two cars are travelling from the same starting point in the same direction, having started their commute at the same time. The first car travels at a steady rate of 55 mph, while the second travels at a steady rate of 52 mph. How much time will pass before the cars are 15 miles away from each other?", "options": ["A)3 hours", "B)5 hours", "C)6 hours", "D)4 hours", "E)7 hours"], "rationale": "Relative Speed: 55-52=3 mph\nDistance:15 miles\nTime: distance/speed=15/3= 5 hours\nCorrect answer is B", "correct": "B"}
{"question": "The events A and B are independent. The probability that event A occurs is 0.6, and the probability that at least one of the events A or B occurs is 0.96. What is the probability that event B occurs?", "options": ["A)0.5", "B)0.6", "C)0.7", "D)0.8", "E)0.9"], "rationale": "Let x be the probability that B does not occur.\nP(A and B do not occur) = 1 - 0.96 = 0.04\n0.4x = 0.04\nx=0.1\nP(B occurs) = 1 - x = 0.9\nThe answer is E.", "correct": "E"}
{"question": "The ratio of the volumes of a cube to that of the sphere which will fit inside the cube is?", "options": ["A)2: \u03c0", "B)7:2", "C)8:2", "D)6: \u03c0", "E)8:3"], "rationale": "a3 : a3/8 * 4/3 \u03c0 => 6: \u03c0\nAnswer: Option D", "correct": "D"}
{"question": "My wall contains 8 red colour ties, 13 violet colour ties,10 blue colour ties, 5 pink colour ties, 4 green colour ties. If electricity is gone and I want at least two ties of same colour then how many ties I should take out from my rack?", "options": ["A)2", "B)3", "C)4", "D)5", "E)6"], "rationale": "5 ties will get you one of different colored ties in the worst case. Thus, one more tie and you will have at least one pair. Thus, 6 is the correct answer.\nANSWER:E", "correct": "E"}
{"question": "Find 25/12*5", "options": ["A)2.5498", "B)0.4167", "C)3.3987", "D)8.5497", "E)5.6312"], "rationale": "Answer=25/12*5\n=25/60=0.4167\nOption B is correct", "correct": "B"}
{"question": "The value of log2 4 is:", "options": ["A)2", "B)4", "C)6", "D)8", "E)12"], "rationale": "Let log2 4 = n.\nlog2 4 = 2.\nAnswer: Option A", "correct": "A"}
{"question": "Calculate the percentage gain of a merchant who purchased 90 kg of oranges for Rs. 450 and sold the whole lot at the rate of Rs. 7.50 per kg.", "options": ["A)50 %", "B)60 %", "C)55 %", "D)70 %", "E)58%"], "rationale": "C.P. of 1 kg = 450/90 = Rs. 5\nS.P. of 1 kg = Rs. 7.50\nGain = 7.50-5 = 2.50\nGain % = 2.50/5 * 100 = 50%. Answer: A", "correct": "A"}
{"question": "A train M leaves City A at 5 am and reaches City B at 9am. Another train N leaves City B at 7am and reaches City A at 1030am. At what time do the 2 trains cross one another?", "options": ["A)1 hr 23 min", "B)1 hr 15 min", "C)1 hr 8 min", "D)56 min", "E)55 min"], "rationale": "Let the distance between the cities be x\nThey meet after y hrs after 7am\nM covers x in 4hrs\nN covers x in 3 1/2 i.e 7/2 hrs\nspeed of M =x/4\nspeed of N = 2x/7\nDistance covered by M in y+2 hrs + Distance covered by N in\ny hrs is x\nx/4 (y+2) +2x/7(y)=x\ny=14/15hr or 56 min\nAnswer : D.", "correct": "D"}
{"question": "Janice bikes at 10 miles per hour, while Jennie bikes at 20. How long until they have collectively biked 1 mile?", "options": ["A)1 minute", "B)2 minutes", "C)3 minutes", "D)4 minutes", "E)5 minutes"], "rationale": "Janice's speed = 1/6 miles per minute\nJennie's speed = 1/3 miles per minute\nJanice + Jennie's speed= (1/6 + 1/3) = 1/2 miles per minute\nBoth together will finish the mile in 2 minutes\ncorrect option is B", "correct": "B"}
{"question": "In an exam, a candidate secured 504 marks of the maximum mark of M. If the maximum mark M is converted into 800 marks, he would have secured 420 marks. What is the value of M?", "options": ["A)278", "B)2890", "C)270", "D)2702", "E)960"], "rationale": "504/M = 420/800\n(504 * 800) / 420 = M\nM = 960\nAnswer:E", "correct": "E"}
{"question": "If Jill needed to buy 10 bottles of soda for a party in which 8 people attended, how many bottles of soda will she need to buy for a party in which 12 people are attending?", "options": ["A)6", "B)8", "C)10", "D)12", "E)14"], "rationale": "We can set up a proportion to solve:\n10 bottles / 8 people = x bottles / 12 people.\nCross-multiply to solve a proportion:\n(10)(12) = (8)(x)\n120 = 8x\n10 = x\nAnswer :C.", "correct": "C"}
{"question": "Two ants are standing side-by-side. One ant, which is 4 inches tall, casts a shadow that is 10 inches long. The other ant is 6 inches tall. Compute, in inches, the length of the shadow that the taller ant casts.", "options": ["A)36", "B)28", "C)42", "D)15", "E)20"], "rationale": "The ratio of shadow to height is constant, so if x is the length of the shadow, then\n4/10 = 6/x and x = 15 .\ncorrect answer D", "correct": "D"}
{"question": "The height of a room to its semi-perimeter is 2:5. It costs Rs.260 to paper the walls of the room with paper 50cm wide at Rs.2 per meter allowing an area of 15 sq.m for doors and windows. The height of the room is:", "options": ["A)2.6m", "B)3.9m", "C)4m", "D)4.2m", "E)4.4m"], "rationale": "Let, height= 2x metres & (length+ breadth)= 5x metres.\nLength of paper= (260/2)m= 130m.\nTherefore, area of paper= (130*50/100)= 65m2\nArea of 4 walls= (65+15)=80m2\n2(length+breadth)*height=80.\nTherefore, 2*5x*2x=80 or x2=4 or x=2\nTherefore, height of the room= 4m\nANSWER:C", "correct": "C"}
{"question": "The sum of k consecutive integers is 51. If the least integer is -50, then k =", "options": ["A)40", "B)62", "C)82", "D)92", "E)102"], "rationale": "The difference is consistent with each integers , therefore the series can be A.P.\nSum of A.P. = A + (N-1) D\nA=First term\nD=Difference between each integer\nN=number of terms\nSum = A + (N - 1 ) D\n51= -50 + N - 1\nN = 102\nAnswer = E", "correct": "E"}
{"question": "In a survey of students, each student selected from a list of 10 songs the 2 songs that the student liked best. If each song was selected 5 times, how many students were surveyed?", "options": ["A)96", "B)48", "C)32", "D)25", "E)18"], "rationale": "Each out of 10 songs was selected 5 times --> the total number of selections = 10*5 = 50.\nEach student selected 2 songs --> the total number of students = 50/2 = 25.\nAnswer: D.", "correct": "D"}
{"question": "If one of the roots of the quadratic equation x^2 + mx + 22 = 0 is 1.5, then what is the value of m?", "options": ["A)-23.5", "B)-17.5", "C)-10.5", "D)-16.2", "E)Cannot be determined"], "rationale": "Here x=1.5 must satisfy the equation\n=> 1.5^2 + 1.5m + 22 = 0\n=> m=-16.2\nANSWER:D", "correct": "D"}
{"question": "At an election meeting 10 speakers are to address the meeting. The only protocol to be observed is that whenever they speak the pm should speak before the mp and the mp should speak before the mla. In how many ways can the meeting be held?", "options": ["A)10!/3", "B)10!/6", "C)10!/2", "D)10!/4", "E)10!/5"], "rationale": "10 speakers can be arranged in 10! ways. Protocol to be observed only one possibility from 3! is appropriate. So, total number of ways=10!/3!=10!/6\nANS:B", "correct": "B"}
{"question": "Anna is able to buy 5 more articles for $300 after the price of each article decreased by 15%. What is the new selling price of each article?", "options": ["A)$8", "B)$10", "C)$13.6", "D)$22.9", "E)$40"], "rationale": "p = old price.\nn = the number of items for $300 for p.\npn = (0.85p)(n + 5) --> n = 0.85(n + 5) --> n = 17.\nNew price = 300/(n + 5) = 13.6.\nAnswer: C.", "correct": "C"}
{"question": "In a row of children Neha is 12th from left end and Radha is 6th from right end. When Radha is shifted to left by 2 places and Neha is shifted to right by 2 places there 6 children between Radha and Neha. How many children are there in the row?", "options": ["A)23", "B)27", "C)26", "D)28", "E)29"], "rationale": "After moving 2 positions to the right Neha is 14 positions from the left, and after moving 2 positions to the left, Radha is on the 8th position from the right. If there are 6 children between them, the total number of children is  14+6+8 = 28\nANSWER:D", "correct": "D"}
{"question": "10kg of a mixture contains 30% sand and 70% clay. In order to make the mixture contain equal quantities of clay and sand how much of the mixture is to be removed and replaced with pure sand?", "options": ["A)10/7", "B)20/7", "C)30/7", "D)40/7", "E)50/7"], "rationale": "The mixture contains 3kg sand and 7 kg clay.\nFor the mixture to be in equal quantities, there should be 2 kg of clay removed.\nClay and sand are in the ratio 7:3\nSo part of sand to be removed = 2*3/7 = 6/7\nSo total mixture to be removed = 2 + 6/7 = 20/7\nANSWER:B", "correct": "B"}
{"question": "A man spends 70% of his income. If his income increases by 20%, then what will be his new expenditure?", "options": ["A)58.3%", "B)62.5%", "C)63.5%", "D)64.5%", "E)65.5%"], "rationale": "Let Rs 100 be the income\nExpenditure=Rs70\nIncreased income=Rs120\nExpenditure in amount is same.\nSo, expenditure % =70/120 *100=58.3%\nANSWER:A", "correct": "A"}
{"question": "What is the greatest number of identical bouquets that can be made out of 28 white and 98 red tulips if no flowers are to be left out? (Two bouquets are identical whenever the number of red tulips in the two bouquets is equal and the number of white tulips in the two bouquets is equal.)", "options": ["A)4", "B)7", "C)10", "D)14", "E)21"], "rationale": "The greatest common divisor of 28 and 98 is 14.\nWe can make 14 identical bouquets with 2 white tulips and 7 red tulips in each bouquet.\nThe answer is D.", "correct": "D"}
{"question": "Sharon works for 5 hours to earn enough tips to buy an ice cream cake, while Karen works for 4. After how many hours will they be able to buy the cake together?", "options": ["A)1 hour", "B)2 hours", "C)3 hours", "D)4 hours", "E)5 hours"], "rationale": "Sharon's earnings = 1/5 cake per hour\nKaren's earnings = 1/4 cake per hour\nSharon + Karen's earnings= 9/20\nThey will be able to buy the cake in just over 2 hours\ncorrect option is C", "correct": "C"}
{"question": "If x<0, y>0, and |x^3| > |y^2|, which of the following must be true?", "options": ["A)x > y", "B)y^2 > x^2", "C)-x^3 < y^2", "D)\u2013x < y", "E)x < \u2013y"], "rationale": "Let\u2019s go through each answer choice: (A) can never be true, since no negative is greater than a positive. (B) doesn\u2019t have to be true \u2013 consider what would happen if x = -2 and y = 1. (C) can never be true, as x^3 must be negative, and y^2 must be positive. (D) can never be true, since if x < 0, -x is the same thing as |x|, and |x| > y. (E) can be manipulated by multiplying both sides by -1, which gives us \u2013x > y. Remember that x < 0, so \u2013x = |x|, and y is positive, so |y| = y. Thus \u2013x^3 > y^2 is the same statement as |x^3| > |y^2|, and (B) must be true.", "correct": "B"}
{"question": "Printer A and Printer B can each print 1\u20442 page per second. How long will it take both printers working together to print 100 pages?", "options": ["A)25 seconds", "B)50 seconds", "C)100 seconds", "D)200 seconds", "E)400 seconds"], "rationale": "Total work = Printer A + Printer B = 2 Printer A\n100= 2 * 1/2 * T => T=100 seconds\nAnswer: C", "correct": "C"}
{"question": "Two ants are moving from their farms towards each other. Ant A is moving at a speed of 9 cm per hour and ant B is moving at a speed of 6 cm per hour. If the farms are 75 cm away from each other, what will be the distance (in cm) that ant A travels until meeting ant B?", "options": ["A)45", "B)48", "C)51", "D)54", "E)57"], "rationale": "The two ants move a total of 15 cm per hour.\nThe time it takes until they meet is 75/15=5 hours.\nIn that time, the distance that ant A travels is 5*9=45 cm.\nThe answer is A.", "correct": "A"}
{"question": "Roberts has a property worth of $1023.65. But in a record his property worth is written as greatest positive even integer less than or equal to his property worth and it is divisible by 100. Find the difference between actual property and recorded property worth?", "options": ["A)23.65", "B)1000", "C)35.62", "D)2.65", "E)1023.65"], "rationale": "Since Robert property worth is written as greatest positive even integer less than or equal to his property worth and it is divisible by 100 then it is =1000 (greatest positive even integer less than or equal to his property worth and it is divisible by 100 is 1000).\nHence the difference = 1023.65 - 1000 = 23.65\nAnswer: A.", "correct": "A"}
{"question": "A man spend 810 in buying trouser at Rs 70 each and shirt at 30 each. What will be the ratio of trouser and shirt when the maximum number of trouser is purchased?", "options": ["A)9 Trousers", "B)8 Trousers", "C)10 Trousers", "D)7 Trousers", "E)11 Trousers"], "rationale": "Lets assume S as price of shirt and T as price of trousers, we have bellow equation:\n70 T + 30 S = 810\nSimplifying we get : 7T + 3S = 81\nT = ( 81 - 3*S )/7\nWe need to find the least value of S which will make (81 - 3*S) divisible by 7\nSimplifying by taking 3 as common factor 3*(27-S) / 7\nLooking at the above equation its not difficult to find out least value of S is 6 so that 27- 3S becomes divisible by S\nHence, T = (81-3*S)/7 = (81-3*6)/7 = 63/7 = 9\nANSWER:A", "correct": "A"}
{"question": "If a subscription for 15 issues of a magazine costs $42.00 and represents a saving of 25 percent of the cover prices, what is the cover price per issue?", "options": ["A)$7.73", "B)$6.73", "C)$5.73", "D)$4.73", "E)$3.73"], "rationale": "Let subscription per magazine = x\n15x = 42\n=> x= 2.8\nLet cover price per magazine = c\nSince there is a 25% saving on cover prices\n0.75c=x\n=> 0.75c = 2.8\n=>c= 3.73\nAnswer E", "correct": "E"}
{"question": "Christopher and Jonathan were having bets. They decide that a coin will be flipped twenty times and each time it turns heads, Christopher will give $2 to Jonathan and each time it turns out to be Tails, Jonathan will give 3$ to Christopher. After flipping for twenty times none of the both won or lost any amount.\nHow many times did the coin landed on Heads ?", "options": ["A)10", "B)23", "C)16", "D)18", "E)12"], "rationale": "The amount won and lost by both is equal.\nThus 2x = 3(20-x) --- x in the number of times heads came\nX = 12", "correct": "E"}
{"question": "Allen starts from X, goes to Y. At the same time Bob starts from Y and goes towards X. Once Allen reaches Y he changes his direction and returns to X. Once Bob reaches X, he changes his direction and returns to Y. Throughout Allen travels at 54 kmph and Bob travels at 78kmph. By the time they meet for the second time, Bob covers 48 km more than Allen. Find the distance between X and Y.", "options": ["A)144km", "B)72 km", "C)126km", "D)84 km", "E)48km"], "rationale": "Total distance 126km\nin an hour both Allen and Bob covered 126km\nthat is 54+78=132\nthey meet for the first time Bob covered more KM than Allen. 78-54=24.\nso when they meet for the second time Bob covered 24*2= 48 more km (ANSWER E)", "correct": "E"}
{"question": "Tires of a certain brand, when purchased new, last for four years. A customer can choose to purchase the new tires at a cost of $180 per tire or can have his current tires repaired at a cost of $40 per tire, a repair that will make the current tires last for one additional year. The average cost per year of the new tires is what percent greater than the cost of repairing the current tires?", "options": ["A)8%", "B)10%", "C)12.5%", "D)16.7%", "E)25%"], "rationale": "Average cost of new tire = $45/tire\ncost of repairing the current tire = $40/tire\nnew tire is $5 more per tire.\ni e. 5/40=1/8=12.5%\nANSWER:C", "correct": "C"}
{"question": "A rope 20 meters long is cut into two pieces. If the length of one piece of rope is 3 meters shorter than the length of the other, what is the length, in meters, of the longer piece of rope?", "options": ["A)7.5", "B)8.9", "C)9.9", "D)11.5", "E)11.7"], "rationale": "Length of the rope = 20 meters.\nAssume length of longer piece = x meters.\nLength of shorter piece = x - 3\nWe know that x + x - 3 = 20\n2x = 23\nLength of the longer piece = x = 11.5 meters\nCorrect Option: D", "correct": "D"}
{"question": "Jerry purchased a 1-year $5,000 bond that paid an annual interest rate of 12% compounded every six months. How much interest had this bond accrued at maturity?", "options": ["A)$5102", "B)$618", "C)$216", "D)$202", "E)$200"], "rationale": "A=P(1+r/q)nq .Here q is no of times interest is compounded in a yr so it is = 2. Amount comes out to be 5618 .Hence interest is 5618-5000=618. >>B", "correct": "B"}
{"question": "Decipher the following multiplication table:\nM A D\nB E\n-------------\nM A D\nR A E\n-------------\nA M I D", "options": ["A)9 2 0 0", "B)9 2 0 9", "C)9 2 0 1", "D)9 2 0 7", "E)9 2 2 2"], "rationale": "It is clear that E = 1 as MAD\u00d7E=MAD\nFrom the hundred's line, M + A = 10 + M or 1 + M + A = 10 + M\nAs A = 10 not possible, A = 9\nSo I = 0.\nand From the thousand's line R + 1 = A. So R = 8.\nM 9 D\nB 1\n-------------\nM 9 D\n8 9 1\n-------------\n9 M 0 D\n-------------\nAs B\u00d7D = 1, B and D takes 3, 7 in some order.\nIf B = 7 and D = 3, then M93\u00d77 = _51 is not satisfied. So B = 3 and D = 7.\n2 9 7\n3 1\n-------------\n2 9 7\n8 9 1\n-------------\n9 2 0 7\n-------------\nAnswer:D", "correct": "D"}
{"question": "Sachin was twice as old as Ajay 10 years back. How old is Ajay today if Sachin will be 40 years old in 10 years", "options": ["A)18", "B)25", "C)15", "D)20", "E)21"], "rationale": "Explanation:\nSachin's age today = 30 years.\nSachin's age 10 years back = 20 years.\nAjay's age 10 years back = 10 years.\nAjay's age today = 20 years\nAnswer: Option D", "correct": "D"}
{"question": "What will be the cost of gardening 1-metre \u2013 broad boundary around a rectangular plot having perimeter of 340 metres at the rate of 10 per square metre?", "options": ["A)3400", "B)1700", "C)3440", "D)Cannot be determined", "E)None of these"], "rationale": "Let l and b be the length and breadth of rectangular plot respectively.\n\u2234 According to the question,we have\n2(l + b) = 340 \u21d2 l + b = 170\nNow, (l + 2) and (b + 2) be the length and breadth of plot with boundary.\n\u2234 Required area = (l + 2) (b + 2) \u2013 lb\n= lb + 2l + 2b + 4 \u2013 lb\n= 2(l + b) + 4 = 344\n\u2234 Required cost = 344 \u00d7 10 = 3440\nAnswer C", "correct": "C"}
{"question": "Last year, 34 percent of Ace Book Company's sales revenue came from the sale of novels. Of the remaining revenue, 1/3 was from the sale of biographies. The company's revenue from the sale of novels was approximately, how many times its revenue from the sale of biographies?", "options": ["A)1.3", "B)1.5", "C)2.1", "D)2.5", "E)3.1"], "rationale": "Percentage of revenue from novels = 34%\nRemaining revenue = 66%\nSale of biographies = 1/3 of 66% = 22%\nSale of novels / sale of biographies\n= 34/22\napprox 1.5\nAnswer B", "correct": "B"}
{"question": "A bee bypasses 0.05% of flowers it flies by because it can sense they don't have any nectar in them. How many flowers will the bee fly by to bypass 8 flowers?", "options": ["A)2000", "B)4000", "C)8000", "D)16000", "E)32000"], "rationale": "Let the number of flowers to be flown by be x.\nThen, .05% of x=8\n(5/100)*(1/100)*x=8\nx=16000\nAnswer is D", "correct": "D"}
{"question": "Fernando purchased a university meal plan that allows him to have a total of 3 lunches and 3 dinners per week. If the cafeteria is closed on weekends and Fernando always goes home for a dinner on Friday nights, how many options does he have to allocate his meals?", "options": ["A)5C3*4C3", "B)5C4*4C2", "C)5C2*4C4", "D)5C6*4C5", "E)4C3*4C3"], "rationale": "He can allocate his 3 free lunches on any 3 days from 5 (excluding weekends), so in 5C3 ways.\nHe can allocate his 3 free dinners on any 3 days from 4 (excluding weekends and Friday), so in 4C3 ways.\nTotal = 5C3*4C3 ways\nANS:A", "correct": "A"}
{"question": "What should come in place of the question mark(?) in each of the following questions ?\na2 - b2/(a + b)2 (?)=(a - b)2", "options": ["A)(a + b)(a - b)", "B)(a - b)2", "C)(a + b)2", "D)a3 + b3", "E)None of these"], "rationale": "(a - b)2 x (a + b)2 / a2 - b2 = (a - b)2 x (a + b)2 / (a + b)(a - b) = (a + b) (a - b)\nAnswer : Option A", "correct": "A"}
{"question": "A number is as much greater than 36 as is less than 86. Find the Number.", "options": ["A)60", "B)56", "C)51", "D)61", "E)41"], "rationale": "Let the number be x. Then, X-36 = 86-X\n2X = 86+36 = 122, x = 61.\nThe answer is option D) 61.", "correct": "D"}
{"question": "A certain phone manufacturer ships its products in crates. A crate consists of p pallets, and each pallet holds 1250 phones. If the manufacturer ships 4 crates, how many phones are shipped?", "options": ["A)1000p", "B)1500p", "C)2000p", "D)2500p", "E)30000"], "rationale": "1 pallet has 1250 phones, so p pallets hold 1250p phones\n1 crate has 1250p phones, so 4 will have 1250p * 3 = 2500p", "correct": "D"}
{"question": "A can construct a wall in 40 min and B can construct the wall in 45 min. How many hours is needed to contruct a wall if both the person working together.", "options": ["A)20 min", "B)22 min", "C)23 min", "D)21 min", "E)20 min"], "rationale": "A's one minute work = 1/40\nB's one minute work = 1/45\n(A+B)'s one minute work = 1/40 + 1/45 = 85/40*45 = 17/360\nso, (A+B)will do work together in 360/17 min = 21 3/17 minutes\n21 min approximately\nANSWER:D", "correct": "D"}
{"question": "An express electric train takes exact three seconds to enter tunnel which is 1 mile long.\nIf train is traveling at 120 mile an hour, how long will it take to pass completely through the tunnel ?", "options": ["A)43 seconds", "B)39 seconds", "C)20 seconds", "D)33 seconds", "E)55 seconds"], "rationale": "The train takes 30 seconds to travel 1 mile, plus 3 seconds for the complete train to pass any point, making a total of 33 seconds.", "correct": "D"}
{"question": "A merchant receives an invoice for a motor boat for $20 000 with terms 4/30, n/100. What is the highest simple interest rate at which he can afford to borrow money in order to take advantage of the discount?", "options": ["A)247.67", "B)237.67", "C)227.67", "D)215.67", "E)None of these"], "rationale": "Explanation:\nSuppose the merchant will take advantage of the cash discount of 4% of $20 000 = $800 by paying the bill within 30 days from the date of invoice. He needs to borrow $20 000 = $800 = $19 200. He would borrow this money on day 30 and repay it on day 100 (the day the original invoice is due) resulting in a 70-day loan. The interest he should be willing to pay on borrowed money should not exceed the cash discount $800.\nr=I/pt=21.73%\nThe highest simple interest rate at which the merchant can afford to borrow money is 21.73%. This is a break-even rate. If he can borrow money, say at a rate of 15%, he should do so. He would borrow $19 200 for 70 days at 15%. Maturity value of the loan is $19 200(1+0.15(70/365))=$19 752.33\nsavings would be $20 000 \u2212 $19 752.33 = $247.67\nAnswer: A", "correct": "A"}
{"question": "There are ten lime soda bottles on a table in a restaurant. They are to be served among two different groups of customers consisting of 5 members each. How many ways are there to create these 2 groups?", "options": ["A)90", "B)105", "C)126", "D)252", "E)525"], "rationale": "Number of ways to select 5 bottles out of 10= 10!/5!5!= 252\nD is the answer", "correct": "D"}
{"question": "A special cereal mixture contains rice, wheat and corn in the ratio of 2:3:5. If a bag of the mixture contains 3 pounds of rice, how much corn does it contain?", "options": ["A)6.5", "B)7.5", "C)7", "D)6", "E)None of the above"], "rationale": "Let x = amount of corn\nrice/corn=2/5=3/x\n2 \u00d7 x = 3 \u00d7 5\n2x = 15\nx=7.5\nAnswer:B", "correct": "B"}
{"question": "You can purchase one soda and two energy bars for 150 cents, or two sodas and three energy bars for 300\ncents. If the costs of the items do not change, compute the cost in cents of six sodas and seven bars.", "options": ["A)500", "B)600", "C)750", "D)800", "E)900"], "rationale": "The cost of adding one soda and one energy bar is 150 cents. We need to purchase six sodas and seven bars. We know two sodas and three bars costs 300 cents, so to that purchase, we'd need to add four sodas and four bars - or four sets of items. So, we take the initial 300 + (4 * 150) = 300+600 = 900.\nAnswer - E.", "correct": "E"}
{"question": "A pen company produces very fine quality of writing pens. Company knows that on average 10% of the produced pens are always defective so are rejected before packing. Company promises to deliver 7200 pens to its wholesaler at Rs. 10 each. It estimates the overall profit on all the manufactured pens to be 25%. What is the manufactured cost of each pen?", "options": ["A)Rs. 6", "B)Rs. 7.2", "C)Rs. 5.6", "D)Rs. 8", "E)None of these"], "rationale": "The company is able to deliver 90% of the manufactured pens. Means to produce 7200 pens they must have to produce 8000 pens as 10% are defectives. So, let K be the manufacturing price of each pen.\nTotal income (including 25% profit) = 8000 *K *1.25\nThis same income is obtained by selling 90% manufactured pens at Rs. 10 which is equal to 7200 *10.\nThus,\n8000 *K *1.25 = 7200 *10\nK = Rs. 7.2. [90% of 8000 = 7200]\nAnswer: Option B", "correct": "B"}
{"question": "A two digit number exceeds the sum of the digits of that number by 18. If the digit at the unit's place is double the digit in the ten's place, what is the number?", "options": ["A)12", "B)24", "C)42", "D)48", "E)49"], "rationale": "Let the ten's digit be x.\nThen, unit's digit = 2x.\nNumber = 10x + 2x = 12x;\nSum of digits = x + 2x = 3x.\nTherefore,  12x - 3x = 18\n\u2039=\u203a 9x = 18\n\u2039=\u203a x = 2.\nHence, required number = 12x = 24.\nAnswer B", "correct": "B"}
{"question": "Suppose for any real number x, [x] denotes the greatest integer less than or equal to x. Let L(x,y) = [x] + [y] + [x + y] and R(x,y) = [2x] + [2y]. Then it is impossible to find any two positive real numbers x and y for which", "options": ["A)L(x,y) = R(x,y)", "B)L(x,y) \u2260 R(x,y)", "C)L(x,y) < R(x,y)", "D)L(x,y) < R(x,y)", "E)None"], "rationale": "x \u2013 1\u2264 [x] \u2264 x\n2x + 2y - 3 \u2264 L(x,y) \u2264 2x + 2y\n=> a \u2013 3 \u2264 L \u2264 a\n2x + 2y -2 \u2264 L(x,y) \u2264 2x + 2y\n=> a \u2013 2 \u2264 R \u2264 a\nTherefore, L \u2264 R.\nAnswer : D", "correct": "D"}
{"question": "Three bells ring at intervals of 36 seconds, 40 seconds and 48 seconds, respectively. They start ringing together at a particular time. When they will ring together again?", "options": ["A)After 6 minutes", "B)After 12 minutes", "C)After 18 minutes", "D)After 24 minutes", "E)none"], "rationale": "LCM of 36,40,48 is 720\n720/60=12\nANSWER:B", "correct": "B"}
{"question": "An electric pole, 14 metres high, casts a shadow of 10 metres. Find the height of a tree that casts a shadow of 15 metres under similar conditions.", "options": ["A)21", "B)22", "C)20", "D)23", "E)24"], "rationale": "direct proportion x1/y1=x2/y2\n14/10=15/x\n(14/10) 15=x\n21 = x\nAnswer:A", "correct": "A"}
{"question": "At my favorite fruit stand, an orange costs 18 dollars, a pineapple costs 27 dollars, and a grape costs 15 dollars. Using the same logic, can you tell how much a mango costs?", "options": ["A)22 dollars", "B)15 dollars", "C)20 dollars", "D)18 dollars", "E)10 dollars"], "rationale": "He cost is equal to 3 dollars for each letter in the fruits name.", "correct": "B"}
{"question": "In the coordinate plane, a triangle has vertices at (a,0), (b,0), and (x,y). If a>x>b>0>y, which of the following represents the area of that triangle?", "options": ["A)(ay\u2212by)/2", "B)(ab\u2212ay)/2", "C)(by\u2212ay)/2", "D)(ay+by)/x", "E)(a\u2212b)/2y"], "rationale": "We must plug in the three points that satisfy y<0<b<x<a.\nOnly C satisfies the area of a triangle.\nAnswer:\nC. (by\u2212ay)/2", "correct": "C"}
{"question": "A car finishes a journey in 20 hours at the speed of 60 km/hr. If the same distance is to be covered in 10 hours, how much speed does the car gain?", "options": ["A)80 kmph", "B)50 kmph", "C)120 kmph", "D)70 kmph", "E)80 kmph"], "rationale": "20 x 60 = 10 x S2\nS2 = 120 kmph\nAnswer: Option C", "correct": "C"}
{"question": "Three friends Alan, Roger and Peter attempt to answer a question on an exam. Alan randomly guesses the answer, giving him a 1/5 probability of guessing correctly. Roger cheats by looking at the paper of the student in front of him, giving him a 2/3 probability of answering correctly. And Peter dutifully performs the calculations, then marks the answer, giving him a 5/6 probability of a correct answer. What is the probability that the question is answered correctly, but not via cheating?", "options": ["A)1/18", "B)1/9", "C)23/90", "D)5/18", "E)13/45"], "rationale": "Prob(Alan) = 1/5\nProb(Roger) without cheating = 2/3-1 = 1/3\nProb(Peter) = 5/6\nTotal Probability = 1/5*1/3*/5/6 = 1/18\nAnswer is A", "correct": "A"}
{"question": "The difference between simple interest and C.I. at the same rate for Rs.5000 for 2 years in Rs.72. The rate of interest is?", "options": ["A)10%", "B)12%", "C)6%", "D)8%", "E)4%"], "rationale": "5000 = 72(100/R)2\n5 R2 = 720 => R = 12\nAnswer: Option B", "correct": "B"}
{"question": "All 250 files on Sam's hard drive are infected by either a virus or a worm or both. The number of files that are infected only by a worm is 2.5 times the number of files that are infected by both a virus and a worm. If 50% of the files were not infected by a virus, how many of Sam's files were NOT infected by a worm?", "options": ["A)50", "B)70", "C)75", "D)100", "E)125"], "rationale": "n(Total) = 250\nn(only worm) = 125(50% of total)\nn(only worm ) = 2.5 * n(both worm and virus)\nSo,\nn(both worn and virus) = 125/2.5 = 50\nn(Total) = n(only worm) + n(both worm and virus) + n(only virus)\nn(only virus) = 250-125-50 = 75\nHence, the files not infected by worm is n(Only virus) = 75\nANSWER :(Option C)", "correct": "C"}
{"question": "A father wants to divide Rs. 5100 between his two sons, Mohan and Sohan who are 23 and 24 at present. He divides the amount in such a way that if their shares are invested at compound interest at 4% p.a. they will receive equal amount on attaining the age of 26 years. Find Mohan's share.", "options": ["A)2400", "B)2500", "C)2600", "D)2700", "E)None of these"], "rationale": "Let, the amount Mohan and Sohan receive be Rs. m and Rs. n, respectively. The amount that they receive 3 years and 2 years after should be equal.\n\u21d2m(1+4/100)3=n(1+4/100)2\n\u21d2m(1+4/100)=n\n\u21d2m(26/25)=n\n\u21d2m/n=25/26\nTherefore, Rs.5100 must be distribued in the ratio 25 : 26\nSo Mohan's share = 5100\u00d725/(25+26)=2500\nAnswer B", "correct": "B"}
{"question": "What is 60% of 30% of 1400 grams?", "options": ["A)450 gms", "B)100 gms", "C)252 gms", "D)240 gms", "E)None of these"], "rationale": "60/100 * 30/100 * 1400= 252\nAnswer: C", "correct": "C"}
{"question": "A certain liquid passes through a drain at a rate of w/25 gallons every x seconds. At that rate, how many minutes will it take y gallons to pass through the drain?", "options": ["A)y/(1200xy)", "B)20xy/w", "C)5xy/(12w)", "D)w/(3xy)", "E)3y/(wx)"], "rationale": "Time needed for w/25 gallons of liquid to pass through a drain = x seconds\nTime needed for w gallons of liquid to pass through a drain = 25x seconds\nTime needed for y gallons of liquid to pass through a drain = (25x/w)*y = 25xy/w seconds\n= (25xy/w )/60 = 5xy/(12w) mins\nAnswer C", "correct": "C"}
{"question": "A coin made of alloy of aluminum and silver measures 2 x 15 mm (it is 2 mm thick and its diameter is 15 mm). If the weight of the coin is 30 grams and the volume of aluminum in the alloy equals that of silver, what will be the weight of a coin measuring 1 x 30 mm made of pure aluminum if silver is twice as heavy as aluminum?", "options": ["A)36 grams", "B)40 grams", "C)42 grams", "D)48 grams", "E)50 grams"], "rationale": "Coin is basically a cylinder.\nSo volume of coin T= pi r^2 h = pi (7.5)^2 * 2\nCoin=Silver+Aluminum\nNow total volume of coin(T) = volume of silver + volume of aluminum\nAlso, volume of silver(Vs)= volume of aluminum(Va)\nT= Va+Vb\nT=2Va\nVa=T/2= pi (7.5)^2 * 2 /2 = pi (7.5)^2\nSilver is twice as heavy as aluminum.\nLet the weight of aluminum in coin be x\nWeight of Silver = 2x\nTotal weight of coin = 30\nx+2x=30\nx=10\nWeight of Aluminum in coin is 10gm\nWright of Silver in coin is 20gm.\nWeight of Aluminum in coin is 10gm and volume is pi (7.5)^2\nNow new Aluminum coin is made with dimension 1x30mm.\nVolume of this new coin = pi (15)^2*1.\nVolume of pi (7.5)^2 contains weight of 10 gm of aluminum\nVolume of pi (15)^2*1 will contain = 10/ pi(7.5)^ * pi (15)^2 * 1= 40gm\nANSWER:B", "correct": "B"}
{"question": "If 10 is subtracted from 2/3 of a number the result is equal to sum of 40 and 1/3 of the number. Find the number", "options": ["A)100", "B)160", "C)150", "D)210", "E)220"], "rationale": "Let the number be x. Then,\n2x/3 - 10 = x/3 + 40\n=> x/3 = 50 => x = 150\nAnswer: Option C", "correct": "C"}
{"question": "What is the largest integral value of 'k' for which the quadratic equation x2 - 5x + k = 0 will have two real and distinct roots?", "options": ["A)9", "B)7", "C)3", "D)8", "E)12"], "rationale": "Any quadratic equation will have real and distinct roots if the discriminant D > 0\nThe discriminant 'D' of a quadratic equation ax2 + bx + c = 0 is given by b2 - 4ac\nIn this question, the value of D = 52 - 4 * 1 * k\nIf D > 0, then 25 > 4k or k < 6.2.\nTherefore, the highest integral value that k can take is 3.\ncorrect choice is (C)", "correct": "C"}
{"question": "900 + 5 \u00d7 12 = ?", "options": ["A)820", "B)202", "C)420", "D)209", "E)960"], "rationale": "900 + 5 \u00d7 12 = ?\nor, ? = 900 + 60 = 960\nAnswer E", "correct": "E"}
{"question": "Shweta rides at the rate of 10 km per hour but stops for 10 minutes to take rest at the end of every 15 km. How many hours will she take to cover 100 km", "options": ["A)9 hours.", "B)10 hours.", "C)11 hours.", "D)12 hours.", "E)13 hours."], "rationale": "After every 15 km she will take a rest of 10 minutes so after every 90 minutes she will 10 min break.\nshe will 10 hours to cover 90 km distance and 1 hour to cover remaining 10km.\nSo the answer is 11 hours.\nANSWER:C", "correct": "C"}
{"question": "Mr.Sam takes 17 hours to go by train to a certain city and return by car. He loses 4 hours if he goes both ways by train. How long would he have taken if he had traveled by car in both ways?", "options": ["A)22 hrs", "B)18 hrs", "C)16 hrs", "D)20 hrs", "E)13 hrs"], "rationale": "Going one way by train and one way by car, he takes 17 hours.\nGoing both ways by train, he takes 4 hours more => The train takes 4 hours more one way\nTherefore travelling both ways by car, he takes 4 hours less than 17\n=> He takes 17-4 = 13 hours.\nE)", "correct": "E"}
{"question": "Jim filled his dog's bowl with dog food. Starting at 8:00 am, Jim's dog ate exactly once an hour, consuming exactly 1/3 of the dog food remaining in the bowl at each feeding session. Approximately, what percent of the original food was in the dog's bowl right before the dog began to eat at noon of the same day?", "options": ["A)20%", "B)25%", "C)30%", "D)35%", "E)40%"], "rationale": "The amount remaining after each feeding session is 2/3 of what was in the bowl.\nThere were four feeding sessions.\nThe amount remaining just before noon was (2/3)^4 = 16/81, which is about 20%.\nThe answer is A.", "correct": "A"}
{"question": "John conducted a survey about car color. 60% of the people who took the survey were women. Of the men who were surveyed, 75% preferred red cars over green cars. If 10 men liked green cars more than red, how many people took the survey?", "options": ["A)100", "B)120", "C)50", "D)200", "E)80"], "rationale": "Let N be the number of people who took the survey. The number of men M is given by M = N - 60%N.\nThe number of men G who liked green cars more than red cars is given by\nG = M - 25%M\nGiven that G = 10, solve for N\n40 = (N - 60%N) - 25%(N - 60%N)\nN = 100\nCorrect answer is A.", "correct": "A"}
{"question": "How many ways A boy can reach the top of stairs which contain 10 steps, when he can take either one or two steps every time?", "options": ["A)88", "B)89", "C)90", "D)91", "E)92"], "rationale": "case 1:1 1 1 1 1 1 1 1 1 1 > 1!\ncase 2:1 1 1 1 1 1 1 1 2 > 9!/8!\ncase 3:1 1 1 1 1 1 2 2 > 8!/6!*2!\ncase 4:1 1 1 1 2 2 2 > 7!/4!*3!\ncase 5:1 1 2 2 2 2 > 6!/4!*2!\ncase 6:2 2 2 2 2 > 1!\nadd answers of all cases => 1+9+28+35+15+1= 89\nANSWER:B", "correct": "B"}
{"question": "IF one gallon of soft drink is made of 40% orange juice and 60% water, how many additional gallons of orange juice must be mixed in order to make the orange juice 60% of the soft drink?", "options": ["A)0.5", "B)1", "C)1.25", "D)1.5", "E)2"], "rationale": "Let x be the quantity to be added\n(0.4+y) / 1+y = 60/100\n=> 4+10y = 6+6y\n=> y = 2/4 = 0.5\nAnswer is A", "correct": "A"}
{"question": "What is the units digit of 9^3-7?", "options": ["A)1", "B)3", "C)5", "D)2", "E)4"], "rationale": "The unit's digit of 9^3 = 9\n9-7=2\nAnswer D", "correct": "D"}
{"question": "5 horses are in a race. Mr.Jain selects two of horses at random and bets on them. The probability that he selected the winning horse is", "options": ["A)1/5", "B)2/5", "C)3/5", "D)4/5", "E)6/5"], "rationale": "There are 5 horses.\nProbability of winning for each horse = 1/5.\nProbability of winning with 2 selected horses= (1/5)+(1/5)= 2/5.\nAnswer is 2/5.\nANSWER:B", "correct": "B"}
{"question": "On dividing 2272 and 875 by a 3-digit number N, we get the same remainder. The sum of the digits of N is:", "options": ["A)10", "B)11", "C)12", "D)13", "E)14"], "rationale": "(2272 - 875) = 1397, is exactly divisible by N.\nNow, 1397 = 11 * 127\nThe required 3-digit number is 127, the sum of whose digit is 10.\nANSWER:A", "correct": "A"}
{"question": "On a test the passing students had an average of 83, while the failing students had an average\nof 55. If the overall class average was 76, what percent of the class passed?", "options": ["A)44%", "B)66%", "C)68%", "D)72%", "E)75%"], "rationale": "Let p = proportion that passed. Then 83p + 55(1- p) = 76, so p = 21/28 = 75\ncorrect answer E", "correct": "E"}
{"question": "The average wages of a worker during a fortnight comprising of 15 consecutive working days was $90 per day. During the first 7 days, his average wage was $87 per day and the average wage during the last 7 days was $92 per day. What was his wage on the 8th day?", "options": ["A)$83", "B)$92", "C)$90", "D)$97", "E)$104"], "rationale": "The total wage earned during the 15 days that the worker worked = 15 * 90 = $ 1350.\nThe total wage earned during the first 7 days = 7 * 87 = $ 609.\nThe total wage earned during the last 7 days = 7 * 92 = $ 644.\nTotal wage earned during the 15 days = wage during first 7 days + wage on 8th day + wage during the last 7 days.\nOr 1350 = 609 + wage on 8th day + 644\nWage on 8th day = 1350 - 609 - 644 = $ 97.\nAnswer D", "correct": "D"}
{"question": "Two numbers are in the ratio 3:5. If 9 is subtracted from each, the new numbers are in the ratio 12:23. The smaller number is?", "options": ["A)21", "B)33", "C)35", "D)42", "E)58"], "rationale": "Let the numbers be 3x and 5x\n3x-9 / 5x-9 = 12/23\n23(3x-9) = 12(5x-9)\n9x = 99\nx = 11\nThe smaller number is = 3*11 = 33\nAnswer is B", "correct": "B"}
{"question": "If 6 yrs are subtracted from the present age of Ajay and the remainder is divided by 18, then the present age of Rahul is obtained. If Rahul is 2 yrs younger to Denis whose age is 5 yrs, then what is Ajay's present age?", "options": ["A)40", "B)60", "C)70", "D)80", "E)90"], "rationale": "Present age of Denis =5 years\nPresent age of Rahul =5\u22122=3\nLet present age of Ajay =x\nThen, present age of Rahul =x\u2212618\nx\u2212618=3\u21d2x\u22126=3\u00d718=54\u21d2x=54+6=60\nB", "correct": "B"}
{"question": "Anna has 4 books. If she decide to arrange the 4 books in every possible combination and moved just one book every minute, how long would it taken by her ?", "options": ["A)22 minutes", "B)30 minutes", "C)15 minutes", "D)24 minutes", "E)35 minutes"], "rationale": "Number of ways of arranging 4 books = 4 ! = 4 x 3 x 2 x 1 = 24.\nSo, total time taken = 24 minutes\nAnswer: D", "correct": "D"}
{"question": "The manufacturer of tyres is offering a 20% discount on the price of its tubeless tyres. Some retailers are offering additional discounts. If a retailer offers an additional 20% discount, then what is the total discount available at that retailer?", "options": ["A)10%", "B)25%", "C)28%", "D)30%", "E)36%"], "rationale": "Discount = 1-0.8*0.8=1-0.64=0.36=36%\nAnswer choice E", "correct": "E"}
{"question": "A rectangular parking space is marked out by painting three of its sides. If the length of the unpainted side is 9 feet, and the sum of the lengths of the painted sides is 37 feet, then what is the area of the parking space in square feet?", "options": ["A)46", "B)81", "C)126", "D)252", "E)None"], "rationale": "We have l=9 and l+2b=37\nArea = (l x b)\n=(9 x 14) sq.ft\n= 126 sq.ft.\nAnswer C", "correct": "C"}
{"question": "Peter wants to find 10 additional people to form a scavenger hunt team with him. If he has 10 friends who would like to participate, how many choices does he have for forming his team?", "options": ["A)0", "B)1", "C)2", "D)3", "E)4"], "rationale": "Peter has 10 friends out of which he has to select 10 so 10C10= 1 Choice.\nANSWER:B", "correct": "B"}
{"question": "The average weight of 10 oarsmen in a boat is increased by 1.8 kg when one of the crew, who weighs 53 kg is replaced by a new man. Find the weight of the new man.", "options": ["A)71", "B)62", "C)43", "D)67", "E)40"], "rationale": "Total weight increased = (1.8 x 10) kg =18 kg.\nWeight of the new man = (53 + 18) kg =71 kg.\nANSWER A", "correct": "A"}
{"question": "A box contains a certain number of balls, marked successively from 1 to n. If there are 45 different ways that two balls can be selected from the box such that the ball with number 3 marked on it is not selected, then what is the value of n?", "options": ["A)11", "B)10", "C)9", "D)8", "E)7"], "rationale": "(n-1)C2=45\nn-1=10\nn=11\nThe answer is A.", "correct": "A"}
{"question": "If Q, a positive integer, has 5 factors, which of the following must be true about Q?\nI. Q is the square of a prime number.\nII. Q is the fourth power of a prime number.\nIII. Q is the product of two prime numbers.", "options": ["A)I only", "B)III only", "C)II only", "D)I and II only", "E)I and III only"], "rationale": "If Q has 5 factors, we can represent Q = a^4, where a is positive integer more than 1.Let's assume that \"a\" is not a prime number. Let a = kp, where both k and p are positive integers.\nThus, Q = (kp)4=k4\u2217p4(kp)4=k4\u2217p4. Now the number of factors of Q = (4+1)*(4+1) = 25. But as the given condition states that Q has ONLY 5 factors, thus \"a\" can't have any other factor except 1 and itself. Thus, a = prime number.\nStatement I :We can represent Q = (a^2)^2. Thus, we have to prove whether a^2 is a prime number. Take a=2. We can see that it is not a prime number. Thus, this option can't answer a \"MUST be true question\"\nStatement II : Always true as proved above.\nStatement III : Again take a =2. Thus, Q = 64. We don't have this as product of 2 primes.\nThe Answer is, B.", "correct": "B"}
{"question": "If 6x - y = 24 and y = 3x, what is the value of x?", "options": ["A)8", "B)9", "C)10", "D)11", "E)12"], "rationale": "6x - 3x = 24\n3x = 24\nx = 8\nThe answer is A.", "correct": "A"}
{"question": "You can rent DVDs at a local video store for $4.00 per movie without a membership. However, if you purchase a membership for $7.00 per month, you can rent DVDs for $2.00 each. What is the minimum amount of DVDs you would have to rent to make it worth it to purchase the membership?", "options": ["A)1", "B)2", "C)3", "D)4", "E)5"], "rationale": "Let's compare the cost to rent x CDs.\n4x > 2x+7\n2x > 7\nx > 3.5\nThe minimum number of CDs you would need to rent is 4.\nThe answer is D.", "correct": "D"}
{"question": "A bag of cat food weighs 7 pounds and 4 ounces. How much does the bag weigh in ounces?", "options": ["A) 108", "B) 112", "C) 116", "D) 120", "E) 124"], "rationale": "1 pound = 16 ounces.\n7 pounds and 4 ounces = (7 x 16) + 4 = 116 ounces.\nAnswer: C.", "correct": "C"}
{"question": "By himself, Jack can clean a yacht in 12 hours. On a particular day, he happens to finish two-thirds of the work. The remaining portion of the work is done by Jill, whose rate of cleaning is just 5% of what Jack can do. How long does it take Jill to finish the remaining work?", "options": ["A)4", "B)8", "C)22", "D)50", "E)20"], "rationale": "Jack did 2/3 of the work, which is 8 hours. So if Jack would finish the job this would take him 4 extra hours. Jill's rate is 5% of what Jack would do in those 4 hours. That means it would take her ten times as much time as Jack put into the job. 5*4 equals 20, answer E.", "correct": "E"}
{"question": "Professors borrowed Rs. 5000 from the university at simple interest. After 3 years, the university got Rs. 300 on interest. What was the rate of interest per annum?", "options": ["A)2%", "B)8%", "C)5%", "D)10%", "E)None of these"], "rationale": "(100 * 300 )/(5000*3) = 2%\nAnswer : A", "correct": "A"}
{"question": "In a sale, a discount of 20% was available on all the articles. If Vinit purchased an article for Rs.4578 in the sale. What was the actual selling price of the article?", "options": ["A)s.5050", "B)s.5723", "C)s.5040", "D)s.4950", "E)s.4870"], "rationale": "Rs.4578 = 20% of SP\n:. SP = 4578 x 100/80 = Rs.5723\nAnswer: Option B", "correct": "B"}
{"question": "If 27 bottles of soda cost A cents, how much will B bottles cost in dollars?", "options": ["A)AB/2700", "B)27/AB", "C)AB/270", "D)2700/AB", "E)100AB/27"], "rationale": "27 bottles cost A cents or A/100 dollars\n1 bottle will cost = A/100/27 = A/2700\nB bottles in dollars = B*A/2700 = AB/2700\nHence, answer will be A.", "correct": "A"}
{"question": "A bag contains 11 candy bars: three cost 50 cents each, four cost $1 each and four cost $2\neach. How many ways can 3 candy bars be selected from the 11 candy bars so that the total cost is more than $4?", "options": ["A)8", "B)28", "C)46", "D)66", "E)70"], "rationale": "The ways of choosing 3 candy bars with a total cost over $4 include: choose 3 out of 4 (2 dollars each); choose 2 out of 4 (2 dollars each) and 1 from the other 7. So, the total number of ways is C4\n3 + (7 C4\n2 ) = 46. Incidentally, the total number ways of choosing 3 candy bars out of 11 is C11\n3 = 165. So the probability of them costing more than $4 if they are randomly chosen is\nC4\n3 + (7 C4\n2 )\nC11\n3\n=\n46/165\ncorrect answer C", "correct": "C"}
{"question": "At a conference, one team is made up of 4 men and 4 women. Four presenters are chosen to present the team's findings in front of the entire conference. How many different groups of presenters can be chosen from the team if a team cannot be composed of men only or women only? (Two groups of presenters are considered different if at least one presenter is different.)", "options": ["A)120", "B)19", "C)180", "D)420", "E)460"], "rationale": "No of ways = All ways to choose - ways using just men - ways using just women = C(8,4)-C(4,4)-C(4,4) = 21 - 1 - 1 = 19\nAnswer is (B)", "correct": "B"}
{"question": "Exactly 2/5th of the children in a certain class are girls. If there are 100 boys in the class, how many girls are in the class?", "options": ["A)50", "B)100", "C)150", "D)200", "E)70"], "rationale": "This means 2/5 of the class are boys\n2x/5=100\nx=250\ntherefore, girls = 150\nAnswer is C", "correct": "C"}
{"question": "Two numbers are said to be relatively prime if they share no common positive factors other than 1. Set S contains the set of integers from 1 to 1,000, inclusive. What is the probability that a number chosen from S is relatively prime to 1,000?", "options": ["A)5/7", "B)3/5", "C)4/7", "D)2/5", "E)2/7"], "rationale": "We need all numbers between 1 and 1000 that are co-prime.\nBetween 1 to 10 there are 4 : 1,3,7,9\nTake the period of 10's , we have 100 periods of 10's between 1 to 1000\nSo the total number of co-primes = 400\nNow, the simple part ...\nProbability = 400/1000 (i.e picking a co-prime from the first 1000 numbers )\nAns: 2/5 D", "correct": "D"}
{"question": "At the of his one-year investment, Charles received $54,080, including interest and principal from a certain investment. If the investment paid an annual interest of 8 percent compounded semi-annually, which of the following is the amount of money that Charles originally invested?", "options": ["A)$45,000", "B)$50,000", "C)$54,000", "D)$59,000", "E)$62,000"], "rationale": "You are given that a certain investment gave you X $ after 1 year. So the original investment must be <X\nThus you can rule out options D-E as these options will make the original amount > the interest+principle amount\nOption C is very close to the amount after 1st year and 4% will definitely give you >80$ in interests.\nNow you only have 2 options left (A and B)\nPer the question, let x be the original amount invested ---> x(1.04)^2 = 54080 .@NL This clearly shows that answer must be B.\nANSWER:B", "correct": "B"}
{"question": "Joe's age, Joe's sister's age and Joe\u2019s fathers age sums up to 100. When Joe is as old as his father, Joe's sister will be twice as old as now. When Joe is as old as his father then his father is twice as old as when his sister was as old as her father. Find the age of Joe's father?", "options": ["A)45", "B)48", "C)50", "D)55", "E)58"], "rationale": "Joe+Sister+Father=100\nAfter x years joe age is equal to his father\nJoe+x = father\nTherefore, Sister+x = 2 * Sister\n=> Sister=x\nJoe+Sister=Father\nTherefore,\n=> 2*Father = 100\n=> Father= 50\nHence (C) is the correct answer", "correct": "C"}
{"question": "If one third of 3/4 of a number is 21. Then, find the number?", "options": ["A)84", "B)66", "C)28", "D)19", "E)11"], "rationale": "x * 1/3 * 3/4 =21 => x = 84\nAnswer: A", "correct": "A"}
{"question": "If m > 0, y > 0, and x is m percent of 4y, then, in terms of y, m is what percentage of x?", "options": ["A)y/400", "B)4y", "C)50y", "D)2500/y", "E)5000/y"], "rationale": "x = (m/100)*4y\nm = 100x/4y = 25x/y\nm is (2500/y)% of x.\nThe answer is D.", "correct": "D"}
{"question": "A man wants to eat fruit for breakfast and vegetable for dinner. He has 6 different types of fruit and 8 different types of vegetables. He can only eat one type at a time. In how many ways can he eat breakfast and dinner.", "options": ["A)54", "B)24", "C)48", "D)20", "E)36"], "rationale": "Number of choices for fruit=6, number of choices for vegetable=8\nThe total number of combinations =8*6=48\nAnswer C", "correct": "C"}
{"question": "There is a 90% chance that a registered voter in Burghtown voted in the last election. If five registered voters are chosen at random, what is the approximate likelihood that exactly four of them voted in the last election?", "options": ["A)26.2%", "B)32.8%", "C)43.7%", "D)59.0%", "E)65.6%"], "rationale": "The probability that four of five voted is :\nP(1st one voted) X ... X P(4th one voted) X (5th one NOT voted)\n= 0.9 x 0.9 x 0.9 x 0.9 x 0.1\n= 0.81 x 0.81 x 0.1 = 0.6561\nANSWER: E", "correct": "E"}
{"question": "Alice wants to put up fencing around three sides of her rectangular yard and leave one side of 10 meters unfenced. If the yard has an area of 240 square meters, how many meters of fencing does she need?", "options": ["A)58", "B)62", "C)66", "D)70", "E)74"], "rationale": "The sides of the rectangle have a length of 10 and L.\nThe area is 10*L=240 so L=24.\nShe needs fencing with a length of 10+2*24=58 meters.\nThe answer is A.", "correct": "A"}
{"question": "John would make the 3-letter codes with diffenrent 5 vowels and 20 consonants with the condition that the middle letter must be vowel and the first letter and the third letter must be different from each other and are both consonant. How many different codes can be made?", "options": ["A)1,980", "B)2,020", "C)2,100", "D)2,200", "E)1,900"], "rationale": "There should be a vowel in the middle of the 3-letter code, which means that 5 letters can be in the middle. Then, 20 letters can be placed in the first letter and 19 letters can be placed in the last letter as they should be different letters. Thus, 20*5*19=1,900.\nTherefore, the answer is E.", "correct": "E"}
{"question": "What is the sum of three consecutive integers whose product can be expressed as 727+728+729.", "options": ["A)37", "B)38", "C)36", "D)30", "E)39"], "rationale": "The addition of any 3 consecutive numbers is always divisible by 3\nthat rules out options a,b,c\nNow focus on options 30 and 39\nIf we consider\n12, 13, 14 the sum is 39\nAnswer = E", "correct": "E"}
{"question": "The capacity of a tank of dimensions (8 m \u00d7 6 m \u00d7 2.5 m) is", "options": ["A)120 litres", "B)1200 litres", "C)12000 litres", "D)120000 litres", "E)None of these"], "rationale": "Capacity of the bank = Volume of the bank\n= (8x100x6x100x2.5x100/1000) =\nanswer D", "correct": "D"}
{"question": "The population of a city is 5265526. If there are 4169516 adults in the city, how many children are there in the city?", "options": ["A)1095961", "B)1065961", "C)1085961", "D)1097961", "E)1096061"], "rationale": "Population of the city = 5265526\nNumber of adults = 4169516\nNumber of children = 5265526-4169516 = 1096061\nAnswer :E", "correct": "E"}
{"question": "It takes a worker 9 minutes to drive from home to work at an average rate of 20 kilometers per hour. How many minutes will it take the worker to cycle from home to work along the same route at an average rate of 6 kilometers per hour?", "options": ["A)30", "B)32", "C)35", "D)36", "E)40"], "rationale": "distance = time*speed = (9/60)(20) kilometers\ntime to bike = distance/speed = (9*20)/(60*6) = 30/60 hours = 30 minutes.\nThe answer is A.", "correct": "A"}
{"question": "Two friends are eating a jar full of candies. Had P eaten alone, it would have taken him 10 minutes to finish the candies in the jar. Had Q eaten alone, it would have taken her 5 minutes to finish half the jar. Since both of them are eating simultaneously, how many minutes would it take them to empty the jar?", "options": ["A)4", "B)5", "C)6", "D)7", "E)8"], "rationale": "Together they eat 1/10 + 1/10 = 1/5 of the jar per minute.\nThe time to finish the jar is 5 minutes.\nThe answer is B.", "correct": "B"}
{"question": "A grocery sells a bag of ice for $1.25, and makes 20% profit. If it sells 500 bags of ice, how much total profit does it make?", "options": ["A)125", "B)150", "C)225", "D)250", "E)275"], "rationale": "Profit per bag = 1.25 * 0.20 = 0.25\nTotal profit = 500 * 0.25 = 125\nAnswer is A.", "correct": "A"}
