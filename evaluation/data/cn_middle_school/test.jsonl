{"idx": 0, "question": "下列四个数中，最小的数是()\nA.-2  B.0  C. 3  D.  -1/2", "answer": "-2", "choice_answer": "A"}
{"idx": 1, "question": "已知点(-3,2)在反比例函数y=k/x (k≠0)的图象上，则k的值为()\nA.-3  B.3  C.-6  D.6", "answer": "-6", "choice_answer": "C"}
{"idx": 2, "question": "若两个相似三角形的相似比是1:3，则这两个相似三角形的面积比是()\nA.1:3  B.1:4  C.1:6  D.1:9", "answer": "1:9", "choice_answer": "D"}
{"idx": 3, "question": "已知m=√27-√3，则实数m的范围是（）\nA.2<m<3  B.3<m<4  C.4<m<5  D.5<m<6", "answer": "3<m<4", "choice_answer": "B"}
{"idx": 4, "question": "已知整式M:a_n x^n+a_(n-1) x^(n-1)+⋯+a_1 x+a_0，其中n,a_(n-1),⋯,a_0为自然数，a_n为正整数，且n+a_n+a_(n-1)+⋯+a_1+a_0=5．下列说法：①满足条件的整式M中有5个单项式；②不存在任何一个n，使得满足条件的整式M有且只有3个；③满足条件的整式M共有16个．其中正确的个数是（）\nA.0  B.1  C.2  D.3", "answer": "3", "choice_answer": "D"}
{"idx": 5, "question": "重庆是一座魔幻都市，有着丰富的旅游资源．甲、乙两人相约来到重庆旅游，两人分别从A、B、C三个景点中随机选择一个景点游览，甲、乙两人同时选择景点B的概率为", "answer": "1/9", "choice_answer": ""}
{"idx": 6, "question": "计算：(π-3)^0 + (1/2)^(-1)＝", "answer": "3", "choice_answer": ""}
{"idx": 7, "question": "如果一个多边形的每一个外角都是40°，那么这个多边形的边数为", "answer": "9", "choice_answer": ""}
{"idx": 8, "question": "随着经济复苏，某公司近两年的总收入逐年递增．该公司2021年缴税40万元，2023年缴税48.4万元，该公司这两年缴税的年平均增长率是", "answer": "10%", "choice_answer": ""}
{"idx": 9, "question": "若关于x的不等式组\\begin{equation}\n{\\begin{cases}\n{\n (4x-1)/3<x+1 \n 2(x+1)≥-x+a)\n}\\end{cases}\n}\n\\end{equation}至少有2个整数解，且关于y的分式方程(a-1)/(y-1)=2-3/(1-y)的解为非负整数，则所有满足条件的整数a的值之和为", "answer": "16", "choice_answer": ""}
{"idx": 10, "question": "我们规定：若一个正整数A能写成m^2-n，其中m与n都是两位数，且m与n的十位数字相同，个位数字之和为8，则称A为“方减数”，并把A分解成m^2-n的过程，称为“方减分解”．例如：因为602=25^2-23，25与23的十位数字相同，个位数字5与3的和为8，所以602是“方减数”，602分解成602=25^2-23的过程就是“方减分解”．按照这个规定，最小的“方减数”是____．把一个“方减数”A进行“方减分解”，即A=m^2-n，将m放在n的左边组成一个新的四位数B，若B除以19余数为1，且2m+n=k^2（k为整数），则满足条件的正整数A为____", "answer": "82,4564", "choice_answer": ""}
{"idx": 11, "question": "计算：x(x-2y)+(x+y)^2", "answer": "2x^2+y^2", "choice_answer": ""}
{"idx": 12, "question": "计算：(1+1/a)÷(a^2-1)/(a^2+a)", "answer": "\\frac{(a+1)}{(a-1)}", "choice_answer": ""}
{"idx": 13, "question": "为促进新质生产力的发展，某企业决定投入一笔资金对现有甲、乙两类共30条生产线的设备进行更新换代．(1)为鼓励企业进行生产线的设备更新，某市出台了相应的补贴政策．根据相关政策，更新1条甲类生产线的设备可获得3万元的补贴，更新1条乙类生产线的设备可获得2万元的补贴．这样更新完这30条生产线的设备，该企业可获得70万元的补贴．该企业甲、乙两类生产线各有多少条？(2)经测算，购买更新1条甲类生产线的设备比购买更新1条乙类生产线的设备需多投入5万元，用200万元购买更新甲类生产线的设备数量和用180万元购买更新乙类生产线的设备数量相同，那么该企业在获得70万元的补贴后，还需投入多少资金更新生产线的设备？", "answer": "10, 20, 13300000", "choice_answer": ""}
{"idx": 14, "question": "反比例函数y=-10/x的图象一定经过的点是（）\nA.(1,10)  B.(-2,5)  C.(2,5)  D.(2,8)", "answer": "(-2,5)", "choice_answer": "B"}
{"idx": 15, "question": "估计\\sqrt{12}(\\sqrt{2}+\\sqrt{3})的值应在（）\nA.8和9之间  B.9和10之间  C.10和11之间 D.11和12之间", "answer": "10和11之间", "choice_answer": "C"}
{"idx": 16, "question": "计算：|-2|+3^0=", "answer": "3", "choice_answer": ""}
{"idx": 17, "question": "若正多边形的一个外角是45°，则该正多边形的边数是", "answer": "8", "choice_answer": ""}
{"idx": 18, "question": "重庆在低空经济领域实现了新的突破．今年第一季度低空飞行航线安全运行了200架次，预计第三季度低空飞行航线安全运行将达到401架次．设第二、第三两个季度安全运行架次的平均增长率为x，根据题意，可列方程为", "answer": "200(1+x)^2=401", "choice_answer": ""}
{"idx": 19, "question": "若∠A=55°，则∠A的补角为（）\nA.35°  B.45°  C.115°  D.125°", "answer": "125°", "choice_answer": "D"}
{"idx": 20, "question": "因式分解：2x^2-8=", "answer": "2(x+2)(x-2)", "choice_answer": ""}
{"idx": 21, "question": "已知一次函数y=-2x+4，当自变量x>2时，函数y的值可以是____（写出一个合理的值即可）", "answer": "2", "choice_answer": ""}
{"idx": 22, "question": "定义一种新运算*，规定运算法则为：m*n=m^n-mn（m，n均为整数，且m≠0）．例：2*3=2^3-2×3=2，则(-2)*2=____", "answer": "8", "choice_answer": ""}
{"idx": 23, "question": "解不等式组：\\begin{equation}\n{\\begin{cases}\n{2(x-2)<x+3 \\ \n(x+1)/2<2x} \n \\end{cases}\n}\\end{equation}", "answer": "1/3<x<7", "choice_answer": ""}
{"idx": 24, "question": "先化简，再求值：[(2a+b)^2-(2a+b)(2a-b)]÷2b=，其中a=2，b=-1", "answer": "3", "choice_answer": ""}
{"idx": 25, "question": "在一只不透明的布袋中，装有质地、大小均相同的四个小球，小球上分别标有数字1，2，3，4．甲乙两人玩摸球游戏，规则为：两人同时从袋中随机各摸出1个小球，若两球上的数字之和为奇数，则甲胜；若两球上的数字之和为偶数，则乙胜．请用画树状图或列表的方法，求甲获胜的概率", "answer": "7/12", "choice_answer": ""}
{"idx": 26, "question": "下列各数中，是无理数的是（）\nA.π/2  B.1/3  C.\\sqrt[3]{27}  D. 0.13133", "answer": "π/2", "choice_answer": "A"}
{"idx": 27, "question": "据央视财经《经济信息联播》消息：甘肃天水凭借一碗香喷喷的麻辣烫成为最“热辣滚烫”的顶流．2024年3月份，天水市累计接待游客464万人次，旅游综合收入27亿元．将数据“27亿”用科学记数法表示为（）\nA.2.7×10^8  B.  0.27×10^10  C.2.7×10^9  D.27×10^8", "answer": "2.7×10^9", "choice_answer": "C"}
{"idx": 28, "question": "下列各式运算结果为a^5的是（       ）\nA.a^2+a^3  B.a^2a^3  C.(a^2)^3  D.a^10÷a^2", "answer": "a^2a^3", "choice_answer": "B"}
{"idx": 29, "question": "一次函数 y=kx-1(k≠0)，若y随x的增大而减小，则它的图象不经过（       ）\nA. 第一象限  B. 第二象限  C. 第三象限  D. 第四象限", "answer": "第一象限", "choice_answer": "A"}
{"idx": 30, "question": "端午节期间，某商家推出“优惠酬宾”活动，决定每袋粽子降价2元销售．细心的小夏发现，降价后用240元可以比降价前多购买10袋，求：每袋粽子的原价是多少元？设每袋粽子的原价是x元，所得方程正确的是（       ）\nA.240/x-240/(x+2)=10  B.240/x-240/(x-2)=10  C.240/(x-2)-240/x=10  D.240/(x+2)-240/x=10", "answer": "240/(x-2)-240/x=10", "choice_answer": "C"}
{"idx": 31, "question": "因式分解：x^2-1/4=____", "answer": "(x-1/2)(x+1/2)", "choice_answer": ""}
{"idx": 32, "question": "若关于x的一元二次方程x2+2x﹣m=0有两个相等的实数根，则m的值为", "answer": "-1", "choice_answer": ""}
{"idx": 33, "question": "化简：(a+1+1/(a-1))÷(a^2+a)/(a-1)", "answer": "a/(a+1)", "choice_answer": ""}
{"idx": 34, "question": "学习小组在延时课上制作了A，B，C，D四张卡片，四张卡片除图片内容不同外，其他没有区别，放置于暗箱中摇匀。小临从四张卡片中随机抽取一张，抽中C卡片的概率是______", "answer": "1/4", "choice_answer": ""}
{"idx": 35, "question": "学习小组在延时课上制作了A，B，C，D四张卡片，四张卡片除图片内容不同外，其他没有区别，放置于暗箱中摇匀。小夏从四张卡片中随机抽取两张，用列表法或画树状图法求小夏抽取两张卡片内容均为化学变化的概率", "answer": "1/6", "choice_answer": ""}
{"idx": 36, "question": "下列运算正确的是（   ）．\nA.(-m^3 )^2=-m^5 B.m^2n\\dot m=m^3\\cdot n C.  3mn-m=3n D.(m-1)^2=m^2-1", "answer": "m^2n\\cdot m=m^3\\cdot", "choice_answer": "B"}
{"idx": 37, "question": "二十四节气，它基本概括了一年中四季交替的准确时间以及大自然中一些物候等自然现象发生的规律，二十四个节气分别为：春季（立春、雨水、惊蛰、春分、清明、谷雨），夏季（立夏、小满、芒种、夏至、小暑、大暑），秋季（立秋、处暑、白露、秋分、寒露、霜降），冬季（立冬、小雪、大雪、冬至、小寒、大寒），若从二十四个节气中选一个节气，则抽到的节气在夏季的概率为（   ）．\nA.  1/2   B.  1/12   C.  1/6   D.  1/4", "answer": "1/4", "choice_answer": "D"}
{"idx": 38, "question": "已知一元二次方程x^2-3x+m=0一个根为1，则m=______ ", "answer": "2", "choice_answer": ""}
{"idx": 39, "question": "计算: -2 \\cdot \\cos 45^{\\circ}+(\\pi-3.14)^{0}+|1-\\sqrt{2}|+\\left(\\frac{1}{4}\\right)^{-1}.", "answer": "4", "choice_answer": ""}
{"idx": 40, "question": "先化简, 再求值: \\left(1-\\frac{2}{a+1}\\right) \\div \\frac{a^{2}-2 a+1}{a+1}, 其中 a=\\sqrt{2}+1", "answer": "\\frac{1}{a-1}, \\frac{\\sqrt{2}}{2}", "choice_answer": ""}
{"idx": 41, "question": "今年 618各大电商平台促销火热, 线下购物中心也亮出大招, 年中大促进入 “白热化” . 深圳各大购物中心早在5月就开始推出618活动, 进入6月更是持续加码, 如图, 某商场为迎接即将到来的 618优惠节, 采购了若干辆购物车. 如图为某商场叠放的购物车, 右图为购物车叠放在一起的示意图, 若一辆购物车车身长 1 m , 每增加一辆购物车, 车身增加 0.2 m . (1) 若某商场采购了 n 辆购物车, 求车身总长 L 与购物车辆数 n 的表达式. (2) 若该商场用直立电梯从一楼运输该批购物车到二楼, 已知该商场的直立电梯长为2.6 m , 且一次可以运输两列购物车, 求直立电梯一次性最多可以运输多少辆购物车? (3) 若该商场扶手电梯一次性可以运输 24 辆购物车, 若要运输 100 辆购物车, 且最多只能使用电梯 5次, 求: 共有多少种运输方案?", "answer": "(0.8+0.2 n) m, 18, 3", "choice_answer": ""}
{"idx": 42, "question": "计算 -5+3 的结果是 ( ) . A. 2 B. -2 C. 8 D. -8", "answer": "-2", "choice_answer": "B"}
{"idx": 43, "question": "2024年6月6日，嫦娥六号在距离地球约 384000千米外上演 “太空牵手”, 完成月球轨道的交会对接. 数据384000用科学记数法表示为(). A. 3.84 \\times 10^{4} B. 3.84 \\times 10^{5} C. 3.84 \\times 10^{6} D. 38.4 \\times 10^{5}", "answer": "3.84 \\times 10^{5}", "choice_answer": "B"}
{"idx": 44, "question": "下列计算正确的是（）. A. a^{2} \\cdot a^{5}=a^{10} B. a^{8} \\div a^{2}=a^{4} C. -2 a+5 a=7 a D. \\left(a^{2}\\right)^{5}=a^{10}", "answer": "\\left(a^{2}\\right)^{5}=a^{10}3", "choice_answer": "D"}
{"idx": 45, "question": "长江是中华民族的母亲河, 长江流域孕育出藏羌文化、巴蜀文化、荆楚文化、吴越文化等区域文化. 若从上述四种区域文化中随机选一种文化开展专题学习, 则选中 “巴蜀文化” 的概率是 ). A. \\frac{1}{4} B. \\frac{1}{3} C. \\frac{1}{2} D. \\frac{3}{4}", "answer": "\\frac{1}{4}", "choice_answer": "A"}
{"idx": 46, "question": "完全相同的 4 个正方形面积之和是 100 , 则正方形的边长是（）. A. 2 B. 5 C. 10 D. 20", "answer": "5", "choice_answer": "B"}
{"idx": 47, "question": "若点 \\left(0, y_{1}\\right),\\left(1, y_{2}\\right),\\left(2, y_{3}\\right) 都在二次函数 y=x^{2} 的图象上，则（ ）. A. y_{3}>y_{2}>y_{1} B. y_{2}>y_{1}>y_{3} C. y_{1}>y_{3}>y_{2} D. y_{3}>y_{1}>y_{2}", "answer": "y_{3}>y_{2}>y_{1}", "choice_answer": "A"}
{"idx": 48, "question": "方程 \\frac{2}{x-3}=\\frac{3}{x} 的解为 ( ). A. x=3 B. x=-9 C. x=9 D. x=-3", "answer": "x=9", "choice_answer": "C"}
{"idx": 49, "question": "数据 2,3,5,5,4 众数是____", "answer": "5", "choice_answer": ""}
{"idx": 50, "question": "若关于 x 的一元二次方程 x^{2}+2 x+c=0 有两个相等的实数根, 则 c=____", "answer": "1", "choice_answer": ""}
{"idx": 51, "question": "计算: \\frac{a}{a-3}-\\frac{3}{a-3}=", "answer": "1", "choice_answer": ""}
{"idx": 52, "question": "计算: 2^{0} \\times\\left|-\\frac{1}{3}\\right|+\\sqrt{4}-3^{-1}", "answer": "2", "choice_answer": ""}
{"idx": 53, "question": "广东省全力实施 “百县千镇万村高质量发展工程” , 2023年农产品进出口总额居全国首位，其中荔枝鲜果远销欧美．某果商以每吨2 万元的价格收购早熟荔枝, 销往国外. 若按每吨5万元出售, 平均每天可售出 100吨．市场调查反映：如果每吨降价 1 万元，每天销售量相应增加 50吨. 该果商如何定价才能使每天的 “利润” 或 “销售收入”最大？并求出其最大值", "answer": "4.5, 312.5", "choice_answer": ""}
{"idx": 54, "question": "下列各数中, 比-2小的数是（） A. -1 B. -4 C. 4 D. 1", "answer": "-4", "choice_answer": "B"}
{"idx": 55, "question": "定义一种新运算 *, 规定运算法则为: m * n=m^{n}-m n （ \\mathrm{~m}, \\mathrm{n} 均为整数, 且 m \neq 0 ）. 例: 2 * 3=2^{3}-2 \\times 3=2, 则 (-2) * 2=____", "answer": "8", "choice_answer": ""}
{"idx": 56, "question": "甘肃临夏砖雕是一种历史悠久的古建筑装饰艺术，是第一批国家级非物质文化遗产. 如图 1 是一块扇面形的临夏砖雕作品, 它的部分设计图如图2, 其中扇形 O B C 和扇形 O A D 有相同的圆心 O, 且圆心角 \\angle O=100^{\\circ}, 若 O A=120 \\mathrm{~cm}, O B= 60 cm , 则阴影部分的面积是 ____ \\mathrm{cm}^{2}. （结果用 \\pi 表示）", "answer": "3000 \\pi", "choice_answer": ""}
{"idx": 57, "question": "计算: \\sqrt{18}-\\sqrt{12} \\times \\sqrt{\\frac{3}{2}}", "answer": "0", "choice_answer": ""}
{"idx": 58, "question": "解不等式组: \\left\\{\\begin{array}{c}2(x-2)<x+3 \\ \\frac{x+1}{2}<2 x\\end{array}\\right.", "answer": "\\frac{1}{3}<x<7", "choice_answer": ""}
{"idx": 59, "question": "先化简, 再求值: \\left[(2 a+b)^{2}-(2 a+b)(2 a-b)\\right] \\div 2 b, 其中 a=2, b=-1", "answer": "2a+b, 3", "choice_answer": ""}
{"idx": 60, "question": "在一只不透明的布袋中，装有质地、大小均相同的四个小球，小球上分别标有数字 1 ， 2 ， 3 ， 4.甲乙两人玩摸球游戏, 规则为：两人同时从袋中随机各摸出 1 个小球, 若两球上的数字之和为奇数 , 则甲胜; 若两球上的数字之和为偶数, 则乙胜. 求甲获胜的概率.", "answer": "\\frac{7}{12}", "choice_answer": ""}
{"idx": 61, "question": "下列各数中, 是无理数的是（） A. \\frac{\\pi}{2} B. \\frac{1}{3} C. \\sqrt[3]{27} D. 0.13133", "answer": "\\frac{\\pi}{2}", "choice_answer": "A"}
{"idx": 62, "question": "据央视财经《经济信息联播》消息：甘肃天水凭借一碗香喷喷的麻辣䖿成为最 “热辣滚煼” 的顶流. 2024 年3月份, 天水市累计接待游客464万人次，旅游综合收入27亿元．将数据 “ 27 亿” 用科学记数法表示为 A.  2.7 \\times 10^{8} B. 0.27 \\times 10^{10} C. 2.7 \\times 10^{9} D. 27 \\times 10^{8}", "answer": "2.7 \\times 10^{9}", "choice_answer": "C"}
{"idx": 63, "question": "下列各式运算结果为 a^{5} 的是 ( ) A. a^{2}+a^{3} B. a^{2} a^{3} C. \\left(a^{2}\\right)^{3} D. a^{10} \\div a^{2}", "answer": "a^{2} a^{3}", "choice_answer": "B"}
{"idx": 64, "question": "一次函数 y=k x-1(k \neq 0), 若 y 随 x 的增大而减小, 则它的图象不经过 ( ) A. 第一象限 B. 第二象限 C. 第三象限 D. 第四象限", "answer": "第一象限", "choice_answer": "A"}
{"idx": 65, "question": "端午节期间，某商家推出“优惠酬宾” 活动，决定每袋粽子降价 2 元销售. 细心的小夏发现，降价后用240元可以比降价前多购买10袋，求：每袋粽子的原价是多少元？设每袋粽子的原价是 x 元, 所得方程正确的是 A. \\frac{240}{x}-\\frac{240}{x+2}=10 B. \\frac{240}{x}-\\frac{240}{x-2}=10 C. \\frac{240}{x-2}-\\frac{240}{x}=10 D. \\frac{240}{x+2}-\\frac{240}{x}=10", "answer": "\\frac{240}{x-2}-\\frac{240}{x}=10", "choice_answer": "C"}
{"idx": 66, "question": "若关于 x 的一元二次方程 x^{2}+2 x-m=0 有两个相等的实数根, 则 m 的值为 ___", "answer": "-1", "choice_answer": ""}
{"idx": 67, "question": "计算: |-\\sqrt{4}|-\\left(\\frac{1}{3}\\right)^{-1}+2025^{0}.", "answer": "0", "choice_answer": ""}
{"idx": 68, "question": "化简: \\left(a+1+\\frac{1}{a-1}\\right) \\div \\frac{a^{2}+a}{a-1}.", "answer": "\\frac{a}{a+1}", "choice_answer": ""}
{"idx": 69, "question": "解不等式组: \\left\\{\\begin{array}{c}2 x+1 \\geq x+2, \\ 2 x-1<\\frac{1}{2}(x+4)\\end{array}\\right..", "answer": "x\\in[1,2)", "choice_answer": ""}
{"idx": 70, "question": "在平面直角坐标系中, 抛物线 y=-x^{2}+b x+c 与 x 轴交于 A(-1,0), B(3,0) 两点,求拋物线的解析式", "answer": "y=-x^{2}+2x+3", "choice_answer": ""}
{"idx": 71, "question": "下列运算正确的是（） A. a^{7}-a^{3}=a^{4} B. 3 a^{2} \\cdot 2 a^{2}=6 a^{2} C. (-2 a)^{3}=-8 a^{3} D. a^{4} \\div a^{4}=a", "answer": "(-2a)^{3}=-8a^{3}", "choice_answer": "C"}
{"idx": 72, "question": "下列数中, 能使不等式 5 x-1<6 成立的 x的值为 ( ) A. 1 B. 2 C. 3 D. 4", "answer": "1", "choice_answer": "A"}
{"idx": 73, "question": "节能环保已成为人们的共识. 淇淇家计划购买 500 度电，若平均每天用电 x 度，则能使用 y 天．下列说法错误的是（） A. 若 x=5, 则 y=100 B. 若 y=125, 则 x=4 C. 若 x减小,则 y也减小 D. 若 x减小一半, 则 y 增大一倍", "answer": "若x减小,则y也减小", "choice_answer": "C"}
{"idx": 74, "question": "淇淇在计算正数 a 的平方时, 误算成 a 与 2 的积, 求得的答案比正确答案小 1 , 则 a=____ A. 1 B. \\sqrt{2}-1 C. \\sqrt{2}+1 D. 1 或 \\sqrt{2}+1", "answer": "\\sqrt{2}+1", "choice_answer": "C"}
{"idx": 75, "question": "已知 A 为整式, 若计算 \\frac{A}{x y+y^{2}}-\\frac{y}{x^{2}+x y} 的结果为 \\frac{x-y}{x y}, 则 A=(____) A. x B. y C. x+y D. x-y", "answer": "3", "choice_answer": ""}
{"idx": 76, "question": "某校生物小组的 9 名同学各用 100 粒种子做发芽实验, 几天后观察并记录种子的发芽数分别为: 89 ，73，90，86，75，86，89，95，89, 以上数据的众数为 ___ .", "answer": "89", "choice_answer": ""}
{"idx": 77, "question": "已知 \\mathrm{a}, \\mathrm{b}, \\mathrm{n} 均为正整数. (1) 若 n<\\sqrt{10}<n+1, 则 n= ___ ; (2) 若 n-1<\\sqrt{a}< n, n<\\sqrt{b}<n+1, 则满足条件的 a 的个数总比 b 的个数少 ___ 个.", "answer": "3, 2", "choice_answer": ""}
{"idx": 78, "question": "甲、乙、丙三张卡片正面分别写有 a+b, 2 a+b, a-b, 除正面的代数式不同外, 其余均相同. \\begin{table}[] \\begin{tabular}{|c|c|c|c|l|l|l|l|l|l|} \\hline 第一次 & $a+b$   & $a+b$  & $a+b$ & $2a+b$                     & $2a+b$                      & $2a+b$                     & $a-b$                      & $a-b$                       & $a-b$                      \\ \\hline 第二次 & $a+b$   & $2a+b$ & $a-b$ & $a+b$ & $2a+b$ & $a-b$ & $a+b$ & $2a+b$ & $a-b$ \\ \\hline 和   & $2a+2b$ &        & $2a$  &                            &                             &                            &        $2a$                    &                             &                        \\ \\hline \\end{tabular} \\end{table} (1)将三张卡片背面向上并洗匀, 从中随机抽取一张, 当 a=1,b=-2 时, 求取出的卡片上代数式的值为负数的概率; (2)将三张卡片背面向上并洗匀, 从中随机抽取一张, 放回后重新洗匀, 再随机抽取一张. 请在表格中补全两次取出的卡片上代数式之和的所有可能结果（化为最简）, 并求出和为单项式的概率.", "answer": "\\frac13, \\frac{4}{9}", "choice_answer": ""}
{"idx": 79, "question": "拋物线 C_{1}: y=a x^{2}-2 x 过点 (4,0), 顶点为 Q . 抛物线 C_{2}: y=-\\frac{1}{2}(x-t)^{2}+\\frac{1}{2} t^{2}- 2 （其中 t 为常数, 且 t>2 ), 顶点为 P.直接写出 a 的值和点 Q 的坐标.", "answer": "a=\\frac{1}{2}, Q(2,-2)", "choice_answer": ""}
{"idx": 80, "question": "据统计，2023年我国人工智能核心产业规模达5784亿元，数据 “5784亿”用科学记数法表示为 A. 5784 \\times 10^{8} B. 5.784 \\times 10^{10} C. 5.784 \\times 10^{11} D. 0.5784 \\times 10^{12}", "answer": "5.784 \\times 10^{11}", "choice_answer": "C"}
{"idx": 81, "question": "下列不等式中，与 -x>1 组成的不等式组无解的是（） A. x>2 B. x<0 C. x<-2 D. x>-3", "answer": "x>2", "choice_answer": "A"}
{"idx": 82, "question": "计算 (a \\cdot a \\cdots \\cdots \\cdot a)^{a} \\uparrow= 的结果是 ( ) A. a^{5} B. a^{6} C. a^{a+3} D. a^{3 a}", "answer": "a^{3 a}", "choice_answer": "D"}
{"idx": 83, "question": "豫剧是国家级非物质文化遗产, 因其雅俗共赏, 深受大众喜爱．正面印有豫剧经典剧目人物的三张卡片如图所示, 它们除正面外完全相同. 把这三张卡片背面朝上洗匀, 从中随机抽取一张, 放回洗匀后，再从中随机抽取一张，两次抽取的卡片正面相同的概率为___ A. \\frac{1}{9} B. \\frac{1}{6} C. \\frac{1}{5} D. \\frac{1}{3}", "answer": "\\frac{1}{3}", "choice_answer": "D"}
{"idx": 84, "question": "若关于 x 的方程 \\frac{1}{2} x^{2}-x+c=0 有两个相等的实数根, 则 c 的值为___ .", "answer": "0.5", "choice_answer": ""}
{"idx": 85, "question": "计算: \\sqrt{2} \\times \\sqrt{50}-(1-\\sqrt{3})^{0}", "answer": "9", "choice_answer": ""}
{"idx": 86, "question": "从地面坚直向上发射的物体离地面的高度 h(\\mathrm{~m}) 满足关系式 h=-5 t^{2}+v_{0} t, 其中 t(\\mathrm{~s}) 是物体运动的时间, v_{0}(\\mathrm{~m} / s )是物体被发射时的速度. 社团活动时, 科学小组在实验楼前从地面坚直向上发射小球. (1)小球被发射后 ___ s 时离地面的高度最大 (用含 v_{0} 的式子表示).", "answer": "\\frac{v_{0}}{10}", "choice_answer": ""}
{"idx": 87, "question": "从地面坚直向上发射的物体离地面的高度 h(\\mathrm{~m}) 满足关系式 h=-5 t^{2}+v_{0} t, 其中 t(\\mathrm{~s}) 是物体运动的时间, v_{0}(\\mathrm{~m} / s )是物体被发射时的速度. 社团活动时, 科学小组在实验楼前从地面坚直向上发射小球.若小球离地面的最大高度为 20 m , 求小球被发射时的速度.", "answer": "20", "choice_answer": ""}
{"idx": 88, "question": "某校八年级3班承担下周学校升旗任务, 老师从备选的甲、乙、丙、丁四名同学中, 选择两名担任升旗手, 则甲、乙两名同学同时被选中的概率是 A. \\frac{1}{6} B. \\frac{1}{8} C. \\frac{1}{4} D. \\frac{2}{3}", "answer": "\\frac{1}{6}", "choice_answer": "A"}
{"idx": 89, "question": "一种药品原价每盒 48 元, 经过两次降价后每盒 27 元, 两次降价的百分率相同, 则每次降价的百分率为 A. 20 \\% B. 22 \\% C. 25 \\% D. 28 \\%", "answer": "25 \\%", "choice_answer": "C"}
{"idx": 90, "question": "函数 y=\\frac{\\sqrt{x+3}}{x} 中, 自变量 x 的取值范围是 ___", "answer": "x \\geq -3, x \neq 0", "choice_answer": ""}
{"idx": 91, "question": "将抛物线 y=a x^{2}+b x+3 向下平移 5 个单位长度后, 经过点 (-2,4), 则 6 a-3 b- 7= ____ .", "answer": "2", "choice_answer": ""}
{"idx": 92, "question": "已知一组正整数 \\mathrm{a}, 1, \\mathrm{~b}, \\mathrm{~b}, 3 有唯一众数 8 , 中位数是 5 , 则这一组数据的平均数为 ___", "answer": "5", "choice_answer": ""}
{"idx": 93, "question": "若分式方程 \\frac{x}{x-1}=3-\\frac{m x}{1-x} 的解为正整数, 则整数 m 的值为 ___ ", "answer": "-1", "choice_answer": ""}
{"idx": 94, "question": "二次函数 y=\\frac{1}{2} x^{2}+b x+c 的图象与 x 轴交于 \\mathrm{A} 、 \\mathrm{~B} 两点, 与 y 轴交于点 C , 点 A 的坐标为 (-1,0), 点 C 的坐标为 (0,-3).求该二次函数的解析式", "answer": "y=\\frac{1}{2} x^{2}-\\frac{5}{2} x-3", "choice_answer": ""}
{"idx": 95, "question": "牡丹江某县市作为猴头菇生产的 “黄金地带” ，年总产量占全国总产量的 50 \\% 以上，黑龙江省发布的 “九珍十八品” 名录将猴头菇列为首位. 某商店准备在该地购进特级鲜品、特级干品两种猴头菇, 购进鲜品猴头菇3箱、干品猴头菇2箱需 420 元, 购进鲜品猴头菇 4 箱、干品猴头菇 5 箱需 910 元. 请解答下列问题: 特级鲜品猴头菇和特级干品猴头菇每箱的进价各是多少元？", "answer": "40, 150", "choice_answer": ""}
{"idx": 96, "question": "在平面直角坐标系中, 直线 y=x+ b 与 x 轴的正半轴交于点 A , 与 y 轴的负半轴交于点 D , 点 B 在 x 轴的正半轴上, 四边形 A B C D 是平行四边形, 线段 O A 的长是一元二次方程 x^{2}-4 x-12=0 的一个根. 请解答下列问题: 求点 D 的坐标", "answer": "(0,-6)", "choice_answer": ""}
{"idx": 97, "question": "下列实数中，最小的是（） A. \\pi B. -(-2) C. \\sqrt{8} D. |-\\sqrt{3}|", "answer": "|-\\sqrt{3}|", "choice_answer": "D"}
{"idx": 98, "question": "下列计算正确的是 ( ) A. x^{5}-x^{2}=x^{3} B. (x-2)^{2}=x^{2}-4 C. \\left(-3 x^{2}\\right)^{3}=-9 x^{6} D. 3 x^{2} y \\div 3 x y=x", "answer": "3 x^{2} y \\div 3 x y=x", "choice_answer": "D"}
{"idx": 99, "question": "下列说法中正确的是（） A. 1000件产品中只有一件是次品, 从中随机抽取一件, “是次品” 是不可能事件 B. “在一张纸上任意画两个直角三角形, 这两个直角三角形相似” 是随机事件 C. 天气预报明天武汉有雨, “武汉明天下雨” 是必然事件 D. 了解汉江襄阳段的水质情况，适合用全面调查", "answer": "“在一张纸上任意画两个直角三角形, 这两个直角三角形相似” 是随机事件", "choice_answer": "B"}
{"idx": 100, "question": "我国古代数学名著《算法统宗》里有这样一首诗: “我问开店李三公, 众客都来到店中, 一房七客多七客, 一房九客一房空. ”诗中后面两句的意思是: 如果一间客房住7人, 那么有7人无房可住; 如果一间客房住 9 人, 那么就空出一间客房. 若设该店有客房x间, 则可列方程为 ___ , 求出客房数量为 ___ 间.", "answer": "7x+7=9(x-1),8", "choice_answer": ""}
