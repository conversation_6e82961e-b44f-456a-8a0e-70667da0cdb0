import fire
import os
from datasets import load_dataset
from transformers import AutoTokenizer
import logging
import json
import random

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def save_split_as_jsonl(dataset_split, output_file):
    """Save a dataset split as a JSONL file."""
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as f:
        for example in dataset_split:
            json.dump(example, f, ensure_ascii=False)
            f.write('\n')

def select_random_n_examples(dataset_split, n):
    """Select random n examples from the dataset split."""
    total = len(dataset_split)
    if total <= n:
        return dataset_split
    else:
        indices = random.sample(range(total), n)
        selected = [dataset_split[i] for i in indices]
        return selected

def download_and_filter_dataset_jsonl(
    dataset_name: str = "GAIR/LIMO",
    max_length: int = 16384,
    tokenizer_path: str = "Qwen/Qwen2.5-7B-Instruct",
    output_path: str = None,
    n: int = None
):
    """
    Download and filter a dataset to keep only examples under the specified token limit
    and save as JSONL files maintaining subset and split structure.

    Directory structure:
    - dataset_name/
      - subset1/
        - train.jsonl
        - validation.jsonl
        - test.jsonl
      - subset2/
        - train.jsonl
        - ...
      - train.jsonl (if no subsets)

    Args:
        dataset_name: HuggingFace dataset identifier (default: GAIR/LIMO)
        max_length: Maximum token length for filtering (default: 16384)
        tokenizer_path: HuggingFace tokenizer identifier (default: Qwen/Qwen2.5-7B-Instruct)
        output_path: Output directory path (default: dataset name)
        n: Optional number of random examples to select if more than n examples are under max_length
    """

    # Generate default output path if not provided
    if output_path is None:
        output_path = dataset_name.split('/')[-1]

    logger.info(f"Loading dataset: {dataset_name}")
    logger.info(f"Using tokenizer: {tokenizer_path}")
    logger.info(f"Max token length: {max_length}")
    logger.info(f"Output path: {output_path}")
    if n is not None:
        logger.info(f"Random sampling limit: {n} examples per split")

    # Load the dataset
    try:
        dataset = load_dataset(dataset_name)
        logger.info(f"Dataset loaded successfully. Available splits: {list(dataset.keys())}")
    except Exception as e:
        logger.error(f"Failed to load dataset {dataset_name}: {e}")
        return

    # Load the tokenizer
    try:
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
        logger.info(f"Tokenizer loaded successfully")
    except Exception as e:
        logger.error(f"Failed to load tokenizer {tokenizer_path}: {e}")
        return

    def tokenize_and_check_length(example):
        """
        Tokenize the example and add token count information.
        This function matches exactly how the training script processes the data.
        """
        try:
            # Match the training script's data processing exactly
            # Create the conversation format like in make_supervised function
            if 'question' in example and 'solution' in example:
                # This matches lines 123-127 in train.py
                labels = [
                    {"role": "user", "content": example["question"]},
                    {"role": "assistant", "content": example["solution"]},
                ]

                # Apply chat template like in training script
                tokens = tokenizer.apply_chat_template(labels, return_tensors="pt")[0]
                token_count = len(tokens)

            else:
                # Fallback for other data formats
                text_content = ""
                # Common field names for text content
                possible_fields = ['text', 'content', 'instruction', 'input', 'question', 'problem', 'prompt']

                for field in possible_fields:
                    if field in example:
                        text_content = example[field]
                        break

                # If no standard field found, concatenate all string fields
                if not text_content:
                    text_parts = []
                    for key, value in example.items():
                        if isinstance(value, str) and value.strip():
                            text_parts.append(value)
                    text_content = " ".join(text_parts)

                # Tokenize without truncation to get actual length
                tokens = tokenizer(text_content, truncation=False, return_tensors="pt")
                token_count = len(tokens['input_ids'][0])

            example['token_count'] = token_count
            example['within_limit'] = token_count <= max_length

        except Exception as e:
            logger.warning(f"Failed to tokenize example: {e}")
            example['token_count'] = 0
            example['within_limit'] = False

        return example

    def filter_by_length(example):
        """Filter function to keep only examples within token limit."""
        return example['within_limit']

    # Check if dataset has subsets (configurations)
    try:
        # Try to load with all configurations to detect subsets
        dataset_info = load_dataset(dataset_name, streaming=True)
        has_subsets = False
    except:
        has_subsets = False

    # Alternative method: check if dataset has multiple configurations
    try:
        from datasets import get_dataset_config_names
        config_names = get_dataset_config_names(dataset_name)
        has_subsets = len(config_names) > 1
        logger.info(f"Dataset configurations found: {config_names}")
    except:
        config_names = [None]
        has_subsets = False

    total_original = 0
    total_filtered = 0

    if has_subsets:
        # Process each subset (configuration)
        for config_name in config_names:
            logger.info(f"Processing subset: {config_name}")

            # Load dataset with specific configuration
            try:
                subset_dataset = load_dataset(dataset_name, config_name)
                logger.info(f"Subset {config_name} loaded. Available splits: {list(subset_dataset.keys())}")
            except Exception as e:
                logger.error(f"Failed to load subset {config_name}: {e}")
                continue

            # Process each split in the subset
            for split_name, split_data in subset_dataset.items():
                logger.info(f"Processing subset {config_name}, split: {split_name}")
                original_count = len(split_data)
                total_original += original_count

                # Add token count information
                logger.info(f"Tokenizing {original_count} examples...")
                split_with_tokens = split_data.map(
                    tokenize_and_check_length,
                    desc=f"Tokenizing {config_name}/{split_name}"
                )

                # Filter by length
                logger.info(f"Filtering examples by token length...")
                filtered_split = split_with_tokens.filter(
                    filter_by_length,
                    desc=f"Filtering {config_name}/{split_name}"
                )

                # If n is set, select random n examples from filtered
                if n is not None:
                    filtered_split = select_random_n_examples(filtered_split, n)

                filtered_count = len(filtered_split)
                total_filtered += filtered_count

                # Remove the helper columns
                if hasattr(filtered_split, 'remove_columns'):
                    filtered_split = filtered_split.remove_columns(['token_count', 'within_limit'])

                # Save filtered split as JSONL in subset folder
                subset_dir = os.path.join(output_path, config_name)
                split_output_file = os.path.join(subset_dir, f"{split_name}.jsonl")
                save_split_as_jsonl(filtered_split, split_output_file)

                logger.info(f"Saved {config_name}/{split_name} with {filtered_count} examples "
                           f"({filtered_count/original_count*100:.1f}% retained) to {split_output_file}")
    else:
        # Process dataset without subsets
        for split_name, split_data in dataset.items():
            logger.info(f"Processing split: {split_name}")
            original_count = len(split_data)
            total_original += original_count

            # Add token count information
            logger.info(f"Tokenizing {original_count} examples...")
            split_with_tokens = split_data.map(
                tokenize_and_check_length,
                desc=f"Tokenizing {split_name}"
            )

            # Filter by length
            logger.info(f"Filtering examples by token length...")
            filtered_split = split_with_tokens.filter(
                filter_by_length,
                desc=f"Filtering {split_name}"
            )

            # If n is set, select random n examples from filtered
            if n is not None:
                filtered_split = select_random_n_examples(filtered_split, n)

            filtered_count = len(filtered_split)
            total_filtered += filtered_count

            # Remove the helper columns
            if hasattr(filtered_split, 'remove_columns'):
                filtered_split = filtered_split.remove_columns(['token_count', 'within_limit'])

            # Save filtered split as JSONL in main dataset folder
            split_output_file = os.path.join(output_path, f"{split_name}.jsonl")
            save_split_as_jsonl(filtered_split, split_output_file)

            logger.info(f"Saved {split_name} with {filtered_count} examples "
                       f"({filtered_count/original_count*100:.1f}% retained) to {split_output_file}")

    # Print summary statistics
    logger.info(f"Total: {total_original} -> {total_filtered} examples "
               f"({total_filtered/total_original*100:.1f}% retained)")

    print("\n" + "="*60)
    print("FILTERING SUMMARY")
    print("="*60)
    print(f"Dataset: {dataset_name}")
    print(f"Tokenizer: {tokenizer_path}")
    print(f"Max token length: {max_length}")
    print(f"Output path: {output_path}")
    print(f"Has subsets: {has_subsets}")
    if has_subsets:
        print(f"Subsets: {config_names}")
    if n is not None:
        print(f"Random sampling limit: {n} examples per split")
    print(f"Total examples: {total_original} -> {total_filtered}")
    print(f"Retention rate: {total_filtered/total_original*100:.1f}%")
    print("="*60)

if __name__ == "__main__":
    fire.Fire(download_and_filter_dataset_jsonl)
