[2025-07-08 18:56:46,204] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 18:56:49,558] [WARNING] [runner.py:215:fetch_hostfile] Unable to find hostfile, will proceed with training with local resources only.
Detected VISIBLE_DEVICES=4,5,6,7: setting --include=localhost:4,5,6,7
[2025-07-08 18:56:49,558] [INFO] [runner.py:605:main] cmd = /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10 -u -m deepspeed.launcher.launch --world_info=eyJsb2NhbGhvc3QiOiBbNCwgNSwgNiwgN119 --master_addr=127.0.0.1 --master_port=49056 --enable_each_rank_log=None train.py --deepspeed deepspeed3.json --proctitle junkim100 --model_name_or_path Llama-3.1-8B-Instruct --data_name /data_x/junkim100/projects/finetune/data/LIMO/train.jsonl --wb_project kullm-pro --wb_name LIMO_Llama3.1_8B --output_name LIMO_Llama3.1_8B --max_length 16384 --num_train_epochs 2 --per_device_train_batch_size 1 --per_device_eval_batch_size 1 --gradient_accumulation_steps 1 --save_only_model --learning_rate 1e-5 --weight_decay 0. --warmup_ratio 0. --lr_scheduler_type cosine --bf16 True --tf32 True --gradient_checkpointing True --logging_steps 1
[2025-07-08 18:56:51,431] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 18:56:54,478] [INFO] [launch.py:146:main] WORLD INFO DICT: {'localhost': [4, 5, 6, 7]}
[2025-07-08 18:56:54,478] [INFO] [launch.py:152:main] nnodes=1, num_local_procs=4, node_rank=0
[2025-07-08 18:56:54,478] [INFO] [launch.py:163:main] global_rank_mapping=defaultdict(<class 'list'>, {'localhost': [0, 1, 2, 3]})
[2025-07-08 18:56:54,478] [INFO] [launch.py:164:main] dist_world_size=4
[2025-07-08 18:56:54,478] [INFO] [launch.py:168:main] Setting CUDA_VISIBLE_DEVICES=4,5,6,7
[2025-07-08 18:56:54,479] [INFO] [launch.py:256:main] process 3100575 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=0', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Llama3.1_8B', '--output_name', 'LIMO_Llama3.1_8B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-08 18:56:54,480] [INFO] [launch.py:256:main] process 3100576 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=1', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Llama3.1_8B', '--output_name', 'LIMO_Llama3.1_8B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-08 18:56:54,480] [INFO] [launch.py:256:main] process 3100577 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=2', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Llama3.1_8B', '--output_name', 'LIMO_Llama3.1_8B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-08 18:56:54,481] [INFO] [launch.py:256:main] process 3100578 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=3', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Llama3.1_8B', '--output_name', 'LIMO_Llama3.1_8B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-08 18:56:59,331] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 18:56:59,498] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 18:56:59,535] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 18:56:59,568] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 18:57:00,352] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 18:57:00,599] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 18:57:00,599] [INFO] [comm.py:700:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
[2025-07-08 18:57:00,611] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 18:57:00,782] [INFO] [comm.py:669:init_distributed] cdb=None
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=0,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul08_18-56-57_nlp-server-18,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=LIMO_Llama3.1_8B,
wb_project=kullm-pro,
weight_decay=0.0,
)
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=2,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul08_18-56-57_nlp-server-18,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=LIMO_Llama3.1_8B,
wb_project=kullm-pro,
weight_decay=0.0,
)
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=1,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul08_18-56-57_nlp-server-18,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=LIMO_Llama3.1_8B,
wb_project=kullm-pro,
weight_decay=0.0,
)
[2025-07-08 18:57:01,607] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 4
[2025-07-08 18:57:01,624] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 4
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=3,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul08_18-56-57_nlp-server-18,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=LIMO_Llama3.1_8B,
wb_project=kullm-pro,
weight_decay=0.0,
)
[2025-07-08 18:57:01,718] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 4
[2025-07-08 18:57:01,786] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 4
[2025-07-08 18:57:09,166] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 291, num_elems = 8.03B

Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:42<02:08, 42.87s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:42<02:08, 42.83s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:42<02:08, 42.83s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:43<02:09, 43.04s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [01:25<01:25, 42.95s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [01:25<01:25, 42.96s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [01:25<01:25, 42.95s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [01:26<01:26, 43.02s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [02:08<00:42, 42.72s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [02:08<00:42, 42.71s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [02:08<00:42, 42.71s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [02:08<00:42, 42.76s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:18<00:00, 29.86s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:18<00:00, 34.61s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [02:18<00:00, 29.87s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:18<00:00, 34.62s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [02:18<00:00, 29.86s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:18<00:00, 34.61s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [02:18<00:00, 29.84s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:18<00:00, 34.63s/it]
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
WARNING:root:Loading data...
WARNING:root:Loading data...
WARNING:root:Loading data...
WARNING:root:Loading data...
WARNING:root:Data Loaded...
WARNING:root:Data Loaded...
WARNING:root:Data Loaded...
WARNING:root:Data Loaded...
WARNING:root:mean_token_length: 5863.587322121604
WARNING:root:min_token_length: 1103
WARNING:root:max_token_length: 15232
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:247: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
WARNING:root:mean_token_length: 5863.587322121604
WARNING:root:min_token_length: 1103
WARNING:root:max_token_length: 15232
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:247: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
WARNING:root:mean_token_length: 5863.587322121604
WARNING:root:min_token_length: 1103
WARNING:root:max_token_length: 15232
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:247: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
WARNING:root:mean_token_length: 5863.587322121604
WARNING:root:min_token_length: 1103
WARNING:root:max_token_length: 15232
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:247: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
Received unrecognized `WANDB_LOG_MODEL` setting value=LIMO_Llama3.1_8B; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=LIMO_Llama3.1_8B; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=LIMO_Llama3.1_8B; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=LIMO_Llama3.1_8B; so disabling `WANDB_LOG_MODEL`
Parameter Offload: Total persistent parameters: 266240 in 65 params
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[2025-07-08 19:00:02,828] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-08 19:00:02,828] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-08 19:00:02,828] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
[2025-07-08 19:00:03,164] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
wandb: Currently logged in as: junkim100 (junkim) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /data_x/junkim100/projects/finetune/wandb/run-20250708_190003-c1rnkuf9
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run trainer_output
wandb: ⭐️ View project at https://wandb.ai/junkim/kullm-pro
wandb: 🚀 View run at https://wandb.ai/junkim/kullm-pro/runs/c1rnkuf9

  0%|          | 0/388 [00:00<?, ?it/s]`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.

  0%|          | 1/388 [00:17<1:54:28, 17.75s/it]
                                                 
{'loss': 1.0613, 'grad_norm': 5.972428798675537, 'learning_rate': 0.0, 'epoch': 0.01}

  0%|          | 1/388 [00:17<1:54:28, 17.75s/it]
  1%|          | 2/388 [00:28<1:29:18, 13.88s/it]
                                                 
{'loss': 0.7394, 'grad_norm': 3.9083375930786133, 'learning_rate': 1e-05, 'epoch': 0.01}

  1%|          | 2/388 [00:28<1:29:18, 13.88s/it]
  1%|          | 3/388 [00:37<1:12:33, 11.31s/it]
                                                 
{'loss': 1.039, 'grad_norm': 5.037124156951904, 'learning_rate': 1e-05, 'epoch': 0.02}

  1%|          | 3/388 [00:37<1:12:33, 11.31s/it]
  1%|          | 4/388 [00:45<1:06:00, 10.31s/it]
                                                 
{'loss': 0.8418, 'grad_norm': 2.822321653366089, 'learning_rate': 9.974093264248705e-06, 'epoch': 0.02}

  1%|          | 4/388 [00:45<1:06:00, 10.31s/it]
  1%|▏         | 5/388 [00:57<1:08:24, 10.72s/it]
                                                 
{'loss': 0.7798, 'grad_norm': 5.261780738830566, 'learning_rate': 9.94818652849741e-06, 'epoch': 0.03}

  1%|▏         | 5/388 [00:57<1:08:24, 10.72s/it]
  2%|▏         | 6/388 [01:06<1:05:22, 10.27s/it]
                                                 
{'loss': 0.7993, 'grad_norm': 3.8250977993011475, 'learning_rate': 9.922279792746115e-06, 'epoch': 0.03}

  2%|▏         | 6/388 [01:06<1:05:22, 10.27s/it]
  2%|▏         | 7/388 [01:19<1:09:25, 10.93s/it]
                                                 
{'loss': 0.9744, 'grad_norm': 4.040095329284668, 'learning_rate': 9.89637305699482e-06, 'epoch': 0.04}

  2%|▏         | 7/388 [01:19<1:09:25, 10.93s/it]
  2%|▏         | 8/388 [01:27<1:04:55, 10.25s/it]
                                                 
{'loss': 1.1334, 'grad_norm': 3.3422718048095703, 'learning_rate': 9.870466321243524e-06, 'epoch': 0.04}

  2%|▏         | 8/388 [01:27<1:04:55, 10.25s/it]
  2%|▏         | 9/388 [01:35<1:00:02,  9.51s/it]
                                                 
{'loss': 1.0422, 'grad_norm': 3.6680266857147217, 'learning_rate': 9.844559585492228e-06, 'epoch': 0.05}

  2%|▏         | 9/388 [01:35<1:00:02,  9.51s/it]
  3%|▎         | 10/388 [01:48<1:07:01, 10.64s/it]
                                                  
{'loss': 0.7601, 'grad_norm': 4.0741095542907715, 'learning_rate': 9.818652849740934e-06, 'epoch': 0.05}

  3%|▎         | 10/388 [01:48<1:07:01, 10.64s/it]
  3%|▎         | 11/388 [01:56<1:01:26,  9.78s/it]
                                                  
{'loss': 0.8571, 'grad_norm': 3.0370264053344727, 'learning_rate': 9.792746113989638e-06, 'epoch': 0.06}

  3%|▎         | 11/388 [01:56<1:01:26,  9.78s/it]
  3%|▎         | 12/388 [02:06<1:01:14,  9.77s/it]
                                                  
{'loss': 0.8824, 'grad_norm': 2.6689648628234863, 'learning_rate': 9.766839378238344e-06, 'epoch': 0.06}

  3%|▎         | 12/388 [02:06<1:01:14,  9.77s/it]
  3%|▎         | 13/388 [02:16<1:01:30,  9.84s/it]
                                                  
{'loss': 0.8879, 'grad_norm': 2.9273457527160645, 'learning_rate': 9.740932642487048e-06, 'epoch': 0.07}

  3%|▎         | 13/388 [02:16<1:01:30,  9.84s/it]
  4%|▎         | 14/388 [02:24<57:02,  9.15s/it]  
                                                
{'loss': 0.8117, 'grad_norm': 2.7669601440429688, 'learning_rate': 9.715025906735752e-06, 'epoch': 0.07}

  4%|▎         | 14/388 [02:24<57:02,  9.15s/it]
  4%|▍         | 15/388 [02:32<55:57,  9.00s/it]
                                                
{'loss': 0.7426, 'grad_norm': 2.4161019325256348, 'learning_rate': 9.689119170984456e-06, 'epoch': 0.08}

  4%|▍         | 15/388 [02:32<55:57,  9.00s/it]
  4%|▍         | 16/388 [02:44<1:00:24,  9.74s/it]
                                                  
{'loss': 0.8518, 'grad_norm': 2.307830572128296, 'learning_rate': 9.66321243523316e-06, 'epoch': 0.08}

  4%|▍         | 16/388 [02:44<1:00:24,  9.74s/it]
  4%|▍         | 17/388 [02:55<1:02:14, 10.07s/it]
                                                  
{'loss': 0.9133, 'grad_norm': 2.167433261871338, 'learning_rate': 9.637305699481867e-06, 'epoch': 0.09}

  4%|▍         | 17/388 [02:55<1:02:14, 10.07s/it]
  5%|▍         | 18/388 [03:05<1:02:25, 10.12s/it]
                                                  
{'loss': 0.6976, 'grad_norm': 2.4613585472106934, 'learning_rate': 9.61139896373057e-06, 'epoch': 0.09}

  5%|▍         | 18/388 [03:05<1:02:25, 10.12s/it]
  5%|▍         | 19/388 [03:15<1:02:22, 10.14s/it]
                                                  
{'loss': 0.824, 'grad_norm': 2.515002965927124, 'learning_rate': 9.585492227979275e-06, 'epoch': 0.1}

  5%|▍         | 19/388 [03:15<1:02:22, 10.14s/it]
  5%|▌         | 20/388 [03:24<59:57,  9.77s/it]  
                                                
{'loss': 0.9698, 'grad_norm': 2.5747175216674805, 'learning_rate': 9.559585492227979e-06, 'epoch': 0.1}

  5%|▌         | 20/388 [03:24<59:57,  9.77s/it]
  5%|▌         | 21/388 [03:32<56:23,  9.22s/it]
                                                
{'loss': 0.7327, 'grad_norm': 3.076650619506836, 'learning_rate': 9.533678756476683e-06, 'epoch': 0.11}

  5%|▌         | 21/388 [03:32<56:23,  9.22s/it]
  6%|▌         | 22/388 [03:43<1:00:27,  9.91s/it]
                                                  
{'loss': 0.8011, 'grad_norm': 3.3015217781066895, 'learning_rate': 9.50777202072539e-06, 'epoch': 0.11}

  6%|▌         | 22/388 [03:43<1:00:27,  9.91s/it]
  6%|▌         | 23/388 [03:55<1:02:52, 10.34s/it]
                                                  
{'loss': 0.6903, 'grad_norm': 2.6446707248687744, 'learning_rate': 9.481865284974095e-06, 'epoch': 0.12}

  6%|▌         | 23/388 [03:55<1:02:52, 10.34s/it]
  6%|▌         | 24/388 [04:03<59:02,  9.73s/it]  
                                                
{'loss': 0.7495, 'grad_norm': 2.679511308670044, 'learning_rate': 9.4559585492228e-06, 'epoch': 0.12}

  6%|▌         | 24/388 [04:03<59:02,  9.73s/it]
  6%|▋         | 25/388 [04:15<1:02:47, 10.38s/it]
                                                  
{'loss': 0.6137, 'grad_norm': 2.0696640014648438, 'learning_rate': 9.430051813471504e-06, 'epoch': 0.13}

  6%|▋         | 25/388 [04:15<1:02:47, 10.38s/it]
  7%|▋         | 26/388 [04:22<57:02,  9.46s/it]  
                                                
{'loss': 0.6033, 'grad_norm': 2.506619930267334, 'learning_rate': 9.404145077720208e-06, 'epoch': 0.13}

  7%|▋         | 26/388 [04:22<57:02,  9.46s/it]
  7%|▋         | 27/388 [04:32<57:41,  9.59s/it]
                                                
{'loss': 0.9127, 'grad_norm': 2.3916053771972656, 'learning_rate': 9.378238341968912e-06, 'epoch': 0.14}

  7%|▋         | 27/388 [04:32<57:41,  9.59s/it]
  7%|▋         | 28/388 [04:45<1:03:56, 10.66s/it]
                                                  
{'loss': 0.6533, 'grad_norm': 2.373687505722046, 'learning_rate': 9.352331606217618e-06, 'epoch': 0.14}

  7%|▋         | 28/388 [04:45<1:03:56, 10.66s/it]
  7%|▋         | 29/388 [04:54<1:00:34, 10.12s/it]
                                                  
{'loss': 1.137, 'grad_norm': 2.58476185798645, 'learning_rate': 9.326424870466322e-06, 'epoch': 0.15}

  7%|▋         | 29/388 [04:54<1:00:34, 10.12s/it]
  8%|▊         | 30/388 [05:02<56:44,  9.51s/it]  
                                                
{'loss': 1.004, 'grad_norm': 2.3435304164886475, 'learning_rate': 9.300518134715026e-06, 'epoch': 0.15}

  8%|▊         | 30/388 [05:02<56:44,  9.51s/it]
  8%|▊         | 31/388 [05:12<56:32,  9.50s/it]
                                                
{'loss': 0.6218, 'grad_norm': 2.7886364459991455, 'learning_rate': 9.27461139896373e-06, 'epoch': 0.16}

  8%|▊         | 31/388 [05:12<56:32,  9.50s/it]
  8%|▊         | 32/388 [05:19<53:22,  8.99s/it]
                                                
{'loss': 0.7332, 'grad_norm': 2.8591411113739014, 'learning_rate': 9.248704663212435e-06, 'epoch': 0.16}

  8%|▊         | 32/388 [05:19<53:22,  8.99s/it]
  9%|▊         | 33/388 [05:28<51:57,  8.78s/it]
                                                
{'loss': 0.7658, 'grad_norm': 2.5078628063201904, 'learning_rate': 9.22279792746114e-06, 'epoch': 0.17}

  9%|▊         | 33/388 [05:28<51:57,  8.78s/it]
  9%|▉         | 34/388 [05:38<55:04,  9.34s/it]
                                                
{'loss': 0.9093, 'grad_norm': 2.322923421859741, 'learning_rate': 9.196891191709847e-06, 'epoch': 0.18}

  9%|▉         | 34/388 [05:38<55:04,  9.34s/it]
  9%|▉         | 35/388 [05:47<52:58,  9.00s/it]
                                                
{'loss': 0.9376, 'grad_norm': 2.553248405456543, 'learning_rate': 9.17098445595855e-06, 'epoch': 0.18}

  9%|▉         | 35/388 [05:47<52:58,  9.00s/it]
  9%|▉         | 36/388 [05:56<53:26,  9.11s/it]
                                                
{'loss': 0.6415, 'grad_norm': 2.6050047874450684, 'learning_rate': 9.145077720207255e-06, 'epoch': 0.19}

  9%|▉         | 36/388 [05:56<53:26,  9.11s/it]
 10%|▉         | 37/388 [06:05<53:06,  9.08s/it]
                                                
{'loss': 0.7948, 'grad_norm': 2.4142885208129883, 'learning_rate': 9.11917098445596e-06, 'epoch': 0.19}

 10%|▉         | 37/388 [06:05<53:06,  9.08s/it]
 10%|▉         | 38/388 [06:15<53:57,  9.25s/it]
                                                
{'loss': 0.6776, 'grad_norm': 2.33914852142334, 'learning_rate': 9.093264248704663e-06, 'epoch': 0.2}

 10%|▉         | 38/388 [06:15<53:57,  9.25s/it]
 10%|█         | 39/388 [06:23<52:38,  9.05s/it]
                                                
{'loss': 1.2934, 'grad_norm': 2.9091343879699707, 'learning_rate': 9.06735751295337e-06, 'epoch': 0.2}

 10%|█         | 39/388 [06:23<52:38,  9.05s/it]
 10%|█         | 40/388 [06:32<51:43,  8.92s/it]
                                                
{'loss': 0.7059, 'grad_norm': 2.4093809127807617, 'learning_rate': 9.041450777202073e-06, 'epoch': 0.21}

 10%|█         | 40/388 [06:32<51:43,  8.92s/it]
 11%|█         | 41/388 [06:42<53:40,  9.28s/it]
                                                
{'loss': 0.8125, 'grad_norm': 2.805210590362549, 'learning_rate': 9.015544041450778e-06, 'epoch': 0.21}

 11%|█         | 41/388 [06:42<53:40,  9.28s/it]
 11%|█         | 42/388 [06:51<52:23,  9.08s/it]
                                                
{'loss': 0.7355, 'grad_norm': 2.395639419555664, 'learning_rate': 8.989637305699482e-06, 'epoch': 0.22}

 11%|█         | 42/388 [06:51<52:23,  9.08s/it]
 11%|█         | 43/388 [07:00<53:34,  9.32s/it]
                                                
{'loss': 0.7558, 'grad_norm': 2.558945894241333, 'learning_rate': 8.963730569948186e-06, 'epoch': 0.22}

 11%|█         | 43/388 [07:00<53:34,  9.32s/it]
 11%|█▏        | 44/388 [07:10<53:22,  9.31s/it]
                                                
{'loss': 0.5813, 'grad_norm': 2.971144437789917, 'learning_rate': 8.937823834196892e-06, 'epoch': 0.23}

 11%|█▏        | 44/388 [07:10<53:22,  9.31s/it]
 12%|█▏        | 45/388 [07:21<56:18,  9.85s/it]
                                                
{'loss': 0.7257, 'grad_norm': 2.6035735607147217, 'learning_rate': 8.911917098445596e-06, 'epoch': 0.23}

 12%|█▏        | 45/388 [07:21<56:18,  9.85s/it][2025-07-08 19:07:39,094] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time

 12%|█▏        | 46/388 [07:34<1:01:02, 10.71s/it]
                                                  
{'loss': 0.6401, 'grad_norm': 3.035959243774414, 'learning_rate': 8.886010362694302e-06, 'epoch': 0.24}

 12%|█▏        | 46/388 [07:34<1:01:02, 10.71s/it]
 12%|█▏        | 47/388 [07:43<58:43, 10.33s/it]  
                                                
{'loss': 0.6298, 'grad_norm': 2.4860098361968994, 'learning_rate': 8.860103626943006e-06, 'epoch': 0.24}

 12%|█▏        | 47/388 [07:43<58:43, 10.33s/it]
 12%|█▏        | 48/388 [07:52<55:38,  9.82s/it]
                                                
{'loss': 0.7476, 'grad_norm': 2.293618679046631, 'learning_rate': 8.83419689119171e-06, 'epoch': 0.25}

 12%|█▏        | 48/388 [07:52<55:38,  9.82s/it]
 13%|█▎        | 49/388 [08:03<57:43, 10.22s/it]
                                                
{'loss': 0.848, 'grad_norm': 2.499324083328247, 'learning_rate': 8.808290155440415e-06, 'epoch': 0.25}

 13%|█▎        | 49/388 [08:03<57:43, 10.22s/it]
 13%|█▎        | 50/388 [08:13<57:04, 10.13s/it]
                                                
{'loss': 0.7049, 'grad_norm': 2.6589033603668213, 'learning_rate': 8.78238341968912e-06, 'epoch': 0.26}

 13%|█▎        | 50/388 [08:13<57:04, 10.13s/it]
 13%|█▎        | 51/388 [08:24<58:17, 10.38s/it]
                                                
{'loss': 0.8705, 'grad_norm': 2.1938395500183105, 'learning_rate': 8.756476683937825e-06, 'epoch': 0.26}

 13%|█▎        | 51/388 [08:24<58:17, 10.38s/it]
 13%|█▎        | 52/388 [08:32<55:21,  9.88s/it]
                                                
{'loss': 0.9104, 'grad_norm': 2.4931790828704834, 'learning_rate': 8.730569948186529e-06, 'epoch': 0.27}

 13%|█▎        | 52/388 [08:32<55:21,  9.88s/it]
 14%|█▎        | 53/388 [08:44<58:06, 10.41s/it]
                                                
{'loss': 0.833, 'grad_norm': 2.2161130905151367, 'learning_rate': 8.704663212435233e-06, 'epoch': 0.27}

 14%|█▎        | 53/388 [08:44<58:06, 10.41s/it]
 14%|█▍        | 54/388 [08:55<59:29, 10.69s/it]
                                                
{'loss': 0.7508, 'grad_norm': 2.7588376998901367, 'learning_rate': 8.678756476683938e-06, 'epoch': 0.28}

 14%|█▍        | 54/388 [08:55<59:29, 10.69s/it]
 14%|█▍        | 55/388 [09:04<55:31, 10.00s/it]
                                                
{'loss': 0.8628, 'grad_norm': 2.7748560905456543, 'learning_rate': 8.652849740932643e-06, 'epoch': 0.28}

 14%|█▍        | 55/388 [09:04<55:31, 10.00s/it]
 14%|█▍        | 56/388 [09:13<54:49,  9.91s/it]
                                                
{'loss': 0.7079, 'grad_norm': 2.1448283195495605, 'learning_rate': 8.626943005181348e-06, 'epoch': 0.29}

 14%|█▍        | 56/388 [09:13<54:49,  9.91s/it]
 15%|█▍        | 57/388 [09:23<53:25,  9.69s/it]
                                                
{'loss': 1.2438, 'grad_norm': 2.4516265392303467, 'learning_rate': 8.601036269430052e-06, 'epoch': 0.29}

 15%|█▍        | 57/388 [09:23<53:25,  9.69s/it]
 15%|█▍        | 58/388 [09:30<49:06,  8.93s/it]
                                                
{'loss': 0.5436, 'grad_norm': 2.87629771232605, 'learning_rate': 8.575129533678758e-06, 'epoch': 0.3}

 15%|█▍        | 58/388 [09:30<49:06,  8.93s/it]
 15%|█▌        | 59/388 [09:40<50:47,  9.26s/it]
                                                
{'loss': 0.5823, 'grad_norm': 2.6505439281463623, 'learning_rate': 8.549222797927462e-06, 'epoch': 0.3}

 15%|█▌        | 59/388 [09:40<50:47,  9.26s/it]
 15%|█▌        | 60/388 [09:49<50:57,  9.32s/it]
                                                
{'loss': 1.2081, 'grad_norm': 2.7029812335968018, 'learning_rate': 8.523316062176166e-06, 'epoch': 0.31}

 15%|█▌        | 60/388 [09:49<50:57,  9.32s/it]
 16%|█▌        | 61/388 [09:59<50:44,  9.31s/it]
                                                
{'loss': 0.5786, 'grad_norm': 2.386359214782715, 'learning_rate': 8.497409326424872e-06, 'epoch': 0.31}

 16%|█▌        | 61/388 [09:59<50:44,  9.31s/it]
 16%|█▌        | 62/388 [10:09<53:05,  9.77s/it]
                                                
{'loss': 0.8214, 'grad_norm': 2.2240004539489746, 'learning_rate': 8.471502590673576e-06, 'epoch': 0.32}

 16%|█▌        | 62/388 [10:09<53:05,  9.77s/it]
 16%|█▌        | 63/388 [10:20<53:58,  9.97s/it]
                                                
{'loss': 0.8158, 'grad_norm': 2.523404598236084, 'learning_rate': 8.44559585492228e-06, 'epoch': 0.32}

 16%|█▌        | 63/388 [10:20<53:58,  9.97s/it]
 16%|█▋        | 64/388 [10:27<49:56,  9.25s/it]
                                                
{'loss': 0.7062, 'grad_norm': 2.5156991481781006, 'learning_rate': 8.419689119170985e-06, 'epoch': 0.33}

 16%|█▋        | 64/388 [10:27<49:56,  9.25s/it]
 17%|█▋        | 65/388 [10:39<54:11, 10.07s/it]
                                                
{'loss': 0.6367, 'grad_norm': 2.2868878841400146, 'learning_rate': 8.393782383419689e-06, 'epoch': 0.34}

 17%|█▋        | 65/388 [10:39<54:11, 10.07s/it]
 17%|█▋        | 66/388 [10:49<52:57,  9.87s/it]
                                                
{'loss': 0.9194, 'grad_norm': 3.0570547580718994, 'learning_rate': 8.367875647668395e-06, 'epoch': 0.34}

 17%|█▋        | 66/388 [10:49<52:57,  9.87s/it]
 17%|█▋        | 67/388 [10:57<50:02,  9.35s/it]
                                                
{'loss': 0.6803, 'grad_norm': 2.5580966472625732, 'learning_rate': 8.341968911917099e-06, 'epoch': 0.35}

 17%|█▋        | 67/388 [10:57<50:02,  9.35s/it]
 18%|█▊        | 68/388 [11:06<49:51,  9.35s/it]
                                                
{'loss': 0.6019, 'grad_norm': 2.4723732471466064, 'learning_rate': 8.316062176165803e-06, 'epoch': 0.35}

 18%|█▊        | 68/388 [11:06<49:51,  9.35s/it]
 18%|█▊        | 69/388 [11:15<48:29,  9.12s/it]
                                                
{'loss': 0.9282, 'grad_norm': 2.831188440322876, 'learning_rate': 8.290155440414507e-06, 'epoch': 0.36}

 18%|█▊        | 69/388 [11:15<48:29,  9.12s/it]
 18%|█▊        | 70/388 [11:24<47:47,  9.02s/it]
                                                
{'loss': 0.8331, 'grad_norm': 2.535067081451416, 'learning_rate': 8.264248704663213e-06, 'epoch': 0.36}

 18%|█▊        | 70/388 [11:24<47:47,  9.02s/it]
 18%|█▊        | 71/388 [11:35<51:50,  9.81s/it]
                                                
{'loss': 0.7635, 'grad_norm': 2.6289937496185303, 'learning_rate': 8.238341968911918e-06, 'epoch': 0.37}

 18%|█▊        | 71/388 [11:35<51:50,  9.81s/it]
 19%|█▊        | 72/388 [11:44<50:05,  9.51s/it]
                                                
{'loss': 0.6756, 'grad_norm': 2.524071455001831, 'learning_rate': 8.212435233160623e-06, 'epoch': 0.37}

 19%|█▊        | 72/388 [11:44<50:05,  9.51s/it]
 19%|█▉        | 73/388 [11:55<51:44,  9.86s/it]
                                                
{'loss': 0.8577, 'grad_norm': 2.2408902645111084, 'learning_rate': 8.186528497409328e-06, 'epoch': 0.38}

 19%|█▉        | 73/388 [11:55<51:44,  9.86s/it]
 19%|█▉        | 74/388 [12:06<53:51, 10.29s/it]
                                                
{'loss': 0.709, 'grad_norm': 1.9304513931274414, 'learning_rate': 8.160621761658032e-06, 'epoch': 0.38}

 19%|█▉        | 74/388 [12:06<53:51, 10.29s/it]
 19%|█▉        | 75/388 [12:15<50:57,  9.77s/it]
                                                
{'loss': 0.9485, 'grad_norm': 2.5184836387634277, 'learning_rate': 8.134715025906736e-06, 'epoch': 0.39}

 19%|█▉        | 75/388 [12:15<50:57,  9.77s/it]
 20%|█▉        | 76/388 [12:25<51:22,  9.88s/it]
                                                
{'loss': 0.7096, 'grad_norm': 2.1354572772979736, 'learning_rate': 8.10880829015544e-06, 'epoch': 0.39}

 20%|█▉        | 76/388 [12:25<51:22,  9.88s/it]
 20%|█▉        | 77/388 [12:35<51:58, 10.03s/it]
                                                
{'loss': 0.6038, 'grad_norm': 1.9624686241149902, 'learning_rate': 8.082901554404146e-06, 'epoch': 0.4}

 20%|█▉        | 77/388 [12:35<51:58, 10.03s/it]
 20%|██        | 78/388 [12:47<54:35, 10.57s/it]
                                                
{'loss': 0.7366, 'grad_norm': 2.488140344619751, 'learning_rate': 8.05699481865285e-06, 'epoch': 0.4}

 20%|██        | 78/388 [12:47<54:35, 10.57s/it]
 20%|██        | 79/388 [12:58<55:44, 10.82s/it]
                                                
{'loss': 0.8224, 'grad_norm': 2.462656021118164, 'learning_rate': 8.031088082901555e-06, 'epoch': 0.41}

 20%|██        | 79/388 [12:58<55:44, 10.82s/it]
 21%|██        | 80/388 [13:08<54:07, 10.54s/it]
                                                
{'loss': 0.966, 'grad_norm': 1.9545427560806274, 'learning_rate': 8.005181347150259e-06, 'epoch': 0.41}

 21%|██        | 80/388 [13:08<54:07, 10.54s/it]
 21%|██        | 81/388 [13:19<53:29, 10.45s/it]
                                                
{'loss': 0.6323, 'grad_norm': 2.3197035789489746, 'learning_rate': 7.979274611398965e-06, 'epoch': 0.42}

 21%|██        | 81/388 [13:19<53:29, 10.45s/it]
 21%|██        | 82/388 [13:27<50:27,  9.89s/it]
                                                
{'loss': 0.8814, 'grad_norm': 2.51138973236084, 'learning_rate': 7.953367875647669e-06, 'epoch': 0.42}

 21%|██        | 82/388 [13:27<50:27,  9.89s/it]
 21%|██▏       | 83/388 [13:37<49:36,  9.76s/it]
                                                
{'loss': 0.7239, 'grad_norm': 2.2599854469299316, 'learning_rate': 7.927461139896375e-06, 'epoch': 0.43}

 21%|██▏       | 83/388 [13:37<49:36,  9.76s/it]
 22%|██▏       | 84/388 [13:46<49:05,  9.69s/it]
                                                
{'loss': 0.8482, 'grad_norm': 2.1153910160064697, 'learning_rate': 7.901554404145079e-06, 'epoch': 0.43}

 22%|██▏       | 84/388 [13:46<49:05,  9.69s/it]
 22%|██▏       | 85/388 [13:55<47:04,  9.32s/it]
                                                
{'loss': 0.6717, 'grad_norm': 2.4645915031433105, 'learning_rate': 7.875647668393783e-06, 'epoch': 0.44}

 22%|██▏       | 85/388 [13:55<47:04,  9.32s/it]
 22%|██▏       | 86/388 [14:03<45:48,  9.10s/it]
                                                
{'loss': 0.5643, 'grad_norm': 2.230720043182373, 'learning_rate': 7.849740932642487e-06, 'epoch': 0.44}

 22%|██▏       | 86/388 [14:03<45:48,  9.10s/it]
 22%|██▏       | 87/388 [14:13<46:47,  9.33s/it]
                                                
{'loss': 1.1067, 'grad_norm': 2.247462749481201, 'learning_rate': 7.823834196891192e-06, 'epoch': 0.45}

 22%|██▏       | 87/388 [14:13<46:47,  9.33s/it]
 23%|██▎       | 88/388 [14:22<46:05,  9.22s/it]
                                                
{'loss': 0.5903, 'grad_norm': 2.122015953063965, 'learning_rate': 7.797927461139898e-06, 'epoch': 0.45}

 23%|██▎       | 88/388 [14:22<46:05,  9.22s/it]
 23%|██▎       | 89/388 [14:30<44:09,  8.86s/it]
                                                
{'loss': 0.8035, 'grad_norm': 2.2608187198638916, 'learning_rate': 7.772020725388602e-06, 'epoch': 0.46}

 23%|██▎       | 89/388 [14:30<44:09,  8.86s/it]
 23%|██▎       | 90/388 [14:40<46:10,  9.30s/it]
                                                
{'loss': 0.9305, 'grad_norm': 2.020524740219116, 'learning_rate': 7.746113989637306e-06, 'epoch': 0.46}

 23%|██▎       | 90/388 [14:40<46:10,  9.30s/it]
 23%|██▎       | 91/388 [14:50<46:02,  9.30s/it]
                                                
{'loss': 0.8977, 'grad_norm': 2.1622703075408936, 'learning_rate': 7.72020725388601e-06, 'epoch': 0.47}

 23%|██▎       | 91/388 [14:50<46:02,  9.30s/it]
 24%|██▎       | 92/388 [14:59<46:39,  9.46s/it]
                                                
{'loss': 0.5507, 'grad_norm': 2.5529732704162598, 'learning_rate': 7.694300518134716e-06, 'epoch': 0.47}

 24%|██▎       | 92/388 [14:59<46:39,  9.46s/it]
 24%|██▍       | 93/388 [15:09<46:55,  9.54s/it]
                                                
{'loss': 0.6802, 'grad_norm': 2.2691476345062256, 'learning_rate': 7.66839378238342e-06, 'epoch': 0.48}

 24%|██▍       | 93/388 [15:09<46:55,  9.54s/it]
 24%|██▍       | 94/388 [15:19<46:28,  9.48s/it]
                                                
{'loss': 0.546, 'grad_norm': 2.7194442749023438, 'learning_rate': 7.642487046632126e-06, 'epoch': 0.48}

 24%|██▍       | 94/388 [15:19<46:28,  9.48s/it]
 24%|██▍       | 95/388 [15:29<47:57,  9.82s/it]
                                                
{'loss': 0.7549, 'grad_norm': 2.5331454277038574, 'learning_rate': 7.61658031088083e-06, 'epoch': 0.49}

 24%|██▍       | 95/388 [15:29<47:57,  9.82s/it]
 25%|██▍       | 96/388 [15:40<48:49, 10.03s/it]
                                                
{'loss': 1.0136, 'grad_norm': 2.519665241241455, 'learning_rate': 7.590673575129535e-06, 'epoch': 0.49}

 25%|██▍       | 96/388 [15:40<48:49, 10.03s/it]
 25%|██▌       | 97/388 [15:49<47:45,  9.85s/it]
                                                
{'loss': 0.6214, 'grad_norm': 2.548218250274658, 'learning_rate': 7.564766839378239e-06, 'epoch': 0.5}

 25%|██▌       | 97/388 [15:49<47:45,  9.85s/it]
 25%|██▌       | 98/388 [15:57<45:27,  9.40s/it]
                                                
{'loss': 1.031, 'grad_norm': 2.20029354095459, 'learning_rate': 7.538860103626944e-06, 'epoch': 0.51}

 25%|██▌       | 98/388 [15:57<45:27,  9.40s/it]
 26%|██▌       | 99/388 [16:08<46:38,  9.68s/it]
                                                
{'loss': 0.708, 'grad_norm': 2.1590566635131836, 'learning_rate': 7.512953367875648e-06, 'epoch': 0.51}

 26%|██▌       | 99/388 [16:08<46:38,  9.68s/it]
 26%|██▌       | 100/388 [16:15<43:38,  9.09s/it]
                                                 
{'loss': 0.7503, 'grad_norm': 2.317505121231079, 'learning_rate': 7.487046632124353e-06, 'epoch': 0.52}

 26%|██▌       | 100/388 [16:15<43:38,  9.09s/it]
 26%|██▌       | 101/388 [16:24<42:43,  8.93s/it]
                                                 
{'loss': 0.6039, 'grad_norm': 2.3594632148742676, 'learning_rate': 7.461139896373057e-06, 'epoch': 0.52}

 26%|██▌       | 101/388 [16:24<42:43,  8.93s/it]
 26%|██▋       | 102/388 [16:35<45:02,  9.45s/it]
                                                 
{'loss': 0.7126, 'grad_norm': 1.7658730745315552, 'learning_rate': 7.435233160621762e-06, 'epoch': 0.53}

 26%|██▋       | 102/388 [16:35<45:02,  9.45s/it]
 27%|██▋       | 103/388 [16:47<49:01, 10.32s/it]
                                                 
{'loss': 0.7095, 'grad_norm': 2.122969388961792, 'learning_rate': 7.409326424870467e-06, 'epoch': 0.53}

 27%|██▋       | 103/388 [16:47<49:01, 10.32s/it]
 27%|██▋       | 104/388 [16:57<48:00, 10.14s/it]
                                                 
{'loss': 0.5636, 'grad_norm': 2.3028645515441895, 'learning_rate': 7.383419689119171e-06, 'epoch': 0.54}

 27%|██▋       | 104/388 [16:57<48:00, 10.14s/it]
 27%|██▋       | 105/388 [17:08<49:42, 10.54s/it]
                                                 
{'loss': 0.9669, 'grad_norm': 2.465442419052124, 'learning_rate': 7.357512953367876e-06, 'epoch': 0.54}

 27%|██▋       | 105/388 [17:08<49:42, 10.54s/it]
 27%|██▋       | 106/388 [17:16<45:44,  9.73s/it]
                                                 
{'loss': 0.515, 'grad_norm': 2.96199631690979, 'learning_rate': 7.331606217616582e-06, 'epoch': 0.55}

 27%|██▋       | 106/388 [17:16<45:44,  9.73s/it]
 28%|██▊       | 107/388 [17:26<46:24,  9.91s/it]
                                                 
{'loss': 0.6113, 'grad_norm': 2.0609958171844482, 'learning_rate': 7.305699481865286e-06, 'epoch': 0.55}

 28%|██▊       | 107/388 [17:26<46:24,  9.91s/it]
 28%|██▊       | 108/388 [17:35<44:29,  9.53s/it]
                                                 
{'loss': 0.7473, 'grad_norm': 2.623793840408325, 'learning_rate': 7.27979274611399e-06, 'epoch': 0.56}

 28%|██▊       | 108/388 [17:35<44:29,  9.53s/it]
 28%|██▊       | 109/388 [17:43<41:43,  8.97s/it]
                                                 
{'loss': 0.6019, 'grad_norm': 2.7164416313171387, 'learning_rate': 7.253886010362695e-06, 'epoch': 0.56}

 28%|██▊       | 109/388 [17:43<41:43,  8.97s/it]
 28%|██▊       | 110/388 [17:53<43:16,  9.34s/it]
                                                 
{'loss': 0.5818, 'grad_norm': 2.022935152053833, 'learning_rate': 7.2279792746113995e-06, 'epoch': 0.57}

 28%|██▊       | 110/388 [17:53<43:16,  9.34s/it]
 29%|██▊       | 111/388 [18:03<44:17,  9.59s/it]
                                                 
{'loss': 0.4846, 'grad_norm': 2.591858386993408, 'learning_rate': 7.2020725388601045e-06, 'epoch': 0.57}

 29%|██▊       | 111/388 [18:03<44:17,  9.59s/it]
 29%|██▉       | 112/388 [18:11<42:28,  9.23s/it]
                                                 
{'loss': 0.8831, 'grad_norm': 2.279107093811035, 'learning_rate': 7.176165803108809e-06, 'epoch': 0.58}

 29%|██▉       | 112/388 [18:12<42:28,  9.23s/it]
 29%|██▉       | 113/388 [18:20<40:42,  8.88s/it]
                                                 
{'loss': 0.7941, 'grad_norm': 2.376699924468994, 'learning_rate': 7.150259067357514e-06, 'epoch': 0.58}

 29%|██▉       | 113/388 [18:20<40:42,  8.88s/it]
 29%|██▉       | 114/388 [18:32<45:36,  9.99s/it]
                                                 
{'loss': 0.6602, 'grad_norm': 2.5077431201934814, 'learning_rate': 7.124352331606218e-06, 'epoch': 0.59}

 29%|██▉       | 114/388 [18:32<45:36,  9.99s/it]
 30%|██▉       | 115/388 [18:40<42:38,  9.37s/it]
                                                 
{'loss': 0.7356, 'grad_norm': 2.730398416519165, 'learning_rate': 7.098445595854922e-06, 'epoch': 0.59}

 30%|██▉       | 115/388 [18:40<42:38,  9.37s/it]
 30%|██▉       | 116/388 [18:48<40:02,  8.83s/it]
                                                 
{'loss': 0.5004, 'grad_norm': 2.6164727210998535, 'learning_rate': 7.072538860103627e-06, 'epoch': 0.6}

 30%|██▉       | 116/388 [18:48<40:02,  8.83s/it]
 30%|███       | 117/388 [18:57<40:23,  8.94s/it]
                                                 
{'loss': 0.7939, 'grad_norm': 2.6170222759246826, 'learning_rate': 7.0466321243523315e-06, 'epoch': 0.6}

 30%|███       | 117/388 [18:57<40:23,  8.94s/it]
 30%|███       | 118/388 [19:06<41:05,  9.13s/it]
                                                 
{'loss': 0.6828, 'grad_norm': 2.3522651195526123, 'learning_rate': 7.020725388601037e-06, 'epoch': 0.61}

 30%|███       | 118/388 [19:06<41:05,  9.13s/it]
 31%|███       | 119/388 [19:15<39:50,  8.89s/it]
                                                 
{'loss': 1.0209, 'grad_norm': 2.4204940795898438, 'learning_rate': 6.994818652849742e-06, 'epoch': 0.61}

 31%|███       | 119/388 [19:15<39:50,  8.89s/it]
 31%|███       | 120/388 [19:24<39:34,  8.86s/it]
                                                 
{'loss': 0.5828, 'grad_norm': 2.4277994632720947, 'learning_rate': 6.968911917098447e-06, 'epoch': 0.62}

 31%|███       | 120/388 [19:24<39:34,  8.86s/it]
 31%|███       | 121/388 [19:32<39:05,  8.79s/it]
                                                 
{'loss': 0.8186, 'grad_norm': 2.5694141387939453, 'learning_rate': 6.943005181347151e-06, 'epoch': 0.62}

 31%|███       | 121/388 [19:32<39:05,  8.79s/it]
 31%|███▏      | 122/388 [19:41<39:40,  8.95s/it]
                                                 
{'loss': 0.5802, 'grad_norm': 2.2575204372406006, 'learning_rate': 6.917098445595856e-06, 'epoch': 0.63}

 31%|███▏      | 122/388 [19:41<39:40,  8.95s/it]
 32%|███▏      | 123/388 [19:50<38:21,  8.68s/it]
                                                 
{'loss': 0.7907, 'grad_norm': 2.4539308547973633, 'learning_rate': 6.89119170984456e-06, 'epoch': 0.63}

 32%|███▏      | 123/388 [19:50<38:21,  8.68s/it]
 32%|███▏      | 124/388 [19:59<39:13,  8.91s/it]
                                                 
{'loss': 0.5764, 'grad_norm': 2.5156893730163574, 'learning_rate': 6.865284974093265e-06, 'epoch': 0.64}

 32%|███▏      | 124/388 [19:59<39:13,  8.91s/it]
 32%|███▏      | 125/388 [20:09<40:43,  9.29s/it]
                                                 
{'loss': 1.0345, 'grad_norm': 2.1608986854553223, 'learning_rate': 6.839378238341969e-06, 'epoch': 0.64}

 32%|███▏      | 125/388 [20:09<40:43,  9.29s/it]
 32%|███▏      | 126/388 [20:20<42:45,  9.79s/it]
                                                 
{'loss': 0.7751, 'grad_norm': 1.9052895307540894, 'learning_rate': 6.813471502590674e-06, 'epoch': 0.65}

 32%|███▏      | 126/388 [20:20<42:45,  9.79s/it]
 33%|███▎      | 127/388 [20:30<42:55,  9.87s/it]
                                                 
{'loss': 0.6016, 'grad_norm': 1.7114657163619995, 'learning_rate': 6.787564766839379e-06, 'epoch': 0.65}

 33%|███▎      | 127/388 [20:30<42:55,  9.87s/it]
 33%|███▎      | 128/388 [20:39<41:27,  9.57s/it]
                                                 
{'loss': 0.7309, 'grad_norm': 1.8957144021987915, 'learning_rate': 6.761658031088083e-06, 'epoch': 0.66}

 33%|███▎      | 128/388 [20:39<41:27,  9.57s/it]
 33%|███▎      | 129/388 [20:49<41:11,  9.54s/it]
                                                 
{'loss': 0.8768, 'grad_norm': 2.0205330848693848, 'learning_rate': 6.735751295336788e-06, 'epoch': 0.66}

 33%|███▎      | 129/388 [20:49<41:11,  9.54s/it]
 34%|███▎      | 130/388 [20:58<40:56,  9.52s/it]
                                                 
{'loss': 0.6047, 'grad_norm': 2.091603994369507, 'learning_rate': 6.709844559585493e-06, 'epoch': 0.67}

 34%|███▎      | 130/388 [20:58<40:56,  9.52s/it]
 34%|███▍      | 131/388 [21:06<39:10,  9.15s/it]
                                                 
{'loss': 0.8586, 'grad_norm': 2.113060474395752, 'learning_rate': 6.683937823834198e-06, 'epoch': 0.68}

 34%|███▍      | 131/388 [21:06<39:10,  9.15s/it]
 34%|███▍      | 132/388 [21:14<37:50,  8.87s/it]
                                                 
{'loss': 1.0116, 'grad_norm': 2.217136859893799, 'learning_rate': 6.658031088082902e-06, 'epoch': 0.68}

 34%|███▍      | 132/388 [21:14<37:50,  8.87s/it]
 34%|███▍      | 133/388 [21:22<36:23,  8.56s/it]
                                                 
{'loss': 0.637, 'grad_norm': 2.951040744781494, 'learning_rate': 6.632124352331607e-06, 'epoch': 0.69}

 34%|███▍      | 133/388 [21:22<36:23,  8.56s/it]
 35%|███▍      | 134/388 [21:30<35:23,  8.36s/it]
                                                 
{'loss': 0.5337, 'grad_norm': 2.380998134613037, 'learning_rate': 6.6062176165803115e-06, 'epoch': 0.69}

 35%|███▍      | 134/388 [21:30<35:23,  8.36s/it]
 35%|███▍      | 135/388 [21:40<36:26,  8.64s/it]
                                                 
{'loss': 1.0006, 'grad_norm': 2.2579574584960938, 'learning_rate': 6.5803108808290166e-06, 'epoch': 0.7}

 35%|███▍      | 135/388 [21:40<36:26,  8.64s/it]
 35%|███▌      | 136/388 [21:48<36:35,  8.71s/it]
                                                 
{'loss': 0.7635, 'grad_norm': 2.230733633041382, 'learning_rate': 6.554404145077721e-06, 'epoch': 0.7}

 35%|███▌      | 136/388 [21:48<36:35,  8.71s/it]
 35%|███▌      | 137/388 [21:58<38:10,  9.13s/it]
                                                 
{'loss': 0.7646, 'grad_norm': 2.244591236114502, 'learning_rate': 6.528497409326425e-06, 'epoch': 0.71}

 35%|███▌      | 137/388 [21:58<38:10,  9.13s/it]
 36%|███▌      | 138/388 [22:08<39:01,  9.37s/it]
                                                 
{'loss': 0.7348, 'grad_norm': 2.008380651473999, 'learning_rate': 6.50259067357513e-06, 'epoch': 0.71}

 36%|███▌      | 138/388 [22:08<39:01,  9.37s/it]
 36%|███▌      | 139/388 [22:18<38:32,  9.29s/it]
                                                 
{'loss': 0.9412, 'grad_norm': 1.8554458618164062, 'learning_rate': 6.476683937823834e-06, 'epoch': 0.72}

 36%|███▌      | 139/388 [22:18<38:32,  9.29s/it]
 36%|███▌      | 140/388 [22:26<37:25,  9.06s/it]
                                                 
{'loss': 0.477, 'grad_norm': 1.9788316488265991, 'learning_rate': 6.450777202072539e-06, 'epoch': 0.72}

 36%|███▌      | 140/388 [22:26<37:25,  9.06s/it]
 36%|███▋      | 141/388 [22:37<39:10,  9.52s/it]
                                                 
{'loss': 1.0519, 'grad_norm': 2.246572732925415, 'learning_rate': 6.4248704663212435e-06, 'epoch': 0.73}

 36%|███▋      | 141/388 [22:37<39:10,  9.52s/it]
 37%|███▋      | 142/388 [22:44<36:32,  8.91s/it]
                                                 
{'loss': 0.6619, 'grad_norm': 2.317051410675049, 'learning_rate': 6.398963730569949e-06, 'epoch': 0.73}

 37%|███▋      | 142/388 [22:44<36:32,  8.91s/it]
 37%|███▋      | 143/388 [22:56<40:34,  9.94s/it]
                                                 
{'loss': 0.9211, 'grad_norm': 2.367633581161499, 'learning_rate': 6.373056994818654e-06, 'epoch': 0.74}

 37%|███▋      | 143/388 [22:56<40:34,  9.94s/it]
 37%|███▋      | 144/388 [23:05<38:07,  9.37s/it]
                                                 
{'loss': 0.6537, 'grad_norm': 2.689427375793457, 'learning_rate': 6.347150259067359e-06, 'epoch': 0.74}

 37%|███▋      | 144/388 [23:05<38:07,  9.37s/it]
 37%|███▋      | 145/388 [23:15<38:57,  9.62s/it]
                                                 
{'loss': 0.6335, 'grad_norm': 2.0595192909240723, 'learning_rate': 6.321243523316063e-06, 'epoch': 0.75}

 37%|███▋      | 145/388 [23:15<38:57,  9.62s/it]
 38%|███▊      | 146/388 [23:23<37:06,  9.20s/it]
                                                 
{'loss': 1.0649, 'grad_norm': 2.8765666484832764, 'learning_rate': 6.295336787564768e-06, 'epoch': 0.75}

 38%|███▊      | 146/388 [23:23<37:06,  9.20s/it]
 38%|███▊      | 147/388 [23:32<36:18,  9.04s/it]
                                                 
{'loss': 0.7946, 'grad_norm': 2.6361300945281982, 'learning_rate': 6.269430051813472e-06, 'epoch': 0.76}

 38%|███▊      | 147/388 [23:32<36:18,  9.04s/it]
 38%|███▊      | 148/388 [23:41<36:56,  9.23s/it]
                                                 
{'loss': 0.6266, 'grad_norm': 2.265388011932373, 'learning_rate': 6.243523316062176e-06, 'epoch': 0.76}

 38%|███▊      | 148/388 [23:41<36:56,  9.23s/it]
 38%|███▊      | 149/388 [23:51<37:55,  9.52s/it]
                                                 
{'loss': 0.6959, 'grad_norm': 2.150740623474121, 'learning_rate': 6.217616580310881e-06, 'epoch': 0.77}

 38%|███▊      | 149/388 [23:51<37:55,  9.52s/it]
 39%|███▊      | 150/388 [24:01<37:36,  9.48s/it]
                                                 
{'loss': 1.1691, 'grad_norm': 2.1308672428131104, 'learning_rate': 6.191709844559586e-06, 'epoch': 0.77}

 39%|███▊      | 150/388 [24:01<37:36,  9.48s/it]
 39%|███▉      | 151/388 [24:10<37:38,  9.53s/it]
                                                 
{'loss': 0.7522, 'grad_norm': 2.356973171234131, 'learning_rate': 6.165803108808291e-06, 'epoch': 0.78}

 39%|███▉      | 151/388 [24:10<37:38,  9.53s/it]
 39%|███▉      | 152/388 [24:19<36:43,  9.34s/it]
                                                 
{'loss': 0.7463, 'grad_norm': 1.7162338495254517, 'learning_rate': 6.139896373056995e-06, 'epoch': 0.78}

 39%|███▉      | 152/388 [24:19<36:43,  9.34s/it]
 39%|███▉      | 153/388 [24:29<36:27,  9.31s/it]
                                                 
{'loss': 0.8641, 'grad_norm': 2.0620827674865723, 'learning_rate': 6.113989637305699e-06, 'epoch': 0.79}

 39%|███▉      | 153/388 [24:29<36:27,  9.31s/it]
 40%|███▉      | 154/388 [24:39<37:22,  9.58s/it]
                                                 
{'loss': 0.8537, 'grad_norm': 2.4712204933166504, 'learning_rate': 6.088082901554405e-06, 'epoch': 0.79}

 40%|███▉      | 154/388 [24:39<37:22,  9.58s/it]
 40%|███▉      | 155/388 [24:50<39:27, 10.16s/it]
                                                 
{'loss': 0.6013, 'grad_norm': 1.9165736436843872, 'learning_rate': 6.06217616580311e-06, 'epoch': 0.8}

 40%|███▉      | 155/388 [24:50<39:27, 10.16s/it]
 40%|████      | 156/388 [24:59<37:40,  9.74s/it]
                                                 
{'loss': 0.8943, 'grad_norm': 2.1998610496520996, 'learning_rate': 6.036269430051814e-06, 'epoch': 0.8}

 40%|████      | 156/388 [24:59<37:40,  9.74s/it]
 40%|████      | 157/388 [25:10<38:50, 10.09s/it]
                                                 
{'loss': 0.6887, 'grad_norm': 1.972203254699707, 'learning_rate': 6.0103626943005185e-06, 'epoch': 0.81}

 40%|████      | 157/388 [25:10<38:50, 10.09s/it]
 41%|████      | 158/388 [25:18<36:26,  9.51s/it]
                                                 
{'loss': 0.5238, 'grad_norm': 2.0836496353149414, 'learning_rate': 5.9844559585492235e-06, 'epoch': 0.81}

 41%|████      | 158/388 [25:18<36:26,  9.51s/it]
 41%|████      | 159/388 [25:27<35:51,  9.40s/it]
                                                 
{'loss': 0.7665, 'grad_norm': 2.111018419265747, 'learning_rate': 5.958549222797928e-06, 'epoch': 0.82}

 41%|████      | 159/388 [25:27<35:51,  9.40s/it]
 41%|████      | 160/388 [25:39<38:20, 10.09s/it]
                                                 
{'loss': 0.6519, 'grad_norm': 1.956825613975525, 'learning_rate': 5.932642487046633e-06, 'epoch': 0.82}

 41%|████      | 160/388 [25:39<38:20, 10.09s/it]
 41%|████▏     | 161/388 [25:47<36:07,  9.55s/it]
                                                 
{'loss': 0.5579, 'grad_norm': 2.002511501312256, 'learning_rate': 5.906735751295337e-06, 'epoch': 0.83}

 41%|████▏     | 161/388 [25:47<36:07,  9.55s/it]
 42%|████▏     | 162/388 [25:55<33:43,  8.96s/it]
                                                 
{'loss': 0.4828, 'grad_norm': 2.447082996368408, 'learning_rate': 5.880829015544042e-06, 'epoch': 0.84}

 42%|████▏     | 162/388 [25:55<33:43,  8.96s/it]
 42%|████▏     | 163/388 [26:03<32:15,  8.60s/it]
                                                 
{'loss': 0.4924, 'grad_norm': 2.4028160572052, 'learning_rate': 5.854922279792746e-06, 'epoch': 0.84}

 42%|████▏     | 163/388 [26:03<32:15,  8.60s/it]
 42%|████▏     | 164/388 [26:11<32:16,  8.64s/it]
                                                 
{'loss': 0.6621, 'grad_norm': 2.1808340549468994, 'learning_rate': 5.8290155440414505e-06, 'epoch': 0.85}

 42%|████▏     | 164/388 [26:11<32:16,  8.64s/it]
 43%|████▎     | 165/388 [26:21<33:06,  8.91s/it]
                                                 
{'loss': 0.8003, 'grad_norm': 2.14582896232605, 'learning_rate': 5.8031088082901555e-06, 'epoch': 0.85}

 43%|████▎     | 165/388 [26:21<33:06,  8.91s/it]
 43%|████▎     | 166/388 [26:33<36:36,  9.90s/it]
                                                 
{'loss': 0.7265, 'grad_norm': 2.1482536792755127, 'learning_rate': 5.7772020725388614e-06, 'epoch': 0.86}

 43%|████▎     | 166/388 [26:33<36:36,  9.90s/it]
 43%|████▎     | 167/388 [26:43<36:09,  9.82s/it]
                                                 
{'loss': 1.1708, 'grad_norm': 2.4824554920196533, 'learning_rate': 5.751295336787566e-06, 'epoch': 0.86}

 43%|████▎     | 167/388 [26:43<36:09,  9.82s/it]
 43%|████▎     | 168/388 [26:53<36:28,  9.95s/it]
                                                 
{'loss': 0.5824, 'grad_norm': 2.3513762950897217, 'learning_rate': 5.72538860103627e-06, 'epoch': 0.87}

 43%|████▎     | 168/388 [26:53<36:28,  9.95s/it]
 44%|████▎     | 169/388 [27:03<36:16,  9.94s/it]
                                                 
{'loss': 0.5978, 'grad_norm': 2.6317238807678223, 'learning_rate': 5.699481865284975e-06, 'epoch': 0.87}

 44%|████▎     | 169/388 [27:03<36:16,  9.94s/it]
 44%|████▍     | 170/388 [27:13<36:30, 10.05s/it]
                                                 
{'loss': 0.6302, 'grad_norm': 2.019167900085449, 'learning_rate': 5.673575129533679e-06, 'epoch': 0.88}

 44%|████▍     | 170/388 [27:13<36:30, 10.05s/it]
 44%|████▍     | 171/388 [27:25<38:18, 10.59s/it]
                                                 
{'loss': 0.5126, 'grad_norm': 1.8639978170394897, 'learning_rate': 5.647668393782384e-06, 'epoch': 0.88}

 44%|████▍     | 171/388 [27:25<38:18, 10.59s/it]
 44%|████▍     | 172/388 [27:35<36:53, 10.25s/it]
                                                 
{'loss': 0.5394, 'grad_norm': 2.1784842014312744, 'learning_rate': 5.621761658031088e-06, 'epoch': 0.89}

 44%|████▍     | 172/388 [27:35<36:53, 10.25s/it]
 45%|████▍     | 173/388 [27:44<36:17, 10.13s/it]
                                                 
{'loss': 0.5318, 'grad_norm': 2.814495801925659, 'learning_rate': 5.5958549222797934e-06, 'epoch': 0.89}

 45%|████▍     | 173/388 [27:44<36:17, 10.13s/it]
 45%|████▍     | 174/388 [27:57<38:22, 10.76s/it]
                                                 
{'loss': 0.5904, 'grad_norm': 1.883297085762024, 'learning_rate': 5.569948186528498e-06, 'epoch': 0.9}

 45%|████▍     | 174/388 [27:57<38:22, 10.76s/it]
 45%|████▌     | 175/388 [28:08<38:55, 10.97s/it]
                                                 
{'loss': 0.9498, 'grad_norm': 2.2201955318450928, 'learning_rate': 5.544041450777202e-06, 'epoch': 0.9}

 45%|████▌     | 175/388 [28:08<38:55, 10.97s/it]
 45%|████▌     | 176/388 [28:19<39:04, 11.06s/it]
                                                 
{'loss': 0.8025, 'grad_norm': 2.072869062423706, 'learning_rate': 5.518134715025907e-06, 'epoch': 0.91}

 45%|████▌     | 176/388 [28:19<39:04, 11.06s/it]
 46%|████▌     | 177/388 [28:29<37:10, 10.57s/it]
                                                 
{'loss': 0.4942, 'grad_norm': 2.1868302822113037, 'learning_rate': 5.492227979274611e-06, 'epoch': 0.91}

 46%|████▌     | 177/388 [28:29<37:10, 10.57s/it]
 46%|████▌     | 178/388 [28:38<35:07, 10.04s/it]
                                                 
{'loss': 0.8148, 'grad_norm': 2.412132978439331, 'learning_rate': 5.466321243523317e-06, 'epoch': 0.92}

 46%|████▌     | 178/388 [28:38<35:07, 10.04s/it]
 46%|████▌     | 179/388 [28:47<34:22,  9.87s/it]
                                                 
{'loss': 0.8432, 'grad_norm': 2.23472261428833, 'learning_rate': 5.440414507772021e-06, 'epoch': 0.92}

 46%|████▌     | 179/388 [28:47<34:22,  9.87s/it]
 46%|████▋     | 180/388 [28:57<34:39, 10.00s/it]
                                                 
{'loss': 0.8168, 'grad_norm': 1.9256441593170166, 'learning_rate': 5.414507772020726e-06, 'epoch': 0.93}

 46%|████▋     | 180/388 [28:57<34:39, 10.00s/it]
 47%|████▋     | 181/388 [29:07<33:48,  9.80s/it]
                                                 
{'loss': 0.6928, 'grad_norm': 2.193864345550537, 'learning_rate': 5.3886010362694305e-06, 'epoch': 0.93}

 47%|████▋     | 181/388 [29:07<33:48,  9.80s/it]
 47%|████▋     | 182/388 [29:19<36:19, 10.58s/it]
                                                 
{'loss': 0.6571, 'grad_norm': 2.026597023010254, 'learning_rate': 5.3626943005181356e-06, 'epoch': 0.94}

 47%|████▋     | 182/388 [29:19<36:19, 10.58s/it]
 47%|████▋     | 183/388 [29:27<33:33,  9.82s/it]
                                                 
{'loss': 0.6279, 'grad_norm': 2.291626453399658, 'learning_rate': 5.33678756476684e-06, 'epoch': 0.94}

 47%|████▋     | 183/388 [29:27<33:33,  9.82s/it]
 47%|████▋     | 184/388 [29:37<33:05,  9.73s/it]
                                                 
{'loss': 0.6739, 'grad_norm': 1.8444104194641113, 'learning_rate': 5.310880829015545e-06, 'epoch': 0.95}

 47%|████▋     | 184/388 [29:37<33:05,  9.73s/it]
 48%|████▊     | 185/388 [29:48<34:13, 10.12s/it]
                                                 
{'loss': 1.0054, 'grad_norm': 2.1798253059387207, 'learning_rate': 5.284974093264249e-06, 'epoch': 0.95}

 48%|████▊     | 185/388 [29:48<34:13, 10.12s/it]
 48%|████▊     | 186/388 [29:57<33:46, 10.03s/it]
                                                 
{'loss': 0.9729, 'grad_norm': 2.4138057231903076, 'learning_rate': 5.259067357512953e-06, 'epoch': 0.96}

 48%|████▊     | 186/388 [29:57<33:46, 10.03s/it]
 48%|████▊     | 187/388 [30:11<36:36, 10.93s/it]
                                                 
{'loss': 0.6446, 'grad_norm': 2.078237771987915, 'learning_rate': 5.233160621761658e-06, 'epoch': 0.96}

 48%|████▊     | 187/388 [30:11<36:36, 10.93s/it]
 48%|████▊     | 188/388 [30:20<34:33, 10.37s/it]
                                                 
{'loss': 1.0359, 'grad_norm': 2.258807420730591, 'learning_rate': 5.2072538860103625e-06, 'epoch': 0.97}

 48%|████▊     | 188/388 [30:20<34:33, 10.37s/it]
 49%|████▊     | 189/388 [30:32<36:47, 11.09s/it]
                                                 
{'loss': 0.6271, 'grad_norm': 1.9247407913208008, 'learning_rate': 5.1813471502590676e-06, 'epoch': 0.97}

 49%|████▊     | 189/388 [30:32<36:47, 11.09s/it]
 49%|████▉     | 190/388 [30:42<34:53, 10.58s/it]
                                                 
{'loss': 0.9354, 'grad_norm': 2.414335012435913, 'learning_rate': 5.155440414507773e-06, 'epoch': 0.98}

 49%|████▉     | 190/388 [30:42<34:53, 10.58s/it]
 49%|████▉     | 191/388 [30:54<36:12, 11.03s/it]
                                                 
{'loss': 0.7737, 'grad_norm': 2.6068100929260254, 'learning_rate': 5.129533678756478e-06, 'epoch': 0.98}

 49%|████▉     | 191/388 [30:54<36:12, 11.03s/it]
 49%|████▉     | 192/388 [31:04<35:22, 10.83s/it]
                                                 
{'loss': 0.629, 'grad_norm': 2.1987404823303223, 'learning_rate': 5.103626943005182e-06, 'epoch': 0.99}

 49%|████▉     | 192/388 [31:04<35:22, 10.83s/it]
 50%|████▉     | 193/388 [31:13<33:19, 10.25s/it]
                                                 
{'loss': 0.664, 'grad_norm': 2.374802350997925, 'learning_rate': 5.077720207253887e-06, 'epoch': 0.99}

 50%|████▉     | 193/388 [31:13<33:19, 10.25s/it]
 50%|█████     | 194/388 [31:21<31:19,  9.69s/it]
                                                 
{'loss': 0.5583, 'grad_norm': 3.1563501358032227, 'learning_rate': 5.051813471502591e-06, 'epoch': 1.0}

 50%|█████     | 194/388 [31:21<31:19,  9.69s/it]
 50%|█████     | 195/388 [31:34<33:30, 10.42s/it]
                                                 
{'loss': 0.7121, 'grad_norm': 2.0836429595947266, 'learning_rate': 5.025906735751296e-06, 'epoch': 1.01}

 50%|█████     | 195/388 [31:34<33:30, 10.42s/it]
 51%|█████     | 196/388 [31:43<32:27, 10.14s/it]
                                                 
{'loss': 0.499, 'grad_norm': 2.0512752532958984, 'learning_rate': 5e-06, 'epoch': 1.01}

 51%|█████     | 196/388 [31:43<32:27, 10.14s/it]
 51%|█████     | 197/388 [31:53<32:31, 10.22s/it]
                                                 
{'loss': 0.3842, 'grad_norm': 1.8030757904052734, 'learning_rate': 4.974093264248705e-06, 'epoch': 1.02}

 51%|█████     | 197/388 [31:53<32:31, 10.22s/it]
 51%|█████     | 198/388 [32:04<32:53, 10.39s/it]
                                                 
{'loss': 0.5511, 'grad_norm': 2.8820579051971436, 'learning_rate': 4.94818652849741e-06, 'epoch': 1.02}

 51%|█████     | 198/388 [32:04<32:53, 10.39s/it]
 51%|█████▏    | 199/388 [32:14<32:14, 10.24s/it]
                                                 
{'loss': 0.5513, 'grad_norm': 2.530095100402832, 'learning_rate': 4.922279792746114e-06, 'epoch': 1.03}

 51%|█████▏    | 199/388 [32:14<32:14, 10.24s/it]
 52%|█████▏    | 200/388 [32:23<30:21,  9.69s/it]
                                                 
{'loss': 0.6154, 'grad_norm': 2.7577624320983887, 'learning_rate': 4.896373056994819e-06, 'epoch': 1.03}

 52%|█████▏    | 200/388 [32:23<30:21,  9.69s/it]
 52%|█████▏    | 201/388 [32:34<31:35, 10.14s/it]
                                                 
{'loss': 0.5226, 'grad_norm': 2.301081895828247, 'learning_rate': 4.870466321243524e-06, 'epoch': 1.04}

 52%|█████▏    | 201/388 [32:34<31:35, 10.14s/it]
 52%|█████▏    | 202/388 [32:43<30:53,  9.97s/it]
                                                 
{'loss': 0.4099, 'grad_norm': 2.4583969116210938, 'learning_rate': 4.844559585492228e-06, 'epoch': 1.04}

 52%|█████▏    | 202/388 [32:43<30:53,  9.97s/it]
 52%|█████▏    | 203/388 [32:54<31:48, 10.32s/it]
                                                 
{'loss': 0.6281, 'grad_norm': 2.238999366760254, 'learning_rate': 4.818652849740933e-06, 'epoch': 1.05}

 52%|█████▏    | 203/388 [32:54<31:48, 10.32s/it]
 53%|█████▎    | 204/388 [33:03<30:21,  9.90s/it]
                                                 
{'loss': 0.6042, 'grad_norm': 2.4057085514068604, 'learning_rate': 4.7927461139896375e-06, 'epoch': 1.05}

 53%|█████▎    | 204/388 [33:03<30:21,  9.90s/it]
 53%|█████▎    | 205/388 [33:17<33:19, 10.92s/it]
                                                 
{'loss': 0.6049, 'grad_norm': 2.283625364303589, 'learning_rate': 4.766839378238342e-06, 'epoch': 1.06}

 53%|█████▎    | 205/388 [33:17<33:19, 10.92s/it]
 53%|█████▎    | 206/388 [33:27<32:47, 10.81s/it]
                                                 
{'loss': 0.4113, 'grad_norm': 2.3003745079040527, 'learning_rate': 4.740932642487048e-06, 'epoch': 1.06}

 53%|█████▎    | 206/388 [33:27<32:47, 10.81s/it]
 53%|█████▎    | 207/388 [33:40<34:04, 11.30s/it]
                                                 
{'loss': 0.6683, 'grad_norm': 2.209561586380005, 'learning_rate': 4.715025906735752e-06, 'epoch': 1.07}

 53%|█████▎    | 207/388 [33:40<34:04, 11.30s/it]
 54%|█████▎    | 208/388 [33:50<32:47, 10.93s/it]
                                                 
{'loss': 0.4816, 'grad_norm': 1.7584269046783447, 'learning_rate': 4.689119170984456e-06, 'epoch': 1.07}

 54%|█████▎    | 208/388 [33:50<32:47, 10.93s/it]
 54%|█████▍    | 209/388 [33:59<31:08, 10.44s/it]
                                                 
{'loss': 0.4937, 'grad_norm': 2.759676218032837, 'learning_rate': 4.663212435233161e-06, 'epoch': 1.08}

 54%|█████▍    | 209/388 [33:59<31:08, 10.44s/it]
 54%|█████▍    | 210/388 [34:09<30:14, 10.20s/it]
                                                 
{'loss': 0.5405, 'grad_norm': 2.448197603225708, 'learning_rate': 4.637305699481865e-06, 'epoch': 1.08}

 54%|█████▍    | 210/388 [34:09<30:14, 10.20s/it]
 54%|█████▍    | 211/388 [34:17<28:40,  9.72s/it]
                                                 
{'loss': 0.5399, 'grad_norm': 2.5964267253875732, 'learning_rate': 4.61139896373057e-06, 'epoch': 1.09}

 54%|█████▍    | 211/388 [34:17<28:40,  9.72s/it]
 55%|█████▍    | 212/388 [34:26<27:18,  9.31s/it]
                                                 
{'loss': 0.2167, 'grad_norm': 2.8341469764709473, 'learning_rate': 4.585492227979275e-06, 'epoch': 1.09}

 55%|█████▍    | 212/388 [34:26<27:18,  9.31s/it]
 55%|█████▍    | 213/388 [34:36<28:19,  9.71s/it]
                                                 
{'loss': 0.5542, 'grad_norm': 4.033329486846924, 'learning_rate': 4.55958549222798e-06, 'epoch': 1.1}

 55%|█████▍    | 213/388 [34:36<28:19,  9.71s/it]
 55%|█████▌    | 214/388 [34:45<27:04,  9.34s/it]
                                                 
{'loss': 0.2834, 'grad_norm': 2.854736804962158, 'learning_rate': 4.533678756476685e-06, 'epoch': 1.1}

 55%|█████▌    | 214/388 [34:45<27:04,  9.34s/it]
 55%|█████▌    | 215/388 [34:54<27:01,  9.38s/it]
                                                 
{'loss': 0.4439, 'grad_norm': 2.79582142829895, 'learning_rate': 4.507772020725389e-06, 'epoch': 1.11}

 55%|█████▌    | 215/388 [34:54<27:01,  9.38s/it]
 56%|█████▌    | 216/388 [35:04<27:14,  9.50s/it]
                                                 
{'loss': 0.4934, 'grad_norm': 2.369055986404419, 'learning_rate': 4.481865284974093e-06, 'epoch': 1.11}

 56%|█████▌    | 216/388 [35:04<27:14,  9.50s/it]
 56%|█████▌    | 217/388 [35:14<27:47,  9.75s/it]
                                                 
{'loss': 0.6405, 'grad_norm': 2.2721002101898193, 'learning_rate': 4.455958549222798e-06, 'epoch': 1.12}

 56%|█████▌    | 217/388 [35:14<27:47,  9.75s/it]
 56%|█████▌    | 218/388 [35:24<27:55,  9.86s/it]
                                                 
{'loss': 0.6702, 'grad_norm': 2.5050008296966553, 'learning_rate': 4.430051813471503e-06, 'epoch': 1.12}

 56%|█████▌    | 218/388 [35:24<27:55,  9.86s/it]
 56%|█████▋    | 219/388 [35:33<26:51,  9.53s/it]
                                                 
{'loss': 0.4025, 'grad_norm': 2.191049575805664, 'learning_rate': 4.404145077720207e-06, 'epoch': 1.13}

 56%|█████▋    | 219/388 [35:33<26:51,  9.53s/it]
 57%|█████▋    | 220/388 [35:42<26:02,  9.30s/it]
                                                 
{'loss': 0.3161, 'grad_norm': 3.585730791091919, 'learning_rate': 4.3782383419689124e-06, 'epoch': 1.13}

 57%|█████▋    | 220/388 [35:42<26:02,  9.30s/it]
 57%|█████▋    | 221/388 [35:51<25:47,  9.27s/it]
                                                 
{'loss': 0.4529, 'grad_norm': 2.921163320541382, 'learning_rate': 4.352331606217617e-06, 'epoch': 1.14}

 57%|█████▋    | 221/388 [35:51<25:47,  9.27s/it]
 57%|█████▋    | 222/388 [36:02<27:15,  9.85s/it]
                                                 
{'loss': 0.3962, 'grad_norm': 2.3244917392730713, 'learning_rate': 4.326424870466322e-06, 'epoch': 1.14}

 57%|█████▋    | 222/388 [36:02<27:15,  9.85s/it]
 57%|█████▋    | 223/388 [36:12<26:54,  9.79s/it]
                                                 
{'loss': 0.3131, 'grad_norm': 2.107811212539673, 'learning_rate': 4.300518134715026e-06, 'epoch': 1.15}

 57%|█████▋    | 223/388 [36:12<26:54,  9.79s/it]
 58%|█████▊    | 224/388 [36:24<28:54, 10.58s/it]
                                                 
{'loss': 0.5665, 'grad_norm': 1.9965029954910278, 'learning_rate': 4.274611398963731e-06, 'epoch': 1.15}

 58%|█████▊    | 224/388 [36:24<28:54, 10.58s/it]
 58%|█████▊    | 225/388 [36:34<28:03, 10.33s/it]
                                                 
{'loss': 0.7093, 'grad_norm': 2.1175429821014404, 'learning_rate': 4.248704663212436e-06, 'epoch': 1.16}

 58%|█████▊    | 225/388 [36:34<28:03, 10.33s/it]
 58%|█████▊    | 226/388 [36:44<27:29, 10.18s/it]
                                                 
{'loss': 0.4905, 'grad_norm': 2.3432583808898926, 'learning_rate': 4.22279792746114e-06, 'epoch': 1.16}

 58%|█████▊    | 226/388 [36:44<27:29, 10.18s/it]
 59%|█████▊    | 227/388 [36:53<26:44,  9.97s/it]
                                                 
{'loss': 0.7729, 'grad_norm': 2.5018038749694824, 'learning_rate': 4.1968911917098444e-06, 'epoch': 1.17}

 59%|█████▊    | 227/388 [36:53<26:44,  9.97s/it]
 59%|█████▉    | 228/388 [37:03<26:28,  9.93s/it]
                                                 
{'loss': 0.6847, 'grad_norm': 2.631490707397461, 'learning_rate': 4.1709844559585495e-06, 'epoch': 1.18}

 59%|█████▉    | 228/388 [37:03<26:28,  9.93s/it]
 59%|█████▉    | 229/388 [37:15<27:27, 10.36s/it]
                                                 
{'loss': 0.551, 'grad_norm': 2.2843759059906006, 'learning_rate': 4.145077720207254e-06, 'epoch': 1.18}

 59%|█████▉    | 229/388 [37:15<27:27, 10.36s/it]
 59%|█████▉    | 230/388 [37:24<26:12,  9.95s/it]
                                                 
{'loss': 0.7523, 'grad_norm': 2.279371976852417, 'learning_rate': 4.119170984455959e-06, 'epoch': 1.19}

 59%|█████▉    | 230/388 [37:24<26:12,  9.95s/it]
 60%|█████▉    | 231/388 [37:33<25:52,  9.89s/it]
                                                 
{'loss': 0.3825, 'grad_norm': 2.154730796813965, 'learning_rate': 4.093264248704664e-06, 'epoch': 1.19}

 60%|█████▉    | 231/388 [37:33<25:52,  9.89s/it]
 60%|█████▉    | 232/388 [37:44<26:03, 10.02s/it]
                                                 
{'loss': 0.6958, 'grad_norm': 2.1635422706604004, 'learning_rate': 4.067357512953368e-06, 'epoch': 1.2}

 60%|█████▉    | 232/388 [37:44<26:03, 10.02s/it]
 60%|██████    | 233/388 [37:54<26:08, 10.12s/it]
                                                 
{'loss': 0.4847, 'grad_norm': 2.636568069458008, 'learning_rate': 4.041450777202073e-06, 'epoch': 1.2}

 60%|██████    | 233/388 [37:54<26:08, 10.12s/it]
 60%|██████    | 234/388 [38:05<26:44, 10.42s/it]
                                                 
{'loss': 0.6932, 'grad_norm': 2.0792791843414307, 'learning_rate': 4.015544041450777e-06, 'epoch': 1.21}

 60%|██████    | 234/388 [38:05<26:44, 10.42s/it]
 61%|██████    | 235/388 [38:15<26:15, 10.30s/it]
                                                 
{'loss': 0.7191, 'grad_norm': 2.0182125568389893, 'learning_rate': 3.989637305699482e-06, 'epoch': 1.21}

 61%|██████    | 235/388 [38:15<26:15, 10.30s/it]
 61%|██████    | 236/388 [38:25<25:42, 10.15s/it]
                                                 
{'loss': 0.3539, 'grad_norm': 2.325082302093506, 'learning_rate': 3.963730569948187e-06, 'epoch': 1.22}

 61%|██████    | 236/388 [38:25<25:42, 10.15s/it]
 61%|██████    | 237/388 [38:35<25:45, 10.23s/it]
                                                 
{'loss': 0.6365, 'grad_norm': 2.777247905731201, 'learning_rate': 3.937823834196892e-06, 'epoch': 1.22}

 61%|██████    | 237/388 [38:35<25:45, 10.23s/it]
 61%|██████▏   | 238/388 [38:45<24:51,  9.94s/it]
                                                 
{'loss': 0.357, 'grad_norm': 2.4695026874542236, 'learning_rate': 3.911917098445596e-06, 'epoch': 1.23}

 61%|██████▏   | 238/388 [38:45<24:51,  9.94s/it]
 62%|██████▏   | 239/388 [38:56<25:30, 10.27s/it]
                                                 
{'loss': 0.5525, 'grad_norm': 2.4125864505767822, 'learning_rate': 3.886010362694301e-06, 'epoch': 1.23}

 62%|██████▏   | 239/388 [38:56<25:30, 10.27s/it]
 62%|██████▏   | 240/388 [39:08<26:35, 10.78s/it]
                                                 
{'loss': 0.5571, 'grad_norm': 2.771406650543213, 'learning_rate': 3.860103626943005e-06, 'epoch': 1.24}

 62%|██████▏   | 240/388 [39:08<26:35, 10.78s/it]
 62%|██████▏   | 241/388 [39:17<25:27, 10.39s/it]
                                                 
{'loss': 0.369, 'grad_norm': 2.086074113845825, 'learning_rate': 3.83419689119171e-06, 'epoch': 1.24}

 62%|██████▏   | 241/388 [39:17<25:27, 10.39s/it]
 62%|██████▏   | 242/388 [39:27<24:40, 10.14s/it]
                                                 
{'loss': 0.4846, 'grad_norm': 2.6102590560913086, 'learning_rate': 3.808290155440415e-06, 'epoch': 1.25}

 62%|██████▏   | 242/388 [39:27<24:40, 10.14s/it]
 63%|██████▎   | 243/388 [39:40<26:23, 10.92s/it]
                                                 
{'loss': 0.3644, 'grad_norm': 2.324188232421875, 'learning_rate': 3.7823834196891194e-06, 'epoch': 1.25}

 63%|██████▎   | 243/388 [39:40<26:23, 10.92s/it]
 63%|██████▎   | 244/388 [39:49<25:09, 10.48s/it]
                                                 
{'loss': 0.6624, 'grad_norm': 2.483508348464966, 'learning_rate': 3.756476683937824e-06, 'epoch': 1.26}

 63%|██████▎   | 244/388 [39:49<25:09, 10.48s/it]
 63%|██████▎   | 245/388 [40:00<25:02, 10.51s/it]
                                                 
{'loss': 0.4537, 'grad_norm': 2.391200542449951, 'learning_rate': 3.7305699481865287e-06, 'epoch': 1.26}

 63%|██████▎   | 245/388 [40:00<25:02, 10.51s/it]
 63%|██████▎   | 246/388 [40:10<24:47, 10.47s/it]
                                                 
{'loss': 0.5079, 'grad_norm': 2.1135330200195312, 'learning_rate': 3.7046632124352333e-06, 'epoch': 1.27}

 63%|██████▎   | 246/388 [40:10<24:47, 10.47s/it]
 64%|██████▎   | 247/388 [40:22<25:58, 11.05s/it]
                                                 
{'loss': 0.2855, 'grad_norm': 2.4372682571411133, 'learning_rate': 3.678756476683938e-06, 'epoch': 1.27}

 64%|██████▎   | 247/388 [40:22<25:58, 11.05s/it]
 64%|██████▍   | 248/388 [40:32<24:45, 10.61s/it]
                                                 
{'loss': 0.4011, 'grad_norm': 1.8742483854293823, 'learning_rate': 3.652849740932643e-06, 'epoch': 1.28}

 64%|██████▍   | 248/388 [40:32<24:45, 10.61s/it]
 64%|██████▍   | 249/388 [40:43<24:40, 10.65s/it]
                                                 
{'loss': 0.5521, 'grad_norm': 1.9814589023590088, 'learning_rate': 3.6269430051813476e-06, 'epoch': 1.28}

 64%|██████▍   | 249/388 [40:43<24:40, 10.65s/it]
 64%|██████▍   | 250/388 [40:53<24:13, 10.53s/it]
                                                 
{'loss': 0.5789, 'grad_norm': 2.020714044570923, 'learning_rate': 3.6010362694300523e-06, 'epoch': 1.29}

 64%|██████▍   | 250/388 [40:53<24:13, 10.53s/it]
 65%|██████▍   | 251/388 [41:02<22:53, 10.03s/it]
                                                 
{'loss': 0.5045, 'grad_norm': 2.4552905559539795, 'learning_rate': 3.575129533678757e-06, 'epoch': 1.29}

 65%|██████▍   | 251/388 [41:02<22:53, 10.03s/it]
 65%|██████▍   | 252/388 [41:14<24:23, 10.76s/it]
                                                 
{'loss': 0.4487, 'grad_norm': 2.143057346343994, 'learning_rate': 3.549222797927461e-06, 'epoch': 1.3}

 65%|██████▍   | 252/388 [41:14<24:23, 10.76s/it]
 65%|██████▌   | 253/388 [41:25<24:17, 10.80s/it]
                                                 
{'loss': 0.562, 'grad_norm': 2.016857147216797, 'learning_rate': 3.5233160621761657e-06, 'epoch': 1.3}

 65%|██████▌   | 253/388 [41:25<24:17, 10.80s/it]
 65%|██████▌   | 254/388 [41:34<22:55, 10.27s/it]
                                                 
{'loss': 0.9041, 'grad_norm': 2.293997287750244, 'learning_rate': 3.497409326424871e-06, 'epoch': 1.31}

 65%|██████▌   | 254/388 [41:34<22:55, 10.27s/it]
 66%|██████▌   | 255/388 [41:46<23:57, 10.81s/it]
                                                 
{'loss': 0.4674, 'grad_norm': 2.3336353302001953, 'learning_rate': 3.4715025906735754e-06, 'epoch': 1.31}

 66%|██████▌   | 255/388 [41:46<23:57, 10.81s/it]
 66%|██████▌   | 256/388 [41:57<23:48, 10.82s/it]
                                                 
{'loss': 0.3567, 'grad_norm': 1.8112561702728271, 'learning_rate': 3.44559585492228e-06, 'epoch': 1.32}

 66%|██████▌   | 256/388 [41:57<23:48, 10.82s/it]
 66%|██████▌   | 257/388 [42:06<22:36, 10.36s/it]
                                                 
{'loss': 0.3736, 'grad_norm': 2.2844161987304688, 'learning_rate': 3.4196891191709847e-06, 'epoch': 1.32}

 66%|██████▌   | 257/388 [42:06<22:36, 10.36s/it]
 66%|██████▋   | 258/388 [42:16<22:08, 10.22s/it]
                                                 
{'loss': 0.4566, 'grad_norm': 2.512300968170166, 'learning_rate': 3.3937823834196893e-06, 'epoch': 1.33}

 66%|██████▋   | 258/388 [42:16<22:08, 10.22s/it]
 67%|██████▋   | 259/388 [42:25<21:10,  9.85s/it]
                                                 
{'loss': 0.289, 'grad_norm': 2.7513668537139893, 'learning_rate': 3.367875647668394e-06, 'epoch': 1.34}

 67%|██████▋   | 259/388 [42:25<21:10,  9.85s/it]
 67%|██████▋   | 260/388 [42:36<21:44, 10.19s/it]
                                                 
{'loss': 0.5362, 'grad_norm': 2.3287911415100098, 'learning_rate': 3.341968911917099e-06, 'epoch': 1.34}

 67%|██████▋   | 260/388 [42:36<21:44, 10.19s/it]
 67%|██████▋   | 261/388 [42:46<21:20, 10.08s/it]
                                                 
{'loss': 0.3982, 'grad_norm': 2.460958242416382, 'learning_rate': 3.3160621761658036e-06, 'epoch': 1.35}

 67%|██████▋   | 261/388 [42:46<21:20, 10.08s/it]
 68%|██████▊   | 262/388 [42:58<22:02, 10.50s/it]
                                                 
{'loss': 0.6603, 'grad_norm': 2.2614171504974365, 'learning_rate': 3.2901554404145083e-06, 'epoch': 1.35}

 68%|██████▊   | 262/388 [42:58<22:02, 10.50s/it]
 68%|██████▊   | 263/388 [43:09<22:15, 10.68s/it]
                                                 
{'loss': 0.3947, 'grad_norm': 2.1517562866210938, 'learning_rate': 3.2642487046632125e-06, 'epoch': 1.36}

 68%|██████▊   | 263/388 [43:09<22:15, 10.68s/it]
 68%|██████▊   | 264/388 [43:19<22:04, 10.68s/it]
                                                 
{'loss': 0.3566, 'grad_norm': 2.6794075965881348, 'learning_rate': 3.238341968911917e-06, 'epoch': 1.36}

 68%|██████▊   | 264/388 [43:19<22:04, 10.68s/it]
 68%|██████▊   | 265/388 [43:29<21:31, 10.50s/it]
                                                 
{'loss': 0.3596, 'grad_norm': 2.277747869491577, 'learning_rate': 3.2124352331606218e-06, 'epoch': 1.37}

 68%|██████▊   | 265/388 [43:29<21:31, 10.50s/it]
 69%|██████▊   | 266/388 [43:41<21:48, 10.73s/it]
                                                 
{'loss': 0.5912, 'grad_norm': 2.5137126445770264, 'learning_rate': 3.186528497409327e-06, 'epoch': 1.37}

 69%|██████▊   | 266/388 [43:41<21:48, 10.73s/it][2025-07-08 19:44:00,590] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time

 69%|██████▉   | 267/388 [43:55<23:50, 11.82s/it]
                                                 
{'loss': 0.4655, 'grad_norm': 2.1888692378997803, 'learning_rate': 3.1606217616580314e-06, 'epoch': 1.38}

 69%|██████▉   | 267/388 [43:55<23:50, 11.82s/it]
 69%|██████▉   | 268/388 [44:06<23:20, 11.67s/it]
                                                 
{'loss': 0.576, 'grad_norm': 2.2129290103912354, 'learning_rate': 3.134715025906736e-06, 'epoch': 1.38}

 69%|██████▉   | 268/388 [44:06<23:20, 11.67s/it]
 69%|██████▉   | 269/388 [44:18<22:56, 11.57s/it]
                                                 
{'loss': 0.355, 'grad_norm': 3.1906845569610596, 'learning_rate': 3.1088082901554407e-06, 'epoch': 1.39}

 69%|██████▉   | 269/388 [44:18<22:56, 11.57s/it]
 70%|██████▉   | 270/388 [44:29<22:44, 11.56s/it]
                                                 
{'loss': 0.3613, 'grad_norm': 2.2897989749908447, 'learning_rate': 3.0829015544041453e-06, 'epoch': 1.39}

 70%|██████▉   | 270/388 [44:29<22:44, 11.56s/it]
 70%|██████▉   | 271/388 [44:40<21:53, 11.23s/it]
                                                 
{'loss': 0.4327, 'grad_norm': 2.437657356262207, 'learning_rate': 3.0569948186528495e-06, 'epoch': 1.4}

 70%|██████▉   | 271/388 [44:40<21:53, 11.23s/it]
 70%|███████   | 272/388 [44:49<20:34, 10.65s/it]
                                                 
{'loss': 0.4898, 'grad_norm': 2.3233554363250732, 'learning_rate': 3.031088082901555e-06, 'epoch': 1.4}

 70%|███████   | 272/388 [44:49<20:34, 10.65s/it]
 70%|███████   | 273/388 [44:59<20:09, 10.51s/it]
                                                 
{'loss': 0.3231, 'grad_norm': 2.113227367401123, 'learning_rate': 3.0051813471502592e-06, 'epoch': 1.41}

 70%|███████   | 273/388 [44:59<20:09, 10.51s/it]
 71%|███████   | 274/388 [45:12<21:20, 11.23s/it]
                                                 
{'loss': 0.3608, 'grad_norm': 2.2778213024139404, 'learning_rate': 2.979274611398964e-06, 'epoch': 1.41}

 71%|███████   | 274/388 [45:12<21:20, 11.23s/it]
 71%|███████   | 275/388 [45:25<22:23, 11.89s/it]
                                                 
{'loss': 0.5681, 'grad_norm': 1.8296815156936646, 'learning_rate': 2.9533678756476685e-06, 'epoch': 1.42}

 71%|███████   | 275/388 [45:25<22:23, 11.89s/it]
 71%|███████   | 276/388 [45:36<21:38, 11.59s/it]
                                                 
{'loss': 0.6308, 'grad_norm': 1.8510407209396362, 'learning_rate': 2.927461139896373e-06, 'epoch': 1.42}

 71%|███████   | 276/388 [45:36<21:38, 11.59s/it]
 71%|███████▏  | 277/388 [45:47<21:07, 11.42s/it]
                                                 
{'loss': 0.3525, 'grad_norm': 2.0309178829193115, 'learning_rate': 2.9015544041450778e-06, 'epoch': 1.43}

 71%|███████▏  | 277/388 [45:47<21:07, 11.42s/it]
 72%|███████▏  | 278/388 [45:59<21:17, 11.61s/it]
                                                 
{'loss': 0.7015, 'grad_norm': 2.1533703804016113, 'learning_rate': 2.875647668393783e-06, 'epoch': 1.43}

 72%|███████▏  | 278/388 [45:59<21:17, 11.61s/it]
 72%|███████▏  | 279/388 [46:12<21:38, 11.92s/it]
                                                 
{'loss': 0.5073, 'grad_norm': 2.123189687728882, 'learning_rate': 2.8497409326424875e-06, 'epoch': 1.44}

 72%|███████▏  | 279/388 [46:12<21:38, 11.92s/it]
 72%|███████▏  | 280/388 [46:25<21:52, 12.16s/it]
                                                 
{'loss': 0.5581, 'grad_norm': 1.8934084177017212, 'learning_rate': 2.823834196891192e-06, 'epoch': 1.44}

 72%|███████▏  | 280/388 [46:25<21:52, 12.16s/it]
 72%|███████▏  | 281/388 [46:36<21:25, 12.01s/it]
                                                 
{'loss': 0.4605, 'grad_norm': 2.3454744815826416, 'learning_rate': 2.7979274611398967e-06, 'epoch': 1.45}

 72%|███████▏  | 281/388 [46:36<21:25, 12.01s/it]
 73%|███████▎  | 282/388 [46:46<20:06, 11.38s/it]
                                                 
{'loss': 0.3607, 'grad_norm': 1.9981955289840698, 'learning_rate': 2.772020725388601e-06, 'epoch': 1.45}

 73%|███████▎  | 282/388 [46:46<20:06, 11.38s/it]
 73%|███████▎  | 283/388 [46:58<19:53, 11.37s/it]
                                                 
{'loss': 0.4104, 'grad_norm': 2.231193780899048, 'learning_rate': 2.7461139896373056e-06, 'epoch': 1.46}

 73%|███████▎  | 283/388 [46:58<19:53, 11.37s/it]
 73%|███████▎  | 284/388 [47:07<18:25, 10.63s/it]
                                                 
{'loss': 0.2536, 'grad_norm': 3.4163265228271484, 'learning_rate': 2.7202072538860106e-06, 'epoch': 1.46}

 73%|███████▎  | 284/388 [47:07<18:25, 10.63s/it]
 73%|███████▎  | 285/388 [47:19<19:20, 11.27s/it]
                                                 
{'loss': 0.3403, 'grad_norm': 2.154046058654785, 'learning_rate': 2.6943005181347152e-06, 'epoch': 1.47}

 73%|███████▎  | 285/388 [47:19<19:20, 11.27s/it]
 74%|███████▎  | 286/388 [47:31<19:28, 11.45s/it]
                                                 
{'loss': 0.6198, 'grad_norm': 2.1074230670928955, 'learning_rate': 2.66839378238342e-06, 'epoch': 1.47}

 74%|███████▎  | 286/388 [47:31<19:28, 11.45s/it]
 74%|███████▍  | 287/388 [47:44<20:02, 11.90s/it]
                                                 
{'loss': 0.5823, 'grad_norm': 2.559756278991699, 'learning_rate': 2.6424870466321245e-06, 'epoch': 1.48}

 74%|███████▍  | 287/388 [47:44<20:02, 11.90s/it]
 74%|███████▍  | 288/388 [47:55<19:06, 11.46s/it]
                                                 
{'loss': 0.8734, 'grad_norm': 2.415984869003296, 'learning_rate': 2.616580310880829e-06, 'epoch': 1.48}

 74%|███████▍  | 288/388 [47:55<19:06, 11.46s/it]
 74%|███████▍  | 289/388 [48:05<18:06, 10.98s/it]
                                                 
{'loss': 0.556, 'grad_norm': 2.7131714820861816, 'learning_rate': 2.5906735751295338e-06, 'epoch': 1.49}

 74%|███████▍  | 289/388 [48:05<18:06, 10.98s/it]
 75%|███████▍  | 290/388 [48:16<18:17, 11.19s/it]
                                                 
{'loss': 0.8368, 'grad_norm': 2.8106203079223633, 'learning_rate': 2.564766839378239e-06, 'epoch': 1.49}

 75%|███████▍  | 290/388 [48:16<18:17, 11.19s/it]
 75%|███████▌  | 291/388 [48:27<18:05, 11.19s/it]
                                                 
{'loss': 0.5506, 'grad_norm': 2.2179758548736572, 'learning_rate': 2.5388601036269435e-06, 'epoch': 1.5}

 75%|███████▌  | 291/388 [48:27<18:05, 11.19s/it]
 75%|███████▌  | 292/388 [48:41<19:08, 11.96s/it]
                                                 
{'loss': 0.4746, 'grad_norm': 1.8650314807891846, 'learning_rate': 2.512953367875648e-06, 'epoch': 1.51}

 75%|███████▌  | 292/388 [48:41<19:08, 11.96s/it]
 76%|███████▌  | 293/388 [48:54<19:16, 12.18s/it]
                                                 
{'loss': 0.3436, 'grad_norm': 2.1396875381469727, 'learning_rate': 2.4870466321243523e-06, 'epoch': 1.51}

 76%|███████▌  | 293/388 [48:54<19:16, 12.18s/it]
 76%|███████▌  | 294/388 [49:04<18:04, 11.54s/it]
                                                 
{'loss': 0.4165, 'grad_norm': 1.8841402530670166, 'learning_rate': 2.461139896373057e-06, 'epoch': 1.52}

 76%|███████▌  | 294/388 [49:04<18:04, 11.54s/it]
 76%|███████▌  | 295/388 [49:13<16:48, 10.85s/it]
                                                 
{'loss': 0.7539, 'grad_norm': 2.113678216934204, 'learning_rate': 2.435233160621762e-06, 'epoch': 1.52}

 76%|███████▌  | 295/388 [49:13<16:48, 10.85s/it]
 76%|███████▋  | 296/388 [49:24<16:36, 10.83s/it]
                                                 
{'loss': 0.4493, 'grad_norm': 2.5732593536376953, 'learning_rate': 2.4093264248704666e-06, 'epoch': 1.53}

 76%|███████▋  | 296/388 [49:24<16:36, 10.83s/it]
 77%|███████▋  | 297/388 [49:34<15:57, 10.53s/it]
                                                 
{'loss': 0.3467, 'grad_norm': 2.224625825881958, 'learning_rate': 2.383419689119171e-06, 'epoch': 1.53}

 77%|███████▋  | 297/388 [49:34<15:57, 10.53s/it]
 77%|███████▋  | 298/388 [49:43<15:06, 10.07s/it]
                                                 
{'loss': 0.4526, 'grad_norm': 2.6423981189727783, 'learning_rate': 2.357512953367876e-06, 'epoch': 1.54}

 77%|███████▋  | 298/388 [49:43<15:06, 10.07s/it]
 77%|███████▋  | 299/388 [49:55<15:42, 10.59s/it]
                                                 
{'loss': 0.3433, 'grad_norm': 1.8617727756500244, 'learning_rate': 2.3316062176165805e-06, 'epoch': 1.54}

 77%|███████▋  | 299/388 [49:55<15:42, 10.59s/it]
 77%|███████▋  | 300/388 [50:05<15:26, 10.53s/it]
                                                 
{'loss': 0.6761, 'grad_norm': 2.285628318786621, 'learning_rate': 2.305699481865285e-06, 'epoch': 1.55}

 77%|███████▋  | 300/388 [50:05<15:26, 10.53s/it]
 78%|███████▊  | 301/388 [50:15<14:54, 10.28s/it]
                                                 
{'loss': 0.6779, 'grad_norm': 2.2116644382476807, 'learning_rate': 2.27979274611399e-06, 'epoch': 1.55}

 78%|███████▊  | 301/388 [50:15<14:54, 10.28s/it]
 78%|███████▊  | 302/388 [50:25<14:51, 10.37s/it]
                                                 
{'loss': 0.321, 'grad_norm': 2.1227314472198486, 'learning_rate': 2.2538860103626944e-06, 'epoch': 1.56}

 78%|███████▊  | 302/388 [50:25<14:51, 10.37s/it]
 78%|███████▊  | 303/388 [50:35<14:27, 10.20s/it]
                                                 
{'loss': 0.4848, 'grad_norm': 2.27170991897583, 'learning_rate': 2.227979274611399e-06, 'epoch': 1.56}

 78%|███████▊  | 303/388 [50:35<14:27, 10.20s/it]
 78%|███████▊  | 304/388 [50:44<13:46,  9.84s/it]
                                                 
{'loss': 0.4081, 'grad_norm': 2.157994031906128, 'learning_rate': 2.2020725388601037e-06, 'epoch': 1.57}

 78%|███████▊  | 304/388 [50:44<13:46,  9.84s/it]
 79%|███████▊  | 305/388 [50:54<13:34,  9.81s/it]
                                                 
{'loss': 0.4548, 'grad_norm': 2.0860350131988525, 'learning_rate': 2.1761658031088083e-06, 'epoch': 1.57}

 79%|███████▊  | 305/388 [50:54<13:34,  9.81s/it]
 79%|███████▉  | 306/388 [51:04<13:43, 10.04s/it]
                                                 
{'loss': 0.4788, 'grad_norm': 2.903682231903076, 'learning_rate': 2.150259067357513e-06, 'epoch': 1.58}

 79%|███████▉  | 306/388 [51:04<13:43, 10.04s/it]
 79%|███████▉  | 307/388 [51:14<13:20,  9.88s/it]
                                                 
{'loss': 0.5734, 'grad_norm': 2.398547649383545, 'learning_rate': 2.124352331606218e-06, 'epoch': 1.58}

 79%|███████▉  | 307/388 [51:14<13:20,  9.88s/it]
 79%|███████▉  | 308/388 [51:23<12:52,  9.66s/it]
                                                 
{'loss': 0.446, 'grad_norm': 2.53910493850708, 'learning_rate': 2.0984455958549222e-06, 'epoch': 1.59}

 79%|███████▉  | 308/388 [51:23<12:52,  9.66s/it]
 80%|███████▉  | 309/388 [51:35<13:39, 10.38s/it]
                                                 
{'loss': 0.5528, 'grad_norm': 2.0708515644073486, 'learning_rate': 2.072538860103627e-06, 'epoch': 1.59}

 80%|███████▉  | 309/388 [51:35<13:39, 10.38s/it]
 80%|███████▉  | 310/388 [51:49<14:45, 11.35s/it]
                                                 
{'loss': 0.5587, 'grad_norm': 2.0458717346191406, 'learning_rate': 2.046632124352332e-06, 'epoch': 1.6}

 80%|███████▉  | 310/388 [51:49<14:45, 11.35s/it]
 80%|████████  | 311/388 [51:59<14:06, 10.99s/it]
                                                 
{'loss': 0.4448, 'grad_norm': 2.2896485328674316, 'learning_rate': 2.0207253886010365e-06, 'epoch': 1.6}

 80%|████████  | 311/388 [51:59<14:06, 10.99s/it]
 80%|████████  | 312/388 [52:09<13:27, 10.63s/it]
                                                 
{'loss': 0.7287, 'grad_norm': 2.3465869426727295, 'learning_rate': 1.994818652849741e-06, 'epoch': 1.61}

 80%|████████  | 312/388 [52:09<13:27, 10.63s/it]
 81%|████████  | 313/388 [52:21<13:51, 11.09s/it]
                                                 
{'loss': 0.423, 'grad_norm': 2.393112897872925, 'learning_rate': 1.968911917098446e-06, 'epoch': 1.61}

 81%|████████  | 313/388 [52:21<13:51, 11.09s/it]
 81%|████████  | 314/388 [52:32<13:40, 11.09s/it]
                                                 
{'loss': 0.7744, 'grad_norm': 2.5381312370300293, 'learning_rate': 1.9430051813471504e-06, 'epoch': 1.62}

 81%|████████  | 314/388 [52:32<13:40, 11.09s/it]
 81%|████████  | 315/388 [52:42<13:17, 10.92s/it]
                                                 
{'loss': 0.6686, 'grad_norm': 1.9643605947494507, 'learning_rate': 1.917098445595855e-06, 'epoch': 1.62}

 81%|████████  | 315/388 [52:42<13:17, 10.92s/it]
 81%|████████▏ | 316/388 [52:52<12:47, 10.65s/it]
                                                 
{'loss': 0.3454, 'grad_norm': 2.169778823852539, 'learning_rate': 1.8911917098445597e-06, 'epoch': 1.63}

 81%|████████▏ | 316/388 [52:52<12:47, 10.65s/it]
 82%|████████▏ | 317/388 [53:04<13:06, 11.08s/it]
                                                 
{'loss': 0.7417, 'grad_norm': 2.059842824935913, 'learning_rate': 1.8652849740932643e-06, 'epoch': 1.63}

 82%|████████▏ | 317/388 [53:04<13:06, 11.08s/it]
 82%|████████▏ | 318/388 [53:18<13:50, 11.86s/it]
                                                 
{'loss': 0.5585, 'grad_norm': 1.7974945306777954, 'learning_rate': 1.839378238341969e-06, 'epoch': 1.64}

 82%|████████▏ | 318/388 [53:18<13:50, 11.86s/it]
 82%|████████▏ | 319/388 [53:30<13:40, 11.90s/it]
                                                 
{'loss': 0.3245, 'grad_norm': 2.0723960399627686, 'learning_rate': 1.8134715025906738e-06, 'epoch': 1.64}

 82%|████████▏ | 319/388 [53:30<13:40, 11.90s/it]
 82%|████████▏ | 320/388 [53:43<13:48, 12.19s/it]
                                                 
{'loss': 0.6335, 'grad_norm': 1.7244951725006104, 'learning_rate': 1.7875647668393784e-06, 'epoch': 1.65}

 82%|████████▏ | 320/388 [53:43<13:48, 12.19s/it]
 83%|████████▎ | 321/388 [53:52<12:34, 11.26s/it]
                                                 
{'loss': 0.2948, 'grad_norm': 2.3602211475372314, 'learning_rate': 1.7616580310880829e-06, 'epoch': 1.65}

 83%|████████▎ | 321/388 [53:52<12:34, 11.26s/it]
 83%|████████▎ | 322/388 [54:03<12:25, 11.29s/it]
                                                 
{'loss': 0.4723, 'grad_norm': 2.367523670196533, 'learning_rate': 1.7357512953367877e-06, 'epoch': 1.66}

 83%|████████▎ | 322/388 [54:03<12:25, 11.29s/it]
 83%|████████▎ | 323/388 [54:17<12:55, 11.92s/it]
                                                 
{'loss': 0.7215, 'grad_norm': 1.9810079336166382, 'learning_rate': 1.7098445595854923e-06, 'epoch': 1.66}

 83%|████████▎ | 323/388 [54:17<12:55, 11.92s/it]
 84%|████████▎ | 324/388 [54:28<12:33, 11.78s/it]
                                                 
{'loss': 0.375, 'grad_norm': 2.228205919265747, 'learning_rate': 1.683937823834197e-06, 'epoch': 1.67}

 84%|████████▎ | 324/388 [54:28<12:33, 11.78s/it]
 84%|████████▍ | 325/388 [54:38<11:47, 11.22s/it]
                                                 
{'loss': 0.4098, 'grad_norm': 2.2735326290130615, 'learning_rate': 1.6580310880829018e-06, 'epoch': 1.68}

 84%|████████▍ | 325/388 [54:38<11:47, 11.22s/it]
 84%|████████▍ | 326/388 [54:48<11:11, 10.84s/it]
                                                 
{'loss': 0.4386, 'grad_norm': 2.075532913208008, 'learning_rate': 1.6321243523316062e-06, 'epoch': 1.68}

 84%|████████▍ | 326/388 [54:48<11:11, 10.84s/it]
 84%|████████▍ | 327/388 [54:59<10:57, 10.79s/it]
                                                 
{'loss': 0.4276, 'grad_norm': 1.7820451259613037, 'learning_rate': 1.6062176165803109e-06, 'epoch': 1.69}

 84%|████████▍ | 327/388 [54:59<10:57, 10.79s/it]
 85%|████████▍ | 328/388 [55:08<10:26, 10.45s/it]
                                                 
{'loss': 0.5588, 'grad_norm': 2.103607654571533, 'learning_rate': 1.5803108808290157e-06, 'epoch': 1.69}

 85%|████████▍ | 328/388 [55:08<10:26, 10.45s/it]
 85%|████████▍ | 329/388 [55:21<10:53, 11.08s/it]
                                                 
{'loss': 0.3427, 'grad_norm': 2.3997738361358643, 'learning_rate': 1.5544041450777204e-06, 'epoch': 1.7}

 85%|████████▍ | 329/388 [55:21<10:53, 11.08s/it]
 85%|████████▌ | 330/388 [55:31<10:30, 10.88s/it]
                                                 
{'loss': 0.3634, 'grad_norm': 1.9919472932815552, 'learning_rate': 1.5284974093264248e-06, 'epoch': 1.7}

 85%|████████▌ | 330/388 [55:31<10:30, 10.88s/it]
 85%|████████▌ | 331/388 [55:42<10:09, 10.69s/it]
                                                 
{'loss': 0.6021, 'grad_norm': 2.0764970779418945, 'learning_rate': 1.5025906735751296e-06, 'epoch': 1.71}

 85%|████████▌ | 331/388 [55:42<10:09, 10.69s/it]
 86%|████████▌ | 332/388 [55:53<10:08, 10.87s/it]
                                                 
{'loss': 0.2526, 'grad_norm': 2.3495702743530273, 'learning_rate': 1.4766839378238342e-06, 'epoch': 1.71}

 86%|████████▌ | 332/388 [55:53<10:08, 10.87s/it]
 86%|████████▌ | 333/388 [56:04<10:01, 10.94s/it]
                                                 
{'loss': 0.456, 'grad_norm': 2.2948033809661865, 'learning_rate': 1.4507772020725389e-06, 'epoch': 1.72}

 86%|████████▌ | 333/388 [56:04<10:01, 10.94s/it]
 86%|████████▌ | 334/388 [56:15<09:47, 10.88s/it]
                                                 
{'loss': 0.4439, 'grad_norm': 2.115891933441162, 'learning_rate': 1.4248704663212437e-06, 'epoch': 1.72}

 86%|████████▌ | 334/388 [56:15<09:47, 10.88s/it]
 86%|████████▋ | 335/388 [56:27<09:59, 11.31s/it]
                                                 
{'loss': 0.475, 'grad_norm': 2.1751835346221924, 'learning_rate': 1.3989637305699484e-06, 'epoch': 1.73}

 86%|████████▋ | 335/388 [56:27<09:59, 11.31s/it]
 87%|████████▋ | 336/388 [56:39<09:53, 11.41s/it]
                                                 
{'loss': 0.4867, 'grad_norm': 2.033535957336426, 'learning_rate': 1.3730569948186528e-06, 'epoch': 1.73}

 87%|████████▋ | 336/388 [56:39<09:53, 11.41s/it]
 87%|████████▋ | 337/388 [56:52<10:16, 12.08s/it]
                                                 
{'loss': 0.31, 'grad_norm': 2.0619421005249023, 'learning_rate': 1.3471502590673576e-06, 'epoch': 1.74}

 87%|████████▋ | 337/388 [56:52<10:16, 12.08s/it]
 87%|████████▋ | 338/388 [57:06<10:20, 12.41s/it]
                                                 
{'loss': 0.4221, 'grad_norm': 2.047929048538208, 'learning_rate': 1.3212435233160623e-06, 'epoch': 1.74}

 87%|████████▋ | 338/388 [57:06<10:20, 12.41s/it]
 87%|████████▋ | 339/388 [57:14<09:12, 11.28s/it]
                                                 
{'loss': 0.2444, 'grad_norm': 2.6819007396698, 'learning_rate': 1.2953367875647669e-06, 'epoch': 1.75}

 87%|████████▋ | 339/388 [57:14<09:12, 11.28s/it]
 88%|████████▊ | 340/388 [57:28<09:30, 11.89s/it]
                                                 
{'loss': 0.5839, 'grad_norm': 1.826359748840332, 'learning_rate': 1.2694300518134717e-06, 'epoch': 1.75}

 88%|████████▊ | 340/388 [57:28<09:30, 11.89s/it]
 88%|████████▊ | 341/388 [57:39<09:06, 11.63s/it]
                                                 
{'loss': 0.4928, 'grad_norm': 2.54584002494812, 'learning_rate': 1.2435233160621762e-06, 'epoch': 1.76}

 88%|████████▊ | 341/388 [57:39<09:06, 11.63s/it]
 88%|████████▊ | 342/388 [57:47<08:16, 10.80s/it]
                                                 
{'loss': 0.4494, 'grad_norm': 2.2615675926208496, 'learning_rate': 1.217616580310881e-06, 'epoch': 1.76}

 88%|████████▊ | 342/388 [57:47<08:16, 10.80s/it]
 88%|████████▊ | 343/388 [57:57<07:55, 10.56s/it]
                                                 
{'loss': 0.6856, 'grad_norm': 2.0922272205352783, 'learning_rate': 1.1917098445595854e-06, 'epoch': 1.77}

 88%|████████▊ | 343/388 [57:57<07:55, 10.56s/it]
 89%|████████▊ | 344/388 [58:10<08:12, 11.20s/it]
                                                 
{'loss': 0.4049, 'grad_norm': 2.0808823108673096, 'learning_rate': 1.1658031088082903e-06, 'epoch': 1.77}

 89%|████████▊ | 344/388 [58:10<08:12, 11.20s/it]
 89%|████████▉ | 345/388 [58:19<07:37, 10.64s/it]
                                                 
{'loss': 0.3212, 'grad_norm': 2.256464719772339, 'learning_rate': 1.139896373056995e-06, 'epoch': 1.78}

 89%|████████▉ | 345/388 [58:19<07:37, 10.64s/it]
 89%|████████▉ | 346/388 [58:31<07:35, 10.86s/it]
                                                 
{'loss': 0.8571, 'grad_norm': 2.229799509048462, 'learning_rate': 1.1139896373056995e-06, 'epoch': 1.78}

 89%|████████▉ | 346/388 [58:31<07:35, 10.86s/it]
 89%|████████▉ | 347/388 [58:42<07:22, 10.80s/it]
                                                 
{'loss': 0.6851, 'grad_norm': 2.236447811126709, 'learning_rate': 1.0880829015544042e-06, 'epoch': 1.79}

 89%|████████▉ | 347/388 [58:42<07:22, 10.80s/it]
 90%|████████▉ | 348/388 [58:51<06:54, 10.37s/it]
                                                 
{'loss': 0.3286, 'grad_norm': 2.494565486907959, 'learning_rate': 1.062176165803109e-06, 'epoch': 1.79}

 90%|████████▉ | 348/388 [58:51<06:54, 10.37s/it]
 90%|████████▉ | 349/388 [59:03<07:03, 10.86s/it]
                                                 
{'loss': 0.6564, 'grad_norm': 1.8351247310638428, 'learning_rate': 1.0362694300518134e-06, 'epoch': 1.8}

 90%|████████▉ | 349/388 [59:03<07:03, 10.86s/it]
 90%|█████████ | 350/388 [59:12<06:30, 10.29s/it]
                                                 
{'loss': 0.3133, 'grad_norm': 2.295924425125122, 'learning_rate': 1.0103626943005183e-06, 'epoch': 1.8}

 90%|█████████ | 350/388 [59:12<06:30, 10.29s/it]
 90%|█████████ | 351/388 [59:24<06:38, 10.78s/it]
                                                 
{'loss': 0.4372, 'grad_norm': 1.86611807346344, 'learning_rate': 9.84455958549223e-07, 'epoch': 1.81}

 90%|█████████ | 351/388 [59:24<06:38, 10.78s/it]
 91%|█████████ | 352/388 [59:34<06:25, 10.72s/it]
                                                 
{'loss': 0.6069, 'grad_norm': 2.324321985244751, 'learning_rate': 9.585492227979275e-07, 'epoch': 1.81}

 91%|█████████ | 352/388 [59:34<06:25, 10.72s/it]
 91%|█████████ | 353/388 [59:46<06:22, 10.92s/it]
                                                 
{'loss': 0.5003, 'grad_norm': 1.989992380142212, 'learning_rate': 9.326424870466322e-07, 'epoch': 1.82}

 91%|█████████ | 353/388 [59:46<06:22, 10.92s/it]
 91%|█████████ | 354/388 [59:57<06:16, 11.06s/it]
                                                 
{'loss': 0.3092, 'grad_norm': 2.012613534927368, 'learning_rate': 9.067357512953369e-07, 'epoch': 1.82}

 91%|█████████ | 354/388 [59:57<06:16, 11.06s/it]
 91%|█████████▏| 355/388 [1:00:09<06:08, 11.16s/it]
                                                   
{'loss': 0.3514, 'grad_norm': 2.139753580093384, 'learning_rate': 8.808290155440414e-07, 'epoch': 1.83}

 91%|█████████▏| 355/388 [1:00:09<06:08, 11.16s/it]
 92%|█████████▏| 356/388 [1:00:20<05:57, 11.18s/it]
                                                   
{'loss': 0.4866, 'grad_norm': 1.8078922033309937, 'learning_rate': 8.549222797927462e-07, 'epoch': 1.84}

 92%|█████████▏| 356/388 [1:00:20<05:57, 11.18s/it]
 92%|█████████▏| 357/388 [1:00:33<06:10, 11.95s/it]
                                                   
{'loss': 0.4126, 'grad_norm': 1.9590295553207397, 'learning_rate': 8.290155440414509e-07, 'epoch': 1.84}

 92%|█████████▏| 357/388 [1:00:33<06:10, 11.95s/it]
 92%|█████████▏| 358/388 [1:00:43<05:32, 11.09s/it]
                                                   
{'loss': 0.4893, 'grad_norm': 2.1362719535827637, 'learning_rate': 8.031088082901554e-07, 'epoch': 1.85}

 92%|█████████▏| 358/388 [1:00:43<05:32, 11.09s/it]
 93%|█████████▎| 359/388 [1:00:56<05:41, 11.77s/it]
                                                   
{'loss': 0.3293, 'grad_norm': 2.2367093563079834, 'learning_rate': 7.772020725388602e-07, 'epoch': 1.85}

 93%|█████████▎| 359/388 [1:00:56<05:41, 11.77s/it]
 93%|█████████▎| 360/388 [1:01:06<05:11, 11.13s/it]
                                                   
{'loss': 0.5683, 'grad_norm': 2.1948885917663574, 'learning_rate': 7.512953367875648e-07, 'epoch': 1.86}

 93%|█████████▎| 360/388 [1:01:06<05:11, 11.13s/it]
 93%|█████████▎| 361/388 [1:01:16<04:54, 10.90s/it]
                                                   
{'loss': 0.5219, 'grad_norm': 2.2769477367401123, 'learning_rate': 7.253886010362694e-07, 'epoch': 1.86}

 93%|█████████▎| 361/388 [1:01:16<04:54, 10.90s/it]
 93%|█████████▎| 362/388 [1:01:28<04:55, 11.35s/it]
                                                   
{'loss': 0.3705, 'grad_norm': 1.9062994718551636, 'learning_rate': 6.994818652849742e-07, 'epoch': 1.87}

 93%|█████████▎| 362/388 [1:01:28<04:55, 11.35s/it]
 94%|█████████▎| 363/388 [1:01:40<04:42, 11.31s/it]
                                                   
{'loss': 0.641, 'grad_norm': 2.6360771656036377, 'learning_rate': 6.735751295336788e-07, 'epoch': 1.87}

 94%|█████████▎| 363/388 [1:01:40<04:42, 11.31s/it]
 94%|█████████▍| 364/388 [1:01:51<04:35, 11.47s/it]
                                                   
{'loss': 0.4342, 'grad_norm': 1.8327797651290894, 'learning_rate': 6.476683937823834e-07, 'epoch': 1.88}

 94%|█████████▍| 364/388 [1:01:51<04:35, 11.47s/it]
 94%|█████████▍| 365/388 [1:02:03<04:23, 11.48s/it]
                                                   
{'loss': 0.5184, 'grad_norm': 2.3718812465667725, 'learning_rate': 6.217616580310881e-07, 'epoch': 1.88}

 94%|█████████▍| 365/388 [1:02:03<04:23, 11.48s/it]
 94%|█████████▍| 366/388 [1:02:15<04:19, 11.78s/it]
                                                   
{'loss': 0.4358, 'grad_norm': 2.274719476699829, 'learning_rate': 5.958549222797927e-07, 'epoch': 1.89}

 94%|█████████▍| 366/388 [1:02:15<04:19, 11.78s/it]
 95%|█████████▍| 367/388 [1:02:26<04:02, 11.54s/it]
                                                   
{'loss': 0.8156, 'grad_norm': 2.0248847007751465, 'learning_rate': 5.699481865284974e-07, 'epoch': 1.89}

 95%|█████████▍| 367/388 [1:02:26<04:02, 11.54s/it]
 95%|█████████▍| 368/388 [1:02:38<03:51, 11.55s/it]
                                                   
{'loss': 0.5811, 'grad_norm': 1.902496099472046, 'learning_rate': 5.440414507772021e-07, 'epoch': 1.9}

 95%|█████████▍| 368/388 [1:02:38<03:51, 11.55s/it]
 95%|█████████▌| 369/388 [1:02:49<03:34, 11.30s/it]
                                                   
{'loss': 0.4191, 'grad_norm': 2.6043856143951416, 'learning_rate': 5.181347150259067e-07, 'epoch': 1.9}

 95%|█████████▌| 369/388 [1:02:49<03:34, 11.30s/it]
 95%|█████████▌| 370/388 [1:02:59<03:16, 10.93s/it]
                                                   
{'loss': 0.5595, 'grad_norm': 2.029062032699585, 'learning_rate': 4.922279792746115e-07, 'epoch': 1.91}

 95%|█████████▌| 370/388 [1:02:59<03:16, 10.93s/it]
 96%|█████████▌| 371/388 [1:03:10<03:08, 11.08s/it]
                                                   
{'loss': 0.4161, 'grad_norm': 2.047677993774414, 'learning_rate': 4.663212435233161e-07, 'epoch': 1.91}

 96%|█████████▌| 371/388 [1:03:10<03:08, 11.08s/it]
 96%|█████████▌| 372/388 [1:03:22<03:03, 11.47s/it]
                                                   
{'loss': 0.5255, 'grad_norm': 1.6333986520767212, 'learning_rate': 4.404145077720207e-07, 'epoch': 1.92}

 96%|█████████▌| 372/388 [1:03:22<03:03, 11.47s/it]
 96%|█████████▌| 373/388 [1:03:31<02:38, 10.56s/it]
                                                   
{'loss': 0.2504, 'grad_norm': 2.160090446472168, 'learning_rate': 4.1450777202072546e-07, 'epoch': 1.92}

 96%|█████████▌| 373/388 [1:03:31<02:38, 10.56s/it]
 96%|█████████▋| 374/388 [1:03:40<02:23, 10.24s/it]
                                                   
{'loss': 0.4424, 'grad_norm': 2.3422210216522217, 'learning_rate': 3.886010362694301e-07, 'epoch': 1.93}

 96%|█████████▋| 374/388 [1:03:40<02:23, 10.24s/it]
 97%|█████████▋| 375/388 [1:03:49<02:05,  9.69s/it]
                                                   
{'loss': 0.4118, 'grad_norm': 2.092709541320801, 'learning_rate': 3.626943005181347e-07, 'epoch': 1.93}

 97%|█████████▋| 375/388 [1:03:49<02:05,  9.69s/it]
 97%|█████████▋| 376/388 [1:04:00<02:02, 10.17s/it]
                                                   
{'loss': 0.7061, 'grad_norm': 2.4941372871398926, 'learning_rate': 3.367875647668394e-07, 'epoch': 1.94}

 97%|█████████▋| 376/388 [1:04:00<02:02, 10.17s/it]
 97%|█████████▋| 377/388 [1:04:10<01:50, 10.02s/it]
                                                   
{'loss': 0.413, 'grad_norm': 2.7243452072143555, 'learning_rate': 3.1088082901554404e-07, 'epoch': 1.94}

 97%|█████████▋| 377/388 [1:04:10<01:50, 10.02s/it]
 97%|█████████▋| 378/388 [1:04:20<01:41, 10.13s/it]
                                                   
{'loss': 0.3881, 'grad_norm': 1.8690640926361084, 'learning_rate': 2.849740932642487e-07, 'epoch': 1.95}

 97%|█████████▋| 378/388 [1:04:20<01:41, 10.13s/it]
 98%|█████████▊| 379/388 [1:04:31<01:33, 10.35s/it]
                                                   
{'loss': 0.3744, 'grad_norm': 2.0858564376831055, 'learning_rate': 2.5906735751295336e-07, 'epoch': 1.95}

 98%|█████████▊| 379/388 [1:04:31<01:33, 10.35s/it]
 98%|█████████▊| 380/388 [1:04:44<01:27, 11.00s/it]
                                                   
{'loss': 0.6137, 'grad_norm': 1.994149923324585, 'learning_rate': 2.3316062176165804e-07, 'epoch': 1.96}

 98%|█████████▊| 380/388 [1:04:44<01:27, 11.00s/it]
 98%|█████████▊| 381/388 [1:04:54<01:14, 10.70s/it]
                                                   
{'loss': 0.802, 'grad_norm': 2.0269532203674316, 'learning_rate': 2.0725388601036273e-07, 'epoch': 1.96}

 98%|█████████▊| 381/388 [1:04:54<01:14, 10.70s/it]
 98%|█████████▊| 382/388 [1:05:05<01:05, 10.85s/it]
                                                   
{'loss': 0.619, 'grad_norm': 1.7717715501785278, 'learning_rate': 1.8134715025906736e-07, 'epoch': 1.97}

 98%|█████████▊| 382/388 [1:05:05<01:05, 10.85s/it]
 99%|█████████▊| 383/388 [1:05:16<00:54, 10.89s/it]
                                                   
{'loss': 0.5915, 'grad_norm': 2.2780117988586426, 'learning_rate': 1.5544041450777202e-07, 'epoch': 1.97}

 99%|█████████▊| 383/388 [1:05:16<00:54, 10.89s/it]
 99%|█████████▉| 384/388 [1:05:25<00:41, 10.36s/it]
                                                   
{'loss': 0.2116, 'grad_norm': 2.4663374423980713, 'learning_rate': 1.2953367875647668e-07, 'epoch': 1.98}

 99%|█████████▉| 384/388 [1:05:25<00:41, 10.36s/it]
 99%|█████████▉| 385/388 [1:05:34<00:30, 10.09s/it]
                                                   
{'loss': 0.695, 'grad_norm': 2.1671128273010254, 'learning_rate': 1.0362694300518136e-07, 'epoch': 1.98}

 99%|█████████▉| 385/388 [1:05:34<00:30, 10.09s/it]
 99%|█████████▉| 386/388 [1:05:45<00:20, 10.13s/it]
                                                   
{'loss': 0.7034, 'grad_norm': 1.8202472925186157, 'learning_rate': 7.772020725388601e-08, 'epoch': 1.99}

 99%|█████████▉| 386/388 [1:05:45<00:20, 10.13s/it]
100%|█████████▉| 387/388 [1:05:57<00:10, 10.74s/it]
                                                   
{'loss': 0.756, 'grad_norm': 2.7760379314422607, 'learning_rate': 5.181347150259068e-08, 'epoch': 1.99}

100%|█████████▉| 387/388 [1:05:57<00:10, 10.74s/it]
100%|██████████| 388/388 [1:06:09<00:00, 11.18s/it]
                                                   
{'loss': 0.3306, 'grad_norm': 1.888333797454834, 'learning_rate': 2.590673575129534e-08, 'epoch': 2.0}

100%|██████████| 388/388 [1:06:09<00:00, 11.18s/it]
                                                   
{'train_runtime': 4134.3311, 'train_samples_per_second': 0.374, 'train_steps_per_second': 0.094, 'train_loss': 0.6326200074141788, 'epoch': 2.0}

100%|██████████| 388/388 [1:08:52<00:00, 11.18s/it]
100%|██████████| 388/388 [1:08:52<00:00, 10.65s/it]
[2025-07-08 20:09:17,327] [INFO] [launch.py:351:main] Process 3100578 exits successfully.
[2025-07-08 20:09:19,330] [INFO] [launch.py:351:main] Process 3100577 exits successfully.
[2025-07-08 20:09:22,334] [INFO] [launch.py:351:main] Process 3100576 exits successfully.
[1;34mwandb[0m: 
[1;34mwandb[0m: 🚀 View run [33mtrainer_output[0m at: [34mhttps://wandb.ai/junkim/kullm-pro/runs/c1rnkuf9[0m
[1;34mwandb[0m: Find logs at: [1;35mwandb/run-20250708_190003-c1rnkuf9/logs[0m
[2025-07-08 20:12:00,502] [INFO] [launch.py:351:main] Process 3100575 exits successfully.
