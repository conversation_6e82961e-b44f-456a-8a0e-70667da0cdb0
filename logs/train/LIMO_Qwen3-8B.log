[2025-07-07 19:55:01,575] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 19:55:04,619] [WARNING] [runner.py:215:fetch_hostfile] Unable to find hostfile, will proceed with training with local resources only.
Detected VISIBLE_DEVICES=4,5,6,7: setting --include=localhost:4,5,6,7
[2025-07-07 19:55:04,619] [INFO] [runner.py:605:main] cmd = /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10 -u -m deepspeed.launcher.launch --world_info=eyJsb2NhbGhvc3QiOiBbNCwgNSwgNiwgN119 --master_addr=127.0.0.1 --master_port=49056 --enable_each_rank_log=None train.py --deepspeed deepspeed3.json --proctitle junkim100 --model_name_or_path Qwen3-8B --data_name /data_x/junkim100/projects/finetune/data/LIMO/train.jsonl --wb_project kullm-pro --wb_name LIMO_Qwen3-8B --output_name LIMO_Qwen3-8B --max_length 16384 --num_train_epochs 2 --per_device_train_batch_size 1 --per_device_eval_batch_size 1 --gradient_accumulation_steps 1 --save_only_model --learning_rate 1e-5 --weight_decay 0. --warmup_ratio 0. --lr_scheduler_type cosine --bf16 True --tf32 True --gradient_checkpointing True --logging_steps 1
[2025-07-07 19:55:06,713] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 19:55:09,706] [INFO] [launch.py:146:main] WORLD INFO DICT: {'localhost': [4, 5, 6, 7]}
[2025-07-07 19:55:09,707] [INFO] [launch.py:152:main] nnodes=1, num_local_procs=4, node_rank=0
[2025-07-07 19:55:09,707] [INFO] [launch.py:163:main] global_rank_mapping=defaultdict(<class 'list'>, {'localhost': [0, 1, 2, 3]})
[2025-07-07 19:55:09,707] [INFO] [launch.py:164:main] dist_world_size=4
[2025-07-07 19:55:09,707] [INFO] [launch.py:168:main] Setting CUDA_VISIBLE_DEVICES=4,5,6,7
[2025-07-07 19:55:09,708] [INFO] [launch.py:256:main] process 2630890 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=0', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Qwen3-8B', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Qwen3-8B', '--output_name', 'LIMO_Qwen3-8B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-07 19:55:09,708] [INFO] [launch.py:256:main] process 2630891 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=1', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Qwen3-8B', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Qwen3-8B', '--output_name', 'LIMO_Qwen3-8B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-07 19:55:09,709] [INFO] [launch.py:256:main] process 2630892 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=2', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Qwen3-8B', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Qwen3-8B', '--output_name', 'LIMO_Qwen3-8B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-07 19:55:09,709] [INFO] [launch.py:256:main] process 2630893 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=3', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Qwen3-8B', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Qwen3-8B', '--output_name', 'LIMO_Qwen3-8B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-07 19:55:13,703] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 19:55:13,973] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 19:55:14,050] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 19:55:14,080] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 19:55:14,719] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-07 19:55:15,067] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-07 19:55:15,067] [INFO] [comm.py:700:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
[2025-07-07 19:55:15,131] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-07 19:55:15,160] [INFO] [comm.py:669:init_distributed] cdb=None
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=1,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul07_19-55-13_nlp-server-18,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=LIMO_Qwen3-8B,
wb_project=kullm-pro,
weight_decay=0.0,
)
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=2,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul07_19-55-13_nlp-server-18,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=LIMO_Qwen3-8B,
wb_project=kullm-pro,
weight_decay=0.0,
)
[2025-07-07 19:55:15,654] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 4
[2025-07-07 19:55:15,687] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 4
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=0,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul07_19-55-13_nlp-server-18,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=LIMO_Qwen3-8B,
wb_project=kullm-pro,
weight_decay=0.0,
)
[2025-07-07 19:55:16,412] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 4
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=True,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=3,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jul07_19-55-12_nlp-server-18,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=['wandb'],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=LIMO_Qwen3-8B,
wb_project=kullm-pro,
weight_decay=0.0,
)
[2025-07-07 19:55:16,609] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 4
[2025-07-07 19:55:24,508] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 399, num_elems = 8.19B

Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:32<03:17, 32.90s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:32<03:17, 32.94s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:32<03:17, 32.88s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:33<03:19, 33.23s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [01:11<03:00, 36.15s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [01:11<03:00, 36.14s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [01:11<03:00, 36.13s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [01:11<03:01, 36.28s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [01:21<01:36, 24.20s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [01:21<01:36, 24.19s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [01:21<01:36, 24.18s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [01:21<01:37, 24.31s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [01:22<00:45, 15.04s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [01:22<00:45, 15.03s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [01:22<00:45, 15.04s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [01:22<00:45, 15.12s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [01:23<00:19,  9.98s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [01:23<00:19,  9.97s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [01:23<00:19,  9.98s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [01:23<00:20, 10.04s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [01:24<00:06,  6.94s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [01:24<00:06,  6.94s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [01:24<00:06,  6.94s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [01:24<00:06,  6.98s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [01:25<00:00,  4.95s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [01:25<00:00, 12.17s/it]

Loading checkpoint shards: 100%|██████████| 7/7 [01:25<00:00,  4.95s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [01:25<00:00,  4.95s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [01:25<00:00, 12.17s/it]

Loading checkpoint shards: 100%|██████████| 7/7 [01:25<00:00, 12.16s/it]
WARNING:root:Loading data...
WARNING:root:Loading data...
WARNING:root:Loading data...
WARNING:root:Data Loaded...
WARNING:root:Data Loaded...
WARNING:root:Data Loaded...

Loading checkpoint shards: 100%|██████████| 7/7 [01:25<00:00,  4.94s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [01:25<00:00, 12.22s/it]
WARNING:root:Loading data...
WARNING:root:Data Loaded...
WARNING:root:mean_token_length: 6385.5937904269085
WARNING:root:min_token_length: 1190
WARNING:root:max_token_length: 16367
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:255: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
WARNING:root:mean_token_length: 6385.5937904269085
WARNING:root:min_token_length: 1190
WARNING:root:max_token_length: 16367
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:255: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
WARNING:root:mean_token_length: 6385.5937904269085
WARNING:root:min_token_length: 1190
WARNING:root:max_token_length: 16367
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:255: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
WARNING:root:mean_token_length: 6385.5937904269085
WARNING:root:min_token_length: 1190
WARNING:root:max_token_length: 16367
WARNING:root:revised by max len: 0 out of 773
WARNING:root:Data Prepared...
/data_x/junkim100/projects/finetune/train.py:255: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
Received unrecognized `WANDB_LOG_MODEL` setting value=LIMO_Qwen3-8B; so disabling `WANDB_LOG_MODEL`
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
Received unrecognized `WANDB_LOG_MODEL` setting value=LIMO_Qwen3-8B; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=LIMO_Qwen3-8B; so disabling `WANDB_LOG_MODEL`
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
Received unrecognized `WANDB_LOG_MODEL` setting value=LIMO_Qwen3-8B; so disabling `WANDB_LOG_MODEL`
Parameter Offload: Total persistent parameters: 308224 in 145 params
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[2025-07-07 19:57:31,068] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-07 19:57:31,068] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-07 19:57:31,068] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
[2025-07-07 19:57:31,354] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
wandb: Currently logged in as: junkim100 (junkim) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /data_x/junkim100/projects/finetune/wandb/run-20250707_195731-429qzo86
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run trainer_output
wandb: ⭐️ View project at https://wandb.ai/junkim/kullm-pro
wandb: 🚀 View run at https://wandb.ai/junkim/kullm-pro/runs/429qzo86

  0%|          | 0/388 [00:00<?, ?it/s]`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.

  0%|          | 1/388 [00:10<1:06:15, 10.27s/it]
                                                 
{'loss': 0.6118, 'grad_norm': 3.037013053894043, 'learning_rate': 0.0, 'epoch': 0.01}

  0%|          | 1/388 [00:10<1:06:15, 10.27s/it]
  1%|          | 2/388 [00:20<1:06:37, 10.36s/it]
                                                 
{'loss': 0.4477, 'grad_norm': 4.997533798217773, 'learning_rate': 1e-05, 'epoch': 0.01}

  1%|          | 2/388 [00:20<1:06:37, 10.36s/it]
  1%|          | 3/388 [00:28<57:33,  8.97s/it]  
                                               
{'loss': 0.6518, 'grad_norm': 3.441406726837158, 'learning_rate': 1e-05, 'epoch': 0.02}

  1%|          | 3/388 [00:28<57:33,  8.97s/it]
  1%|          | 4/388 [00:36<55:43,  8.71s/it]
                                               
{'loss': 0.5909, 'grad_norm': 2.857480764389038, 'learning_rate': 9.974093264248705e-06, 'epoch': 0.02}

  1%|          | 4/388 [00:36<55:43,  8.71s/it]
  1%|▏         | 5/388 [00:47<1:01:34,  9.65s/it]
                                                 
{'loss': 0.4532, 'grad_norm': 1.358364462852478, 'learning_rate': 9.94818652849741e-06, 'epoch': 0.03}

  1%|▏         | 5/388 [00:47<1:01:34,  9.65s/it]
  2%|▏         | 6/388 [00:55<57:51,  9.09s/it]  
                                               
{'loss': 0.4716, 'grad_norm': 2.034412145614624, 'learning_rate': 9.922279792746115e-06, 'epoch': 0.03}

  2%|▏         | 6/388 [00:55<57:51,  9.09s/it]
  2%|▏         | 7/388 [01:09<1:06:51, 10.53s/it]
                                                 
{'loss': 0.5401, 'grad_norm': 1.3364737033843994, 'learning_rate': 9.89637305699482e-06, 'epoch': 0.04}

  2%|▏         | 7/388 [01:09<1:06:51, 10.53s/it]
  2%|▏         | 8/388 [01:17<1:02:40,  9.90s/it]
                                                 
{'loss': 0.8155, 'grad_norm': 1.8729639053344727, 'learning_rate': 9.870466321243524e-06, 'epoch': 0.04}

  2%|▏         | 8/388 [01:17<1:02:40,  9.90s/it]
  2%|▏         | 9/388 [01:25<58:53,  9.32s/it]  
                                               
{'loss': 0.6926, 'grad_norm': 1.7938966751098633, 'learning_rate': 9.844559585492228e-06, 'epoch': 0.05}

  2%|▏         | 9/388 [01:25<58:53,  9.32s/it]
  3%|▎         | 10/388 [01:39<1:08:00, 10.80s/it]
                                                  
{'loss': 0.3846, 'grad_norm': 1.4358798265457153, 'learning_rate': 9.818652849740934e-06, 'epoch': 0.05}

  3%|▎         | 10/388 [01:39<1:08:00, 10.80s/it]
  3%|▎         | 11/388 [01:47<1:01:57,  9.86s/it]
                                                  
{'loss': 0.5504, 'grad_norm': 1.3389484882354736, 'learning_rate': 9.792746113989638e-06, 'epoch': 0.06}

  3%|▎         | 11/388 [01:47<1:01:57,  9.86s/it]
  3%|▎         | 12/388 [01:58<1:03:23, 10.12s/it]
                                                  
{'loss': 0.5422, 'grad_norm': 1.162811279296875, 'learning_rate': 9.766839378238344e-06, 'epoch': 0.06}

  3%|▎         | 12/388 [01:58<1:03:23, 10.12s/it]
  3%|▎         | 13/388 [02:08<1:03:36, 10.18s/it]
                                                  
{'loss': 0.5769, 'grad_norm': 0.9763242602348328, 'learning_rate': 9.740932642487048e-06, 'epoch': 0.07}

  3%|▎         | 13/388 [02:08<1:03:36, 10.18s/it]
  4%|▎         | 14/388 [02:15<58:09,  9.33s/it]  
                                                
{'loss': 0.5118, 'grad_norm': 1.156229019165039, 'learning_rate': 9.715025906735752e-06, 'epoch': 0.07}

  4%|▎         | 14/388 [02:15<58:09,  9.33s/it]
  4%|▍         | 15/388 [02:24<57:19,  9.22s/it]
                                                
{'loss': 0.4388, 'grad_norm': 0.8329430818557739, 'learning_rate': 9.689119170984456e-06, 'epoch': 0.08}

  4%|▍         | 15/388 [02:24<57:19,  9.22s/it]
  4%|▍         | 16/388 [02:37<1:03:46, 10.29s/it]
                                                  
{'loss': 0.5285, 'grad_norm': 0.907092273235321, 'learning_rate': 9.66321243523316e-06, 'epoch': 0.08}

  4%|▍         | 16/388 [02:37<1:03:46, 10.29s/it]
  4%|▍         | 17/388 [02:50<1:07:28, 10.91s/it]
                                                  
{'loss': 0.627, 'grad_norm': 0.9273800849914551, 'learning_rate': 9.637305699481867e-06, 'epoch': 0.09}

  4%|▍         | 17/388 [02:50<1:07:28, 10.91s/it]
  5%|▍         | 18/388 [03:00<1:06:42, 10.82s/it]
                                                  
{'loss': 0.4012, 'grad_norm': 0.7798483967781067, 'learning_rate': 9.61139896373057e-06, 'epoch': 0.09}

  5%|▍         | 18/388 [03:00<1:06:42, 10.82s/it]
  5%|▍         | 19/388 [03:10<1:05:20, 10.62s/it]
                                                  
{'loss': 0.5275, 'grad_norm': 0.9151303768157959, 'learning_rate': 9.585492227979275e-06, 'epoch': 0.1}

  5%|▍         | 19/388 [03:10<1:05:20, 10.62s/it]
  5%|▌         | 20/388 [03:20<1:02:30, 10.19s/it]
                                                  
{'loss': 0.6498, 'grad_norm': 0.9406693577766418, 'learning_rate': 9.559585492227979e-06, 'epoch': 0.1}

  5%|▌         | 20/388 [03:20<1:02:30, 10.19s/it]
  5%|▌         | 21/388 [03:27<58:13,  9.52s/it]  
                                                
{'loss': 0.4066, 'grad_norm': 1.1001673936843872, 'learning_rate': 9.533678756476683e-06, 'epoch': 0.11}

  5%|▌         | 21/388 [03:27<58:13,  9.52s/it]
  6%|▌         | 22/388 [03:42<1:06:28, 10.90s/it]
                                                  
{'loss': 0.4623, 'grad_norm': 0.8436588644981384, 'learning_rate': 9.50777202072539e-06, 'epoch': 0.11}

  6%|▌         | 22/388 [03:42<1:06:28, 10.90s/it]
  6%|▌         | 23/388 [03:53<1:07:38, 11.12s/it]
                                                  
{'loss': 0.3532, 'grad_norm': 0.85975581407547, 'learning_rate': 9.481865284974095e-06, 'epoch': 0.12}

  6%|▌         | 23/388 [03:53<1:07:38, 11.12s/it]
  6%|▌         | 24/388 [04:02<1:03:17, 10.43s/it]
                                                  
{'loss': 0.4455, 'grad_norm': 1.0105911493301392, 'learning_rate': 9.4559585492228e-06, 'epoch': 0.12}

  6%|▌         | 24/388 [04:02<1:03:17, 10.43s/it]
  6%|▋         | 25/388 [04:14<1:06:29, 10.99s/it]
                                                  
{'loss': 0.3336, 'grad_norm': 0.8208414316177368, 'learning_rate': 9.430051813471504e-06, 'epoch': 0.13}

  6%|▋         | 25/388 [04:14<1:06:29, 10.99s/it]
  7%|▋         | 26/388 [04:22<59:37,  9.88s/it]  
                                                
{'loss': 0.327, 'grad_norm': 0.9341593384742737, 'learning_rate': 9.404145077720208e-06, 'epoch': 0.13}

  7%|▋         | 26/388 [04:22<59:37,  9.88s/it]
  7%|▋         | 27/388 [04:32<1:00:29, 10.05s/it]
                                                  
{'loss': 0.5783, 'grad_norm': 0.733910322189331, 'learning_rate': 9.378238341968912e-06, 'epoch': 0.14}

  7%|▋         | 27/388 [04:32<1:00:29, 10.05s/it]
  7%|▋         | 28/388 [04:45<1:05:45, 10.96s/it]
                                                  
{'loss': 0.3499, 'grad_norm': 0.7290202379226685, 'learning_rate': 9.352331606217618e-06, 'epoch': 0.14}

  7%|▋         | 28/388 [04:45<1:05:45, 10.96s/it]
  7%|▋         | 29/388 [04:54<1:02:36, 10.46s/it]
                                                  
{'loss': 0.8039, 'grad_norm': 1.4489502906799316, 'learning_rate': 9.326424870466322e-06, 'epoch': 0.15}

  7%|▋         | 29/388 [04:54<1:02:36, 10.46s/it]
  8%|▊         | 30/388 [05:03<58:37,  9.83s/it]  
                                                
{'loss': 0.6505, 'grad_norm': 1.0298844575881958, 'learning_rate': 9.300518134715026e-06, 'epoch': 0.15}

  8%|▊         | 30/388 [05:03<58:37,  9.83s/it]
  8%|▊         | 31/388 [05:13<58:21,  9.81s/it]
                                                
{'loss': 0.2983, 'grad_norm': 0.8450474739074707, 'learning_rate': 9.27461139896373e-06, 'epoch': 0.16}

  8%|▊         | 31/388 [05:13<58:21,  9.81s/it]
  8%|▊         | 32/388 [05:20<54:35,  9.20s/it]
                                                
{'loss': 0.4557, 'grad_norm': 1.1032010316848755, 'learning_rate': 9.248704663212435e-06, 'epoch': 0.16}

  8%|▊         | 32/388 [05:20<54:35,  9.20s/it]
  9%|▊         | 33/388 [05:29<53:41,  9.07s/it]
                                                
{'loss': 0.4925, 'grad_norm': 1.0421240329742432, 'learning_rate': 9.22279792746114e-06, 'epoch': 0.17}

  9%|▊         | 33/388 [05:29<53:41,  9.07s/it]
  9%|▉         | 34/388 [05:40<55:59,  9.49s/it]
                                                
{'loss': 0.5736, 'grad_norm': 0.9763453006744385, 'learning_rate': 9.196891191709847e-06, 'epoch': 0.18}

  9%|▉         | 34/388 [05:40<55:59,  9.49s/it]
  9%|▉         | 35/388 [05:48<53:36,  9.11s/it]
                                                
{'loss': 0.6538, 'grad_norm': 1.0696004629135132, 'learning_rate': 9.17098445595855e-06, 'epoch': 0.18}

  9%|▉         | 35/388 [05:48<53:36,  9.11s/it]
  9%|▉         | 36/388 [05:58<54:37,  9.31s/it]
                                                
{'loss': 0.3677, 'grad_norm': 0.8796079158782959, 'learning_rate': 9.145077720207255e-06, 'epoch': 0.19}

  9%|▉         | 36/388 [05:58<54:37,  9.31s/it]
 10%|▉         | 37/388 [06:07<54:15,  9.27s/it]
                                                
{'loss': 0.5165, 'grad_norm': 0.9399876594543457, 'learning_rate': 9.11917098445596e-06, 'epoch': 0.19}

 10%|▉         | 37/388 [06:07<54:15,  9.27s/it]
 10%|▉         | 38/388 [06:18<56:41,  9.72s/it]
                                                
{'loss': 0.3586, 'grad_norm': 0.8276817202568054, 'learning_rate': 9.093264248704663e-06, 'epoch': 0.2}

 10%|▉         | 38/388 [06:18<56:41,  9.72s/it]
 10%|█         | 39/388 [06:26<54:19,  9.34s/it]
                                                
{'loss': 1.0234, 'grad_norm': 1.2928184270858765, 'learning_rate': 9.06735751295337e-06, 'epoch': 0.2}

 10%|█         | 39/388 [06:26<54:19,  9.34s/it]
 10%|█         | 40/388 [06:35<53:30,  9.22s/it]
                                                
{'loss': 0.4397, 'grad_norm': 0.84074866771698, 'learning_rate': 9.041450777202073e-06, 'epoch': 0.21}

 10%|█         | 40/388 [06:35<53:30,  9.22s/it]
 11%|█         | 41/388 [06:45<55:31,  9.60s/it]
                                                
{'loss': 0.508, 'grad_norm': 1.0501139163970947, 'learning_rate': 9.015544041450778e-06, 'epoch': 0.21}

 11%|█         | 41/388 [06:45<55:31,  9.60s/it]
 11%|█         | 42/388 [06:54<53:11,  9.22s/it]
                                                
{'loss': 0.4575, 'grad_norm': 0.8736107349395752, 'learning_rate': 8.989637305699482e-06, 'epoch': 0.22}

 11%|█         | 42/388 [06:54<53:11,  9.22s/it]
 11%|█         | 43/388 [07:04<55:22,  9.63s/it]
                                                
{'loss': 0.4772, 'grad_norm': 0.9019767642021179, 'learning_rate': 8.963730569948186e-06, 'epoch': 0.22}

 11%|█         | 43/388 [07:04<55:22,  9.63s/it]
 11%|█▏        | 44/388 [07:14<54:56,  9.58s/it]
                                                
{'loss': 0.3033, 'grad_norm': 0.9801058769226074, 'learning_rate': 8.937823834196892e-06, 'epoch': 0.23}

 11%|█▏        | 44/388 [07:14<54:56,  9.58s/it]
 12%|█▏        | 45/388 [07:25<58:22, 10.21s/it]
                                                
{'loss': 0.4267, 'grad_norm': 0.8529039621353149, 'learning_rate': 8.911917098445596e-06, 'epoch': 0.23}

 12%|█▏        | 45/388 [07:26<58:22, 10.21s/it][2025-07-07 20:05:13,026] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time

 12%|█▏        | 46/388 [07:40<1:04:53, 11.39s/it]
                                                  
{'loss': 0.3449, 'grad_norm': 0.9937889575958252, 'learning_rate': 8.886010362694302e-06, 'epoch': 0.24}

 12%|█▏        | 46/388 [07:40<1:04:53, 11.39s/it]
 12%|█▏        | 47/388 [07:48<59:31, 10.47s/it]  
                                                
{'loss': 0.4012, 'grad_norm': 1.0137683153152466, 'learning_rate': 8.860103626943006e-06, 'epoch': 0.24}

 12%|█▏        | 47/388 [07:48<59:31, 10.47s/it]
 12%|█▏        | 48/388 [07:56<55:57,  9.87s/it]
                                                
{'loss': 0.499, 'grad_norm': 1.0287439823150635, 'learning_rate': 8.83419689119171e-06, 'epoch': 0.25}

 12%|█▏        | 48/388 [07:56<55:57,  9.87s/it]
 13%|█▎        | 49/388 [08:08<58:43, 10.39s/it]
                                                
{'loss': 0.5593, 'grad_norm': 0.9435105919837952, 'learning_rate': 8.808290155440415e-06, 'epoch': 0.25}

 13%|█▎        | 49/388 [08:08<58:43, 10.39s/it]
 13%|█▎        | 50/388 [08:19<58:53, 10.45s/it]
                                                
{'loss': 0.4125, 'grad_norm': 1.0088106393814087, 'learning_rate': 8.78238341968912e-06, 'epoch': 0.26}

 13%|█▎        | 50/388 [08:19<58:53, 10.45s/it]
 13%|█▎        | 51/388 [08:31<1:02:02, 11.05s/it]
                                                  
{'loss': 0.5531, 'grad_norm': 0.7858838438987732, 'learning_rate': 8.756476683937825e-06, 'epoch': 0.26}

 13%|█▎        | 51/388 [08:31<1:02:02, 11.05s/it]
 13%|█▎        | 52/388 [08:40<58:38, 10.47s/it]  
                                                
{'loss': 0.5977, 'grad_norm': 0.7622793912887573, 'learning_rate': 8.730569948186529e-06, 'epoch': 0.27}

 13%|█▎        | 52/388 [08:40<58:38, 10.47s/it]
 14%|█▎        | 53/388 [08:53<1:01:43, 11.06s/it]
                                                  
{'loss': 0.5597, 'grad_norm': 1.03977370262146, 'learning_rate': 8.704663212435233e-06, 'epoch': 0.27}

 14%|█▎        | 53/388 [08:53<1:01:43, 11.06s/it]
 14%|█▍        | 54/388 [09:05<1:03:05, 11.34s/it]
                                                  
{'loss': 0.4562, 'grad_norm': 0.9040418863296509, 'learning_rate': 8.678756476683938e-06, 'epoch': 0.28}

 14%|█▍        | 54/388 [09:05<1:03:05, 11.34s/it]
 14%|█▍        | 55/388 [09:13<58:49, 10.60s/it]  
                                                
{'loss': 0.567, 'grad_norm': 0.9234251379966736, 'learning_rate': 8.652849740932643e-06, 'epoch': 0.28}

 14%|█▍        | 55/388 [09:13<58:49, 10.60s/it]
 14%|█▍        | 56/388 [09:24<57:58, 10.48s/it]
                                                
{'loss': 0.484, 'grad_norm': 0.8023905754089355, 'learning_rate': 8.626943005181348e-06, 'epoch': 0.29}

 14%|█▍        | 56/388 [09:24<57:58, 10.48s/it]
 15%|█▍        | 57/388 [09:33<56:02, 10.16s/it]
                                                
{'loss': 0.9456, 'grad_norm': 1.074746012687683, 'learning_rate': 8.601036269430052e-06, 'epoch': 0.29}

 15%|█▍        | 57/388 [09:33<56:02, 10.16s/it]
 15%|█▍        | 58/388 [09:40<50:31,  9.19s/it]
                                                
{'loss': 0.3013, 'grad_norm': 0.9573298692703247, 'learning_rate': 8.575129533678758e-06, 'epoch': 0.3}

 15%|█▍        | 58/388 [09:40<50:31,  9.19s/it]
 15%|█▌        | 59/388 [09:51<52:31,  9.58s/it]
                                                
{'loss': 0.2912, 'grad_norm': 0.8359588980674744, 'learning_rate': 8.549222797927462e-06, 'epoch': 0.3}

 15%|█▌        | 59/388 [09:51<52:31,  9.58s/it]
 15%|█▌        | 60/388 [10:00<52:57,  9.69s/it]
                                                
{'loss': 0.9056, 'grad_norm': 1.1320743560791016, 'learning_rate': 8.523316062176166e-06, 'epoch': 0.31}

 15%|█▌        | 60/388 [10:00<52:57,  9.69s/it]
 16%|█▌        | 61/388 [10:10<52:31,  9.64s/it]
                                                
{'loss': 0.2991, 'grad_norm': 0.8571169376373291, 'learning_rate': 8.497409326424872e-06, 'epoch': 0.31}

 16%|█▌        | 61/388 [10:10<52:31,  9.64s/it]
 16%|█▌        | 62/388 [10:22<55:26, 10.20s/it]
                                                
{'loss': 0.5322, 'grad_norm': 0.8584021329879761, 'learning_rate': 8.471502590673576e-06, 'epoch': 0.32}

 16%|█▌        | 62/388 [10:22<55:26, 10.20s/it]
 16%|█▌        | 63/388 [10:33<56:56, 10.51s/it]
                                                
{'loss': 0.5388, 'grad_norm': 0.912481963634491, 'learning_rate': 8.44559585492228e-06, 'epoch': 0.32}

 16%|█▌        | 63/388 [10:33<56:56, 10.51s/it]
 16%|█▋        | 64/388 [10:40<51:54,  9.61s/it]
                                                
{'loss': 0.4445, 'grad_norm': 0.9597629904747009, 'learning_rate': 8.419689119170985e-06, 'epoch': 0.33}

 16%|█▋        | 64/388 [10:40<51:54,  9.61s/it]
 17%|█▋        | 65/388 [10:54<59:04, 10.97s/it]
                                                
{'loss': 0.3828, 'grad_norm': 0.9330686926841736, 'learning_rate': 8.393782383419689e-06, 'epoch': 0.34}

 17%|█▋        | 65/388 [10:54<59:04, 10.97s/it]
 17%|█▋        | 66/388 [11:04<56:55, 10.61s/it]
                                                
{'loss': 0.6254, 'grad_norm': 1.016761302947998, 'learning_rate': 8.367875647668395e-06, 'epoch': 0.34}

 17%|█▋        | 66/388 [11:04<56:55, 10.61s/it]
 17%|█▋        | 67/388 [11:13<53:18,  9.96s/it]
                                                
{'loss': 0.393, 'grad_norm': 0.8420445322990417, 'learning_rate': 8.341968911917099e-06, 'epoch': 0.35}

 17%|█▋        | 67/388 [11:13<53:18,  9.96s/it]
 18%|█▊        | 68/388 [11:22<52:37,  9.87s/it]
                                                
{'loss': 0.3739, 'grad_norm': 4.204626083374023, 'learning_rate': 8.316062176165803e-06, 'epoch': 0.35}

 18%|█▊        | 68/388 [11:22<52:37,  9.87s/it]
 18%|█▊        | 69/388 [11:31<51:17,  9.65s/it]
                                                
{'loss': 0.6238, 'grad_norm': 1.0802873373031616, 'learning_rate': 8.290155440414507e-06, 'epoch': 0.36}

 18%|█▊        | 69/388 [11:31<51:17,  9.65s/it]
 18%|█▊        | 70/388 [11:40<49:59,  9.43s/it]
                                                
{'loss': 0.5613, 'grad_norm': 0.9659601449966431, 'learning_rate': 8.264248704663213e-06, 'epoch': 0.36}

 18%|█▊        | 70/388 [11:40<49:59,  9.43s/it]
 18%|█▊        | 71/388 [11:52<52:49, 10.00s/it]
                                                
{'loss': 0.5156, 'grad_norm': 0.8848123550415039, 'learning_rate': 8.238341968911918e-06, 'epoch': 0.37}

 18%|█▊        | 71/388 [11:52<52:49, 10.00s/it]
 19%|█▊        | 72/388 [12:01<51:37,  9.80s/it]
                                                
{'loss': 0.3952, 'grad_norm': 0.8703460693359375, 'learning_rate': 8.212435233160623e-06, 'epoch': 0.37}

 19%|█▊        | 72/388 [12:01<51:37,  9.80s/it]
 19%|█▉        | 73/388 [12:12<53:38, 10.22s/it]
                                                
{'loss': 0.5856, 'grad_norm': 0.723524808883667, 'learning_rate': 8.186528497409328e-06, 'epoch': 0.38}

 19%|█▉        | 73/388 [12:12<53:38, 10.22s/it]
 19%|█▉        | 74/388 [12:24<56:38, 10.82s/it]
                                                
{'loss': 0.4317, 'grad_norm': 0.7903366088867188, 'learning_rate': 8.160621761658032e-06, 'epoch': 0.38}

 19%|█▉        | 74/388 [12:24<56:38, 10.82s/it]
 19%|█▉        | 75/388 [12:33<53:03, 10.17s/it]
                                                
{'loss': 0.6356, 'grad_norm': 0.8691446781158447, 'learning_rate': 8.134715025906736e-06, 'epoch': 0.39}

 19%|█▉        | 75/388 [12:33<53:03, 10.17s/it]
 20%|█▉        | 76/388 [12:43<53:14, 10.24s/it]
                                                
{'loss': 0.4639, 'grad_norm': 0.8351852893829346, 'learning_rate': 8.10880829015544e-06, 'epoch': 0.39}

 20%|█▉        | 76/388 [12:43<53:14, 10.24s/it]
 20%|█▉        | 77/388 [12:55<55:21, 10.68s/it]
                                                
{'loss': 0.3514, 'grad_norm': 0.6321220993995667, 'learning_rate': 8.082901554404146e-06, 'epoch': 0.4}

 20%|█▉        | 77/388 [12:55<55:21, 10.68s/it]
 20%|██        | 78/388 [13:08<58:12, 11.27s/it]
                                                
{'loss': 0.4798, 'grad_norm': 0.9055750370025635, 'learning_rate': 8.05699481865285e-06, 'epoch': 0.4}

 20%|██        | 78/388 [13:08<58:12, 11.27s/it]
 20%|██        | 79/388 [13:20<59:59, 11.65s/it]
                                                
{'loss': 0.551, 'grad_norm': 0.9771506190299988, 'learning_rate': 8.031088082901555e-06, 'epoch': 0.41}

 20%|██        | 79/388 [13:20<59:59, 11.65s/it]
 21%|██        | 80/388 [13:30<57:18, 11.17s/it]
                                                
{'loss': 0.7073, 'grad_norm': 1.014744758605957, 'learning_rate': 8.005181347150259e-06, 'epoch': 0.41}

 21%|██        | 80/388 [13:30<57:18, 11.17s/it]
 21%|██        | 81/388 [13:41<56:42, 11.08s/it]
                                                
{'loss': 0.3534, 'grad_norm': 0.880586564540863, 'learning_rate': 7.979274611398965e-06, 'epoch': 0.42}

 21%|██        | 81/388 [13:41<56:42, 11.08s/it]
 21%|██        | 82/388 [13:50<52:52, 10.37s/it]
                                                
{'loss': 0.5754, 'grad_norm': 0.8685542345046997, 'learning_rate': 7.953367875647669e-06, 'epoch': 0.42}

 21%|██        | 82/388 [13:50<52:52, 10.37s/it]
 21%|██▏       | 83/388 [14:00<52:20, 10.30s/it]
                                                
{'loss': 0.4483, 'grad_norm': 0.8849413990974426, 'learning_rate': 7.927461139896375e-06, 'epoch': 0.43}

 21%|██▏       | 83/388 [14:00<52:20, 10.30s/it]
 22%|██▏       | 84/388 [14:10<51:41, 10.20s/it]
                                                
{'loss': 0.5691, 'grad_norm': 0.832760751247406, 'learning_rate': 7.901554404145079e-06, 'epoch': 0.43}

 22%|██▏       | 84/388 [14:10<51:41, 10.20s/it]
 22%|██▏       | 85/388 [14:19<49:23,  9.78s/it]
                                                
{'loss': 0.3668, 'grad_norm': 0.896132230758667, 'learning_rate': 7.875647668393783e-06, 'epoch': 0.44}

 22%|██▏       | 85/388 [14:19<49:23,  9.78s/it]
 22%|██▏       | 86/388 [14:27<47:20,  9.41s/it]
                                                
{'loss': 0.329, 'grad_norm': 0.8391252160072327, 'learning_rate': 7.849740932642487e-06, 'epoch': 0.44}

 22%|██▏       | 86/388 [14:27<47:20,  9.41s/it]
 22%|██▏       | 87/388 [14:37<48:06,  9.59s/it]
                                                
{'loss': 0.8363, 'grad_norm': 0.9460362792015076, 'learning_rate': 7.823834196891192e-06, 'epoch': 0.45}

 22%|██▏       | 87/388 [14:37<48:06,  9.59s/it]
 23%|██▎       | 88/388 [14:47<48:23,  9.68s/it]
                                                
{'loss': 0.3265, 'grad_norm': 0.6968737840652466, 'learning_rate': 7.797927461139898e-06, 'epoch': 0.45}

 23%|██▎       | 88/388 [14:47<48:23,  9.68s/it]
 23%|██▎       | 89/388 [14:56<46:19,  9.30s/it]
                                                
{'loss': 0.5725, 'grad_norm': 0.8940482139587402, 'learning_rate': 7.772020725388602e-06, 'epoch': 0.46}

 23%|██▎       | 89/388 [14:56<46:19,  9.30s/it]
 23%|██▎       | 90/388 [15:07<49:34,  9.98s/it]
                                                
{'loss': 0.6128, 'grad_norm': 0.7471238374710083, 'learning_rate': 7.746113989637306e-06, 'epoch': 0.46}

 23%|██▎       | 90/388 [15:07<49:34,  9.98s/it]
 23%|██▎       | 91/388 [15:17<48:27,  9.79s/it]
                                                
{'loss': 0.6115, 'grad_norm': 0.9247841835021973, 'learning_rate': 7.72020725388601e-06, 'epoch': 0.47}

 23%|██▎       | 91/388 [15:17<48:27,  9.79s/it]
 24%|██▎       | 92/388 [15:26<48:17,  9.79s/it]
                                                
{'loss': 0.3097, 'grad_norm': 1.023402214050293, 'learning_rate': 7.694300518134716e-06, 'epoch': 0.47}

 24%|██▎       | 92/388 [15:26<48:17,  9.79s/it]
 24%|██▍       | 93/388 [15:36<48:05,  9.78s/it]
                                                
{'loss': 0.4119, 'grad_norm': 0.9374239444732666, 'learning_rate': 7.66839378238342e-06, 'epoch': 0.48}

 24%|██▍       | 93/388 [15:36<48:05,  9.78s/it]
 24%|██▍       | 94/388 [15:46<47:53,  9.77s/it]
                                                
{'loss': 0.2883, 'grad_norm': 0.9606110453605652, 'learning_rate': 7.642487046632126e-06, 'epoch': 0.48}

 24%|██▍       | 94/388 [15:46<47:53,  9.77s/it]
 24%|██▍       | 95/388 [15:58<51:28, 10.54s/it]
                                                
{'loss': 0.5141, 'grad_norm': 1.0684373378753662, 'learning_rate': 7.61658031088083e-06, 'epoch': 0.49}

 24%|██▍       | 95/388 [15:58<51:28, 10.54s/it]
 25%|██▍       | 96/388 [16:10<52:34, 10.80s/it]
                                                
{'loss': 0.7329, 'grad_norm': 0.907875120639801, 'learning_rate': 7.590673575129535e-06, 'epoch': 0.49}

 25%|██▍       | 96/388 [16:10<52:34, 10.80s/it]
 25%|██▌       | 97/388 [16:21<53:10, 10.96s/it]
                                                
{'loss': 0.3581, 'grad_norm': 1.4813743829727173, 'learning_rate': 7.564766839378239e-06, 'epoch': 0.5}

 25%|██▌       | 97/388 [16:21<53:10, 10.96s/it]
 25%|██▌       | 98/388 [16:30<49:38, 10.27s/it]
                                                
{'loss': 0.7921, 'grad_norm': 1.053281307220459, 'learning_rate': 7.538860103626944e-06, 'epoch': 0.51}

 25%|██▌       | 98/388 [16:30<49:38, 10.27s/it]
 26%|██▌       | 99/388 [16:42<52:30, 10.90s/it]
                                                
{'loss': 0.4022, 'grad_norm': 0.6693784594535828, 'learning_rate': 7.512953367875648e-06, 'epoch': 0.51}

 26%|██▌       | 99/388 [16:42<52:30, 10.90s/it]
 26%|██▌       | 100/388 [16:50<47:37,  9.92s/it]
                                                 
{'loss': 0.5226, 'grad_norm': 0.8689789772033691, 'learning_rate': 7.487046632124353e-06, 'epoch': 0.52}

 26%|██▌       | 100/388 [16:50<47:37,  9.92s/it]
 26%|██▌       | 101/388 [16:58<45:30,  9.51s/it]
                                                 
{'loss': 0.3559, 'grad_norm': 0.859642744064331, 'learning_rate': 7.461139896373057e-06, 'epoch': 0.52}

 26%|██▌       | 101/388 [16:58<45:30,  9.51s/it]
 26%|██▋       | 102/388 [17:09<47:18,  9.93s/it]
                                                 
{'loss': 0.5043, 'grad_norm': 0.925703763961792, 'learning_rate': 7.435233160621762e-06, 'epoch': 0.53}

 26%|██▋       | 102/388 [17:09<47:18,  9.93s/it]
 27%|██▋       | 103/388 [17:22<50:39, 10.66s/it]
                                                 
{'loss': 0.445, 'grad_norm': 0.849816620349884, 'learning_rate': 7.409326424870467e-06, 'epoch': 0.53}

 27%|██▋       | 103/388 [17:22<50:39, 10.66s/it]
 27%|██▋       | 104/388 [17:32<49:55, 10.55s/it]
                                                 
{'loss': 0.3361, 'grad_norm': 0.8914594054222107, 'learning_rate': 7.383419689119171e-06, 'epoch': 0.54}

 27%|██▋       | 104/388 [17:32<49:55, 10.55s/it]
 27%|██▋       | 105/388 [17:43<51:21, 10.89s/it]
                                                 
{'loss': 0.6787, 'grad_norm': 0.9576083421707153, 'learning_rate': 7.357512953367876e-06, 'epoch': 0.54}

 27%|██▋       | 105/388 [17:43<51:21, 10.89s/it]
 27%|██▋       | 106/388 [17:52<47:18, 10.06s/it]
                                                 
{'loss': 0.2507, 'grad_norm': 0.9256339073181152, 'learning_rate': 7.331606217616582e-06, 'epoch': 0.55}

 27%|██▋       | 106/388 [17:52<47:18, 10.06s/it]
 28%|██▊       | 107/388 [18:02<47:48, 10.21s/it]
                                                 
{'loss': 0.3721, 'grad_norm': 0.7709092497825623, 'learning_rate': 7.305699481865286e-06, 'epoch': 0.55}

 28%|██▊       | 107/388 [18:02<47:48, 10.21s/it]
 28%|██▊       | 108/388 [18:11<46:22,  9.94s/it]
                                                 
{'loss': 0.4938, 'grad_norm': 1.0105618238449097, 'learning_rate': 7.27979274611399e-06, 'epoch': 0.56}

 28%|██▊       | 108/388 [18:11<46:22,  9.94s/it]
 28%|██▊       | 109/388 [18:19<43:00,  9.25s/it]
                                                 
{'loss': 0.3601, 'grad_norm': 1.143574595451355, 'learning_rate': 7.253886010362695e-06, 'epoch': 0.56}

 28%|██▊       | 109/388 [18:19<43:00,  9.25s/it]
 28%|██▊       | 110/388 [18:30<44:35,  9.63s/it]
                                                 
{'loss': 0.3356, 'grad_norm': 0.784905195236206, 'learning_rate': 7.2279792746113995e-06, 'epoch': 0.57}

 28%|██▊       | 110/388 [18:30<44:35,  9.63s/it]
 29%|██▊       | 111/388 [18:40<45:38,  9.89s/it]
                                                 
{'loss': 0.2662, 'grad_norm': 0.9888504147529602, 'learning_rate': 7.2020725388601045e-06, 'epoch': 0.57}

 29%|██▊       | 111/388 [18:40<45:38,  9.89s/it]
 29%|██▉       | 112/388 [18:49<43:54,  9.55s/it]
                                                 
{'loss': 0.6064, 'grad_norm': 0.815376877784729, 'learning_rate': 7.176165803108809e-06, 'epoch': 0.58}

 29%|██▉       | 112/388 [18:49<43:54,  9.55s/it]
 29%|██▉       | 113/388 [18:57<41:20,  9.02s/it]
                                                 
{'loss': 0.5501, 'grad_norm': 0.9281860589981079, 'learning_rate': 7.150259067357514e-06, 'epoch': 0.58}

 29%|██▉       | 113/388 [18:57<41:20,  9.02s/it]
 29%|██▉       | 114/388 [19:09<45:45, 10.02s/it]
                                                 
{'loss': 0.4158, 'grad_norm': 0.9554753303527832, 'learning_rate': 7.124352331606218e-06, 'epoch': 0.59}

 29%|██▉       | 114/388 [19:09<45:45, 10.02s/it]
 30%|██▉       | 115/388 [19:17<42:39,  9.38s/it]
                                                 
{'loss': 0.473, 'grad_norm': 0.8598635792732239, 'learning_rate': 7.098445595854922e-06, 'epoch': 0.59}

 30%|██▉       | 115/388 [19:17<42:39,  9.38s/it]
 30%|██▉       | 116/388 [19:25<40:25,  8.92s/it]
                                                 
{'loss': 0.2477, 'grad_norm': 0.8450217843055725, 'learning_rate': 7.072538860103627e-06, 'epoch': 0.6}

 30%|██▉       | 116/388 [19:25<40:25,  8.92s/it]
 30%|███       | 117/388 [19:34<41:16,  9.14s/it]
                                                 
{'loss': 0.5415, 'grad_norm': 0.9790889024734497, 'learning_rate': 7.0466321243523315e-06, 'epoch': 0.6}

 30%|███       | 117/388 [19:34<41:16,  9.14s/it]
 30%|███       | 118/388 [19:44<41:58,  9.33s/it]
                                                 
{'loss': 0.4417, 'grad_norm': 1.092555284500122, 'learning_rate': 7.020725388601037e-06, 'epoch': 0.61}

 30%|███       | 118/388 [19:44<41:58,  9.33s/it]
 31%|███       | 119/388 [19:52<40:20,  9.00s/it]
                                                 
{'loss': 0.7875, 'grad_norm': 1.0388628244400024, 'learning_rate': 6.994818652849742e-06, 'epoch': 0.61}

 31%|███       | 119/388 [19:52<40:20,  9.00s/it]
 31%|███       | 120/388 [20:01<40:16,  9.02s/it]
                                                 
{'loss': 0.3303, 'grad_norm': 0.8189808130264282, 'learning_rate': 6.968911917098447e-06, 'epoch': 0.62}

 31%|███       | 120/388 [20:01<40:16,  9.02s/it]
 31%|███       | 121/388 [20:10<39:30,  8.88s/it]
                                                 
{'loss': 0.584, 'grad_norm': 1.0733768939971924, 'learning_rate': 6.943005181347151e-06, 'epoch': 0.62}

 31%|███       | 121/388 [20:10<39:30,  8.88s/it]
 31%|███▏      | 122/388 [20:20<40:24,  9.11s/it]
                                                 
{'loss': 0.3183, 'grad_norm': 0.8364907503128052, 'learning_rate': 6.917098445595856e-06, 'epoch': 0.63}

 31%|███▏      | 122/388 [20:20<40:24,  9.11s/it]
 32%|███▏      | 123/388 [20:28<39:14,  8.89s/it]
                                                 
{'loss': 0.5751, 'grad_norm': 1.023781418800354, 'learning_rate': 6.89119170984456e-06, 'epoch': 0.63}

 32%|███▏      | 123/388 [20:28<39:14,  8.89s/it]
 32%|███▏      | 124/388 [20:38<40:47,  9.27s/it]
                                                 
{'loss': 0.3403, 'grad_norm': 0.9007506966590881, 'learning_rate': 6.865284974093265e-06, 'epoch': 0.64}

 32%|███▏      | 124/388 [20:38<40:47,  9.27s/it]
 32%|███▏      | 125/388 [20:49<42:54,  9.79s/it]
                                                 
{'loss': 0.7758, 'grad_norm': 1.030462384223938, 'learning_rate': 6.839378238341969e-06, 'epoch': 0.64}

 32%|███▏      | 125/388 [20:49<42:54,  9.79s/it]
 32%|███▏      | 126/388 [21:01<45:01, 10.31s/it]
                                                 
{'loss': 0.5537, 'grad_norm': 0.7548647522926331, 'learning_rate': 6.813471502590674e-06, 'epoch': 0.65}

 32%|███▏      | 126/388 [21:01<45:01, 10.31s/it]
 33%|███▎      | 127/388 [21:11<44:55, 10.33s/it]
                                                 
{'loss': 0.3662, 'grad_norm': 0.6818444132804871, 'learning_rate': 6.787564766839379e-06, 'epoch': 0.65}

 33%|███▎      | 127/388 [21:11<44:55, 10.33s/it]
 33%|███▎      | 128/388 [21:20<43:32, 10.05s/it]
                                                 
{'loss': 0.492, 'grad_norm': 0.6560463905334473, 'learning_rate': 6.761658031088083e-06, 'epoch': 0.66}

 33%|███▎      | 128/388 [21:20<43:32, 10.05s/it]
 33%|███▎      | 129/388 [21:30<42:56,  9.95s/it]
                                                 
{'loss': 0.592, 'grad_norm': 0.7523697018623352, 'learning_rate': 6.735751295336788e-06, 'epoch': 0.66}

 33%|███▎      | 129/388 [21:30<42:56,  9.95s/it]
 34%|███▎      | 130/388 [21:40<42:45,  9.94s/it]
                                                 
{'loss': 0.3868, 'grad_norm': 0.8241267204284668, 'learning_rate': 6.709844559585493e-06, 'epoch': 0.67}

 34%|███▎      | 130/388 [21:40<42:45,  9.94s/it]
 34%|███▍      | 131/388 [21:49<41:04,  9.59s/it]
                                                 
{'loss': 0.6122, 'grad_norm': 0.8949790596961975, 'learning_rate': 6.683937823834198e-06, 'epoch': 0.68}

 34%|███▍      | 131/388 [21:49<41:04,  9.59s/it]
 34%|███▍      | 132/388 [21:57<39:12,  9.19s/it]
                                                 
{'loss': 0.7691, 'grad_norm': 0.934779942035675, 'learning_rate': 6.658031088082902e-06, 'epoch': 0.68}

 34%|███▍      | 132/388 [21:57<39:12,  9.19s/it]
 34%|███▍      | 133/388 [22:05<37:09,  8.74s/it]
                                                 
{'loss': 0.3908, 'grad_norm': 0.9307014346122742, 'learning_rate': 6.632124352331607e-06, 'epoch': 0.69}

 34%|███▍      | 133/388 [22:05<37:09,  8.74s/it]
 35%|███▍      | 134/388 [22:12<35:36,  8.41s/it]
                                                 
{'loss': 0.3137, 'grad_norm': 0.841033935546875, 'learning_rate': 6.6062176165803115e-06, 'epoch': 0.69}

 35%|███▍      | 134/388 [22:12<35:36,  8.41s/it]
 35%|███▍      | 135/388 [22:22<37:08,  8.81s/it]
                                                 
{'loss': 0.7522, 'grad_norm': 1.0251457691192627, 'learning_rate': 6.5803108808290166e-06, 'epoch': 0.7}

 35%|███▍      | 135/388 [22:22<37:08,  8.81s/it]
 35%|███▌      | 136/388 [22:31<37:33,  8.94s/it]
                                                 
{'loss': 0.5229, 'grad_norm': 0.8711403012275696, 'learning_rate': 6.554404145077721e-06, 'epoch': 0.7}

 35%|███▌      | 136/388 [22:31<37:33,  8.94s/it]
 35%|███▌      | 137/388 [22:42<39:08,  9.36s/it]
                                                 
{'loss': 0.532, 'grad_norm': 0.9510254859924316, 'learning_rate': 6.528497409326425e-06, 'epoch': 0.71}

 35%|███▌      | 137/388 [22:42<39:08,  9.36s/it]
 36%|███▌      | 138/388 [22:52<40:02,  9.61s/it]
                                                 
{'loss': 0.5194, 'grad_norm': 0.8545109033584595, 'learning_rate': 6.50259067357513e-06, 'epoch': 0.71}

 36%|███▌      | 138/388 [22:52<40:02,  9.61s/it]
 36%|███▌      | 139/388 [23:01<39:29,  9.52s/it]
                                                 
{'loss': 0.698, 'grad_norm': 0.8434369564056396, 'learning_rate': 6.476683937823834e-06, 'epoch': 0.72}

 36%|███▌      | 139/388 [23:01<39:29,  9.52s/it]
 36%|███▌      | 140/388 [23:10<38:10,  9.24s/it]
                                                 
{'loss': 0.2716, 'grad_norm': 0.7308311462402344, 'learning_rate': 6.450777202072539e-06, 'epoch': 0.72}

 36%|███▌      | 140/388 [23:10<38:10,  9.24s/it]
 36%|███▋      | 141/388 [23:21<39:44,  9.65s/it]
                                                 
{'loss': 0.8146, 'grad_norm': 0.8369062542915344, 'learning_rate': 6.4248704663212435e-06, 'epoch': 0.73}

 36%|███▋      | 141/388 [23:21<39:44,  9.65s/it]
 37%|███▋      | 142/388 [23:28<36:44,  8.96s/it]
                                                 
{'loss': 0.4138, 'grad_norm': 0.9072701930999756, 'learning_rate': 6.398963730569949e-06, 'epoch': 0.73}

 37%|███▋      | 142/388 [23:28<36:44,  8.96s/it]
 37%|███▋      | 143/388 [23:41<41:31, 10.17s/it]
                                                 
{'loss': 0.6456, 'grad_norm': 1.0603035688400269, 'learning_rate': 6.373056994818654e-06, 'epoch': 0.74}

 37%|███▋      | 143/388 [23:41<41:31, 10.17s/it]
 37%|███▋      | 144/388 [23:49<38:32,  9.48s/it]
                                                 
{'loss': 0.3786, 'grad_norm': 1.019129991531372, 'learning_rate': 6.347150259067359e-06, 'epoch': 0.74}

 37%|███▋      | 144/388 [23:49<38:32,  9.48s/it]
 37%|███▋      | 145/388 [23:59<39:34,  9.77s/it]
                                                 
{'loss': 0.3916, 'grad_norm': 0.7640241980552673, 'learning_rate': 6.321243523316063e-06, 'epoch': 0.75}

 37%|███▋      | 145/388 [23:59<39:34,  9.77s/it]
 38%|███▊      | 146/388 [24:07<37:30,  9.30s/it]
                                                 
{'loss': 0.7937, 'grad_norm': 2.3868517875671387, 'learning_rate': 6.295336787564768e-06, 'epoch': 0.75}

 38%|███▊      | 146/388 [24:07<37:30,  9.30s/it]
 38%|███▊      | 147/388 [24:16<36:44,  9.15s/it]
                                                 
{'loss': 0.542, 'grad_norm': 0.9608807563781738, 'learning_rate': 6.269430051813472e-06, 'epoch': 0.76}

 38%|███▊      | 147/388 [24:16<36:44,  9.15s/it]
 38%|███▊      | 148/388 [24:27<38:19,  9.58s/it]
                                                 
{'loss': 0.3933, 'grad_norm': 0.971348762512207, 'learning_rate': 6.243523316062176e-06, 'epoch': 0.76}

 38%|███▊      | 148/388 [24:27<38:19,  9.58s/it]
 38%|███▊      | 149/388 [24:37<38:53,  9.76s/it]
                                                 
{'loss': 0.4675, 'grad_norm': 1.2680869102478027, 'learning_rate': 6.217616580310881e-06, 'epoch': 0.77}

 38%|███▊      | 149/388 [24:37<38:53,  9.76s/it]
 39%|███▊      | 150/388 [24:47<39:11,  9.88s/it]
                                                 
{'loss': 0.8772, 'grad_norm': 0.8467795848846436, 'learning_rate': 6.191709844559586e-06, 'epoch': 0.77}

 39%|███▊      | 150/388 [24:47<39:11,  9.88s/it]
 39%|███▉      | 151/388 [24:57<39:21,  9.97s/it]
                                                 
{'loss': 0.5283, 'grad_norm': 0.9661357998847961, 'learning_rate': 6.165803108808291e-06, 'epoch': 0.78}

 39%|███▉      | 151/388 [24:57<39:21,  9.97s/it]
 39%|███▉      | 152/388 [25:06<38:01,  9.67s/it]
                                                 
{'loss': 0.522, 'grad_norm': 0.7277345061302185, 'learning_rate': 6.139896373056995e-06, 'epoch': 0.78}

 39%|███▉      | 152/388 [25:06<38:01,  9.67s/it]
 39%|███▉      | 153/388 [25:16<37:40,  9.62s/it]
                                                 
{'loss': 0.6123, 'grad_norm': 0.8264330625534058, 'learning_rate': 6.113989637305699e-06, 'epoch': 0.79}

 39%|███▉      | 153/388 [25:16<37:40,  9.62s/it]
 40%|███▉      | 154/388 [25:26<38:18,  9.82s/it]
                                                 
{'loss': 0.6348, 'grad_norm': 0.9117377400398254, 'learning_rate': 6.088082901554405e-06, 'epoch': 0.79}

 40%|███▉      | 154/388 [25:26<38:18,  9.82s/it]
 40%|███▉      | 155/388 [25:38<40:14, 10.36s/it]
                                                 
{'loss': 0.365, 'grad_norm': 0.7495144009590149, 'learning_rate': 6.06217616580311e-06, 'epoch': 0.8}

 40%|███▉      | 155/388 [25:38<40:14, 10.36s/it]
 40%|████      | 156/388 [25:47<38:19,  9.91s/it]
                                                 
{'loss': 0.629, 'grad_norm': 0.9202139377593994, 'learning_rate': 6.036269430051814e-06, 'epoch': 0.8}

 40%|████      | 156/388 [25:47<38:19,  9.91s/it]
 40%|████      | 157/388 [25:58<39:45, 10.33s/it]
                                                 
{'loss': 0.4574, 'grad_norm': 0.817642092704773, 'learning_rate': 6.0103626943005185e-06, 'epoch': 0.81}

 40%|████      | 157/388 [25:58<39:45, 10.33s/it]
 41%|████      | 158/388 [26:06<37:31,  9.79s/it]
                                                 
{'loss': 0.3118, 'grad_norm': 0.7683699131011963, 'learning_rate': 5.9844559585492235e-06, 'epoch': 0.81}

 41%|████      | 158/388 [26:06<37:31,  9.79s/it]
 41%|████      | 159/388 [26:16<36:56,  9.68s/it]
                                                 
{'loss': 0.5434, 'grad_norm': 0.7886037826538086, 'learning_rate': 5.958549222797928e-06, 'epoch': 0.82}

 41%|████      | 159/388 [26:16<36:56,  9.68s/it]
 41%|████      | 160/388 [26:29<40:37, 10.69s/it]
                                                 
{'loss': 0.3869, 'grad_norm': 0.79804527759552, 'learning_rate': 5.932642487046633e-06, 'epoch': 0.82}

 41%|████      | 160/388 [26:29<40:37, 10.69s/it]
 41%|████▏     | 161/388 [26:37<37:53, 10.01s/it]
                                                 
{'loss': 0.3377, 'grad_norm': 0.7386977076530457, 'learning_rate': 5.906735751295337e-06, 'epoch': 0.83}

 41%|████▏     | 161/388 [26:37<37:53, 10.01s/it]
 42%|████▏     | 162/388 [26:45<35:00,  9.29s/it]
                                                 
{'loss': 0.2733, 'grad_norm': 0.9634663462638855, 'learning_rate': 5.880829015544042e-06, 'epoch': 0.84}

 42%|████▏     | 162/388 [26:45<35:00,  9.29s/it]
 42%|████▏     | 163/388 [26:53<33:17,  8.88s/it]
                                                 
{'loss': 0.2873, 'grad_norm': 0.9033777117729187, 'learning_rate': 5.854922279792746e-06, 'epoch': 0.84}

 42%|████▏     | 163/388 [26:53<33:17,  8.88s/it]
 42%|████▏     | 164/388 [27:01<32:58,  8.83s/it]
                                                 
{'loss': 0.3791, 'grad_norm': 0.7759392857551575, 'learning_rate': 5.8290155440414505e-06, 'epoch': 0.85}

 42%|████▏     | 164/388 [27:01<32:58,  8.83s/it]
 43%|████▎     | 165/388 [27:11<33:38,  9.05s/it]
                                                 
{'loss': 0.555, 'grad_norm': 0.9811961650848389, 'learning_rate': 5.8031088082901555e-06, 'epoch': 0.85}

 43%|████▎     | 165/388 [27:11<33:38,  9.05s/it]
 43%|████▎     | 166/388 [27:24<38:02, 10.28s/it]
                                                 
{'loss': 0.4975, 'grad_norm': 0.8664981126785278, 'learning_rate': 5.7772020725388614e-06, 'epoch': 0.86}

 43%|████▎     | 166/388 [27:24<38:02, 10.28s/it]
 43%|████▎     | 167/388 [27:34<37:15, 10.11s/it]
                                                 
{'loss': 0.9302, 'grad_norm': 1.1479095220565796, 'learning_rate': 5.751295336787566e-06, 'epoch': 0.86}

 43%|████▎     | 167/388 [27:34<37:15, 10.11s/it]
 43%|████▎     | 168/388 [27:44<37:13, 10.15s/it]
                                                 
{'loss': 0.3664, 'grad_norm': 0.8097944855690002, 'learning_rate': 5.72538860103627e-06, 'epoch': 0.87}

 43%|████▎     | 168/388 [27:44<37:13, 10.15s/it]
 44%|████▎     | 169/388 [27:55<37:33, 10.29s/it]
                                                 
{'loss': 0.359, 'grad_norm': 1.001518726348877, 'learning_rate': 5.699481865284975e-06, 'epoch': 0.87}

 44%|████▎     | 169/388 [27:55<37:33, 10.29s/it]
 44%|████▍     | 170/388 [28:05<37:42, 10.38s/it]
                                                 
{'loss': 0.3865, 'grad_norm': 0.7037398219108582, 'learning_rate': 5.673575129533679e-06, 'epoch': 0.88}

 44%|████▍     | 170/388 [28:05<37:42, 10.38s/it]
 44%|████▍     | 171/388 [28:18<39:50, 11.01s/it]
                                                 
{'loss': 0.2764, 'grad_norm': 0.6667627096176147, 'learning_rate': 5.647668393782384e-06, 'epoch': 0.88}

 44%|████▍     | 171/388 [28:18<39:50, 11.01s/it]
 44%|████▍     | 172/388 [28:28<38:15, 10.63s/it]
                                                 
{'loss': 0.3071, 'grad_norm': 0.8470035195350647, 'learning_rate': 5.621761658031088e-06, 'epoch': 0.89}

 44%|████▍     | 172/388 [28:28<38:15, 10.63s/it]
 45%|████▍     | 173/388 [28:37<37:11, 10.38s/it]
                                                 
{'loss': 0.2946, 'grad_norm': 0.9179544448852539, 'learning_rate': 5.5958549222797934e-06, 'epoch': 0.89}

 45%|████▍     | 173/388 [28:37<37:11, 10.38s/it]
 45%|████▍     | 174/388 [28:49<38:44, 10.86s/it]
                                                 
{'loss': 0.3683, 'grad_norm': 0.7452818155288696, 'learning_rate': 5.569948186528498e-06, 'epoch': 0.9}

 45%|████▍     | 174/388 [28:49<38:44, 10.86s/it]
 45%|████▌     | 175/388 [29:02<40:15, 11.34s/it]
                                                 
{'loss': 0.6684, 'grad_norm': 0.8301803469657898, 'learning_rate': 5.544041450777202e-06, 'epoch': 0.9}

 45%|████▌     | 175/388 [29:02<40:15, 11.34s/it]
 45%|████▌     | 176/388 [29:13<39:36, 11.21s/it]
                                                 
{'loss': 0.5862, 'grad_norm': 0.9258779883384705, 'learning_rate': 5.518134715025907e-06, 'epoch': 0.91}

 45%|████▌     | 176/388 [29:13<39:36, 11.21s/it]
 46%|████▌     | 177/388 [29:22<36:59, 10.52s/it]
                                                 
{'loss': 0.2908, 'grad_norm': 0.7997896671295166, 'learning_rate': 5.492227979274611e-06, 'epoch': 0.91}

 46%|████▌     | 177/388 [29:22<36:59, 10.52s/it]
 46%|████▌     | 178/388 [29:31<35:12, 10.06s/it]
                                                 
{'loss': 0.5515, 'grad_norm': 0.9105134606361389, 'learning_rate': 5.466321243523317e-06, 'epoch': 0.92}

 46%|████▌     | 178/388 [29:31<35:12, 10.06s/it]
 46%|████▌     | 179/388 [29:40<34:07,  9.80s/it]
                                                 
{'loss': 0.6012, 'grad_norm': 0.83064866065979, 'learning_rate': 5.440414507772021e-06, 'epoch': 0.92}

 46%|████▌     | 179/388 [29:40<34:07,  9.80s/it]
 46%|████▋     | 180/388 [29:50<34:42, 10.01s/it]
                                                 
{'loss': 0.5579, 'grad_norm': 0.760399580001831, 'learning_rate': 5.414507772020726e-06, 'epoch': 0.93}

 46%|████▋     | 180/388 [29:50<34:42, 10.01s/it]
 47%|████▋     | 181/388 [29:59<33:09,  9.61s/it]
                                                 
{'loss': 0.4926, 'grad_norm': 0.9484172463417053, 'learning_rate': 5.3886010362694305e-06, 'epoch': 0.93}

 47%|████▋     | 181/388 [29:59<33:09,  9.61s/it]
 47%|████▋     | 182/388 [30:12<36:13, 10.55s/it]
                                                 
{'loss': 0.4309, 'grad_norm': 0.824654757976532, 'learning_rate': 5.3626943005181356e-06, 'epoch': 0.94}

 47%|████▋     | 182/388 [30:12<36:13, 10.55s/it]
 47%|████▋     | 183/388 [30:19<32:43,  9.58s/it]
                                                 
{'loss': 0.4282, 'grad_norm': 0.951941728591919, 'learning_rate': 5.33678756476684e-06, 'epoch': 0.94}

 47%|████▋     | 183/388 [30:19<32:43,  9.58s/it]
 47%|████▋     | 184/388 [30:28<31:42,  9.33s/it]
                                                 
{'loss': 0.4861, 'grad_norm': 1.0191141366958618, 'learning_rate': 5.310880829015545e-06, 'epoch': 0.95}

 47%|████▋     | 184/388 [30:28<31:42,  9.33s/it]
 48%|████▊     | 185/388 [30:38<32:52,  9.71s/it]
                                                 
{'loss': 0.7317, 'grad_norm': 0.9370522499084473, 'learning_rate': 5.284974093264249e-06, 'epoch': 0.95}

 48%|████▊     | 185/388 [30:38<32:52,  9.71s/it]
 48%|████▊     | 186/388 [30:47<31:58,  9.50s/it]
                                                 
{'loss': 0.7262, 'grad_norm': 1.106014370918274, 'learning_rate': 5.259067357512953e-06, 'epoch': 0.96}

 48%|████▊     | 186/388 [30:47<31:58,  9.50s/it]
 48%|████▊     | 187/388 [31:00<35:02, 10.46s/it]
                                                 
{'loss': 0.4155, 'grad_norm': 0.8690452575683594, 'learning_rate': 5.233160621761658e-06, 'epoch': 0.96}

 48%|████▊     | 187/388 [31:00<35:02, 10.46s/it]
 48%|████▊     | 188/388 [31:09<32:56,  9.88s/it]
                                                 
{'loss': 0.7695, 'grad_norm': 1.0348173379898071, 'learning_rate': 5.2072538860103625e-06, 'epoch': 0.97}

 48%|████▊     | 188/388 [31:09<32:56,  9.88s/it]
 49%|████▊     | 189/388 [31:22<35:49, 10.80s/it]
                                                 
{'loss': 0.3921, 'grad_norm': 0.7686324119567871, 'learning_rate': 5.1813471502590676e-06, 'epoch': 0.97}

 49%|████▊     | 189/388 [31:22<35:49, 10.80s/it]
 49%|████▉     | 190/388 [31:31<33:51, 10.26s/it]
                                                 
{'loss': 0.7081, 'grad_norm': 1.04275643825531, 'learning_rate': 5.155440414507773e-06, 'epoch': 0.98}

 49%|████▉     | 190/388 [31:31<33:51, 10.26s/it]
 49%|████▉     | 191/388 [31:42<35:14, 10.74s/it]
                                                 
{'loss': 0.5294, 'grad_norm': 1.056119441986084, 'learning_rate': 5.129533678756478e-06, 'epoch': 0.98}

 49%|████▉     | 191/388 [31:42<35:14, 10.74s/it]
 49%|████▉     | 192/388 [31:53<34:24, 10.53s/it]
                                                 
{'loss': 0.3886, 'grad_norm': 0.7871842384338379, 'learning_rate': 5.103626943005182e-06, 'epoch': 0.99}

 49%|████▉     | 192/388 [31:53<34:24, 10.53s/it]
 50%|████▉     | 193/388 [32:01<32:11,  9.91s/it]
                                                 
{'loss': 0.4465, 'grad_norm': 1.0403472185134888, 'learning_rate': 5.077720207253887e-06, 'epoch': 0.99}

 50%|████▉     | 193/388 [32:01<32:11,  9.91s/it]
 50%|█████     | 194/388 [32:10<30:44,  9.51s/it]
                                                 
{'loss': 0.3627, 'grad_norm': 1.0968815088272095, 'learning_rate': 5.051813471502591e-06, 'epoch': 1.0}

 50%|█████     | 194/388 [32:10<30:44,  9.51s/it]
 50%|█████     | 195/388 [32:21<32:32, 10.12s/it]
                                                 
{'loss': 0.5637, 'grad_norm': 0.8739809393882751, 'learning_rate': 5.025906735751296e-06, 'epoch': 1.01}

 50%|█████     | 195/388 [32:21<32:32, 10.12s/it]
 51%|█████     | 196/388 [32:31<31:46,  9.93s/it]
                                                 
{'loss': 0.3663, 'grad_norm': 1.0730175971984863, 'learning_rate': 5e-06, 'epoch': 1.01}

 51%|█████     | 196/388 [32:31<31:46,  9.93s/it]
 51%|█████     | 197/388 [32:41<32:13, 10.12s/it]
                                                 
{'loss': 0.2489, 'grad_norm': 0.642601490020752, 'learning_rate': 4.974093264248705e-06, 'epoch': 1.02}

 51%|█████     | 197/388 [32:41<32:13, 10.12s/it]
 51%|█████     | 198/388 [32:52<32:25, 10.24s/it]
                                                 
{'loss': 0.4683, 'grad_norm': 1.270029902458191, 'learning_rate': 4.94818652849741e-06, 'epoch': 1.02}

 51%|█████     | 198/388 [32:52<32:25, 10.24s/it]
 51%|█████▏    | 199/388 [33:01<31:18,  9.94s/it]
                                                 
{'loss': 0.3966, 'grad_norm': 0.7098864912986755, 'learning_rate': 4.922279792746114e-06, 'epoch': 1.03}

 51%|█████▏    | 199/388 [33:01<31:18,  9.94s/it]
 52%|█████▏    | 200/388 [33:09<29:17,  9.35s/it]
                                                 
{'loss': 0.5352, 'grad_norm': 1.1087995767593384, 'learning_rate': 4.896373056994819e-06, 'epoch': 1.03}

 52%|█████▏    | 200/388 [33:09<29:17,  9.35s/it]
 52%|█████▏    | 201/388 [33:20<30:54,  9.92s/it]
                                                 
{'loss': 0.3561, 'grad_norm': 0.7115294337272644, 'learning_rate': 4.870466321243524e-06, 'epoch': 1.04}

 52%|█████▏    | 201/388 [33:20<30:54,  9.92s/it]
 52%|█████▏    | 202/388 [33:29<29:30,  9.52s/it]
                                                 
{'loss': 0.3001, 'grad_norm': 0.7292823791503906, 'learning_rate': 4.844559585492228e-06, 'epoch': 1.04}

 52%|█████▏    | 202/388 [33:29<29:30,  9.52s/it]
 52%|█████▏    | 203/388 [33:39<30:13,  9.80s/it]
                                                 
{'loss': 0.5047, 'grad_norm': 0.8520799875259399, 'learning_rate': 4.818652849740933e-06, 'epoch': 1.05}

 52%|█████▏    | 203/388 [33:39<30:13,  9.80s/it]
 53%|█████▎    | 204/388 [33:47<28:23,  9.26s/it]
                                                 
{'loss': 0.4968, 'grad_norm': 0.9761709570884705, 'learning_rate': 4.7927461139896375e-06, 'epoch': 1.05}

 53%|█████▎    | 204/388 [33:47<28:23,  9.26s/it]
 53%|█████▎    | 205/388 [34:00<31:22, 10.29s/it]
                                                 
{'loss': 0.4803, 'grad_norm': 0.941666841506958, 'learning_rate': 4.766839378238342e-06, 'epoch': 1.06}

 53%|█████▎    | 205/388 [34:00<31:22, 10.29s/it]
 53%|█████▎    | 206/388 [34:10<30:52, 10.18s/it]
                                                 
{'loss': 0.2413, 'grad_norm': 0.857654869556427, 'learning_rate': 4.740932642487048e-06, 'epoch': 1.06}

 53%|█████▎    | 206/388 [34:10<30:52, 10.18s/it]
 53%|█████▎    | 207/388 [34:22<32:12, 10.68s/it]
                                                 
{'loss': 0.501, 'grad_norm': 0.8872515559196472, 'learning_rate': 4.715025906735752e-06, 'epoch': 1.07}

 53%|█████▎    | 207/388 [34:22<32:12, 10.68s/it]
 54%|█████▎    | 208/388 [34:31<30:59, 10.33s/it]
                                                 
{'loss': 0.3562, 'grad_norm': 0.6613529920578003, 'learning_rate': 4.689119170984456e-06, 'epoch': 1.07}

 54%|█████▎    | 208/388 [34:31<30:59, 10.33s/it]
 54%|█████▍    | 209/388 [34:40<29:27,  9.88s/it]
                                                 
{'loss': 0.4087, 'grad_norm': 1.10734224319458, 'learning_rate': 4.663212435233161e-06, 'epoch': 1.08}

 54%|█████▍    | 209/388 [34:40<29:27,  9.88s/it]
 54%|█████▍    | 210/388 [34:49<28:23,  9.57s/it]
                                                 
{'loss': 0.4626, 'grad_norm': 1.0690429210662842, 'learning_rate': 4.637305699481865e-06, 'epoch': 1.08}

 54%|█████▍    | 210/388 [34:49<28:23,  9.57s/it]
 54%|█████▍    | 211/388 [34:56<26:22,  8.94s/it]
                                                 
{'loss': 0.4665, 'grad_norm': 1.0308058261871338, 'learning_rate': 4.61139896373057e-06, 'epoch': 1.09}

 54%|█████▍    | 211/388 [34:56<26:22,  8.94s/it]
 55%|█████▍    | 212/388 [35:03<24:38,  8.40s/it]
                                                 
{'loss': 0.1303, 'grad_norm': 0.9486850500106812, 'learning_rate': 4.585492227979275e-06, 'epoch': 1.09}

 55%|█████▍    | 212/388 [35:03<24:38,  8.40s/it]
 55%|█████▍    | 213/388 [35:14<26:23,  9.05s/it]
                                                 
{'loss': 0.4269, 'grad_norm': 1.4386814832687378, 'learning_rate': 4.55958549222798e-06, 'epoch': 1.1}

 55%|█████▍    | 213/388 [35:14<26:23,  9.05s/it]
 55%|█████▌    | 214/388 [35:21<24:42,  8.52s/it]
                                                 
{'loss': 0.2116, 'grad_norm': 1.227901577949524, 'learning_rate': 4.533678756476685e-06, 'epoch': 1.1}

 55%|█████▌    | 214/388 [35:21<24:42,  8.52s/it]
 55%|█████▌    | 215/388 [35:29<24:18,  8.43s/it]
                                                 
{'loss': 0.3246, 'grad_norm': 0.983674168586731, 'learning_rate': 4.507772020725389e-06, 'epoch': 1.11}

 55%|█████▌    | 215/388 [35:29<24:18,  8.43s/it]
 56%|█████▌    | 216/388 [35:39<24:43,  8.62s/it]
                                                 
{'loss': 0.4171, 'grad_norm': 1.1157034635543823, 'learning_rate': 4.481865284974093e-06, 'epoch': 1.11}

 56%|█████▌    | 216/388 [35:39<24:43,  8.62s/it]
 56%|█████▌    | 217/388 [35:48<25:35,  8.98s/it]
                                                 
{'loss': 0.5354, 'grad_norm': 0.7598445415496826, 'learning_rate': 4.455958549222798e-06, 'epoch': 1.12}

 56%|█████▌    | 217/388 [35:48<25:35,  8.98s/it]
 56%|█████▌    | 218/388 [35:58<25:42,  9.07s/it]
                                                 
{'loss': 0.55, 'grad_norm': 1.0720133781433105, 'learning_rate': 4.430051813471503e-06, 'epoch': 1.12}

 56%|█████▌    | 218/388 [35:58<25:42,  9.07s/it]
 56%|█████▋    | 219/388 [36:06<24:32,  8.71s/it]
                                                 
{'loss': 0.2955, 'grad_norm': 0.7510941624641418, 'learning_rate': 4.404145077720207e-06, 'epoch': 1.13}

 56%|█████▋    | 219/388 [36:06<24:32,  8.71s/it]
 57%|█████▋    | 220/388 [36:13<23:35,  8.43s/it]
                                                 
{'loss': 0.218, 'grad_norm': 0.9709896445274353, 'learning_rate': 4.3782383419689124e-06, 'epoch': 1.13}

 57%|█████▋    | 220/388 [36:13<23:35,  8.43s/it]
 57%|█████▋    | 221/388 [36:22<23:22,  8.40s/it]
                                                 
{'loss': 0.3604, 'grad_norm': 1.016642689704895, 'learning_rate': 4.352331606217617e-06, 'epoch': 1.14}

 57%|█████▋    | 221/388 [36:22<23:22,  8.40s/it]
 57%|█████▋    | 222/388 [36:34<26:18,  9.51s/it]
                                                 
{'loss': 0.2208, 'grad_norm': 0.7355630993843079, 'learning_rate': 4.326424870466322e-06, 'epoch': 1.14}

 57%|█████▋    | 222/388 [36:34<26:18,  9.51s/it]
 57%|█████▋    | 223/388 [36:43<25:37,  9.32s/it]
                                                 
{'loss': 0.2242, 'grad_norm': 0.7518354654312134, 'learning_rate': 4.300518134715026e-06, 'epoch': 1.15}

 57%|█████▋    | 223/388 [36:43<25:37,  9.32s/it]
 58%|█████▊    | 224/388 [36:54<27:14,  9.97s/it]
                                                 
{'loss': 0.4313, 'grad_norm': 0.8330957293510437, 'learning_rate': 4.274611398963731e-06, 'epoch': 1.15}

 58%|█████▊    | 224/388 [36:54<27:14,  9.97s/it]
 58%|█████▊    | 225/388 [37:03<26:06,  9.61s/it]
                                                 
{'loss': 0.5839, 'grad_norm': 0.739578902721405, 'learning_rate': 4.248704663212436e-06, 'epoch': 1.16}

 58%|█████▊    | 225/388 [37:03<26:06,  9.61s/it]
 58%|█████▊    | 226/388 [37:12<25:46,  9.54s/it]
                                                 
{'loss': 0.417, 'grad_norm': 0.8069799542427063, 'learning_rate': 4.22279792746114e-06, 'epoch': 1.16}

 58%|█████▊    | 226/388 [37:12<25:46,  9.54s/it]
 59%|█████▊    | 227/388 [37:21<24:53,  9.28s/it]
                                                 
{'loss': 0.6776, 'grad_norm': 0.9648683071136475, 'learning_rate': 4.1968911917098444e-06, 'epoch': 1.17}

 59%|█████▊    | 227/388 [37:21<24:53,  9.28s/it]
 59%|█████▉    | 228/388 [37:30<24:36,  9.23s/it]
                                                 
{'loss': 0.5574, 'grad_norm': 1.0480639934539795, 'learning_rate': 4.1709844559585495e-06, 'epoch': 1.18}

 59%|█████▉    | 228/388 [37:30<24:36,  9.23s/it]
 59%|█████▉    | 229/388 [37:42<26:34, 10.03s/it]
                                                 
{'loss': 0.484, 'grad_norm': 0.761233389377594, 'learning_rate': 4.145077720207254e-06, 'epoch': 1.18}

 59%|█████▉    | 229/388 [37:42<26:34, 10.03s/it]
 59%|█████▉    | 230/388 [37:50<24:58,  9.48s/it]
                                                 
{'loss': 0.6625, 'grad_norm': 0.8255932331085205, 'learning_rate': 4.119170984455959e-06, 'epoch': 1.19}

 59%|█████▉    | 230/388 [37:50<24:58,  9.48s/it]
 60%|█████▉    | 231/388 [37:59<24:26,  9.34s/it]
                                                 
{'loss': 0.2215, 'grad_norm': 0.7817264199256897, 'learning_rate': 4.093264248704664e-06, 'epoch': 1.19}

 60%|█████▉    | 231/388 [37:59<24:26,  9.34s/it]
 60%|█████▉    | 232/388 [38:08<24:10,  9.30s/it]
                                                 
{'loss': 0.6005, 'grad_norm': 0.8821240067481995, 'learning_rate': 4.067357512953368e-06, 'epoch': 1.2}

 60%|█████▉    | 232/388 [38:08<24:10,  9.30s/it]
 60%|██████    | 233/388 [38:18<24:16,  9.39s/it]
                                                 
{'loss': 0.3526, 'grad_norm': 1.122470736503601, 'learning_rate': 4.041450777202073e-06, 'epoch': 1.2}

 60%|██████    | 233/388 [38:18<24:16,  9.39s/it]
 60%|██████    | 234/388 [38:29<25:37,  9.99s/it]
                                                 
{'loss': 0.5916, 'grad_norm': 0.7580505609512329, 'learning_rate': 4.015544041450777e-06, 'epoch': 1.21}

 60%|██████    | 234/388 [38:29<25:37,  9.99s/it]
 61%|██████    | 235/388 [38:39<24:59,  9.80s/it]
                                                 
{'loss': 0.5727, 'grad_norm': 0.6954234838485718, 'learning_rate': 3.989637305699482e-06, 'epoch': 1.21}

 61%|██████    | 235/388 [38:39<24:59,  9.80s/it]
 61%|██████    | 236/388 [38:48<24:17,  9.59s/it]
                                                 
{'loss': 0.2446, 'grad_norm': 0.8904476165771484, 'learning_rate': 3.963730569948187e-06, 'epoch': 1.22}

 61%|██████    | 236/388 [38:48<24:17,  9.59s/it]
 61%|██████    | 237/388 [38:58<24:29,  9.73s/it]
                                                 
{'loss': 0.5475, 'grad_norm': 1.04800546169281, 'learning_rate': 3.937823834196892e-06, 'epoch': 1.22}

 61%|██████    | 237/388 [38:58<24:29,  9.73s/it]
 61%|██████▏   | 238/388 [39:06<23:12,  9.28s/it]
                                                 
{'loss': 0.2318, 'grad_norm': 0.9867790937423706, 'learning_rate': 3.911917098445596e-06, 'epoch': 1.23}

 61%|██████▏   | 238/388 [39:06<23:12,  9.28s/it]
 62%|██████▏   | 239/388 [39:16<23:27,  9.45s/it]
                                                 
{'loss': 0.4378, 'grad_norm': 0.8790474534034729, 'learning_rate': 3.886010362694301e-06, 'epoch': 1.23}

 62%|██████▏   | 239/388 [39:16<23:27,  9.45s/it]
 62%|██████▏   | 240/388 [39:28<25:18, 10.26s/it]
                                                 
{'loss': 0.4314, 'grad_norm': 0.9075559377670288, 'learning_rate': 3.860103626943005e-06, 'epoch': 1.24}

 62%|██████▏   | 240/388 [39:28<25:18, 10.26s/it]
 62%|██████▏   | 241/388 [39:37<23:59,  9.79s/it]
                                                 
{'loss': 0.2503, 'grad_norm': 0.8689141273498535, 'learning_rate': 3.83419689119171e-06, 'epoch': 1.24}

 62%|██████▏   | 241/388 [39:37<23:59,  9.79s/it]
 62%|██████▏   | 242/388 [39:45<22:57,  9.43s/it]
                                                 
{'loss': 0.406, 'grad_norm': 0.9015355706214905, 'learning_rate': 3.808290155440415e-06, 'epoch': 1.25}

 62%|██████▏   | 242/388 [39:45<22:57,  9.43s/it]
 63%|██████▎   | 243/388 [39:58<25:01, 10.36s/it]
                                                 
{'loss': 0.2272, 'grad_norm': 0.8865298628807068, 'learning_rate': 3.7823834196891194e-06, 'epoch': 1.25}

 63%|██████▎   | 243/388 [39:58<25:01, 10.36s/it]
 63%|██████▎   | 244/388 [40:06<23:22,  9.74s/it]
                                                 
{'loss': 0.5826, 'grad_norm': 0.9343792796134949, 'learning_rate': 3.756476683937824e-06, 'epoch': 1.26}

 63%|██████▎   | 244/388 [40:06<23:22,  9.74s/it]
 63%|██████▎   | 245/388 [40:17<23:43,  9.95s/it]
                                                 
{'loss': 0.3145, 'grad_norm': 0.7708160877227783, 'learning_rate': 3.7305699481865287e-06, 'epoch': 1.26}

 63%|██████▎   | 245/388 [40:17<23:43,  9.95s/it]
 63%|██████▎   | 246/388 [40:26<23:16,  9.84s/it]
                                                 
{'loss': 0.3848, 'grad_norm': 0.7378104329109192, 'learning_rate': 3.7046632124352333e-06, 'epoch': 1.27}

 63%|██████▎   | 246/388 [40:26<23:16,  9.84s/it]
 64%|██████▎   | 247/388 [40:38<24:14, 10.32s/it]
                                                 
{'loss': 0.2129, 'grad_norm': 0.8466808199882507, 'learning_rate': 3.678756476683938e-06, 'epoch': 1.27}

 64%|██████▎   | 247/388 [40:38<24:14, 10.32s/it]
 64%|██████▍   | 248/388 [40:46<22:56,  9.83s/it]
                                                 
{'loss': 0.2917, 'grad_norm': 0.7484762072563171, 'learning_rate': 3.652849740932643e-06, 'epoch': 1.28}

 64%|██████▍   | 248/388 [40:46<22:56,  9.83s/it]
 64%|██████▍   | 249/388 [40:57<23:17, 10.05s/it]
                                                 
{'loss': 0.4156, 'grad_norm': 0.7783294320106506, 'learning_rate': 3.6269430051813476e-06, 'epoch': 1.28}

 64%|██████▍   | 249/388 [40:57<23:17, 10.05s/it]
 64%|██████▍   | 250/388 [41:06<22:45,  9.90s/it]
                                                 
{'loss': 0.4921, 'grad_norm': 0.8098664283752441, 'learning_rate': 3.6010362694300523e-06, 'epoch': 1.29}

 64%|██████▍   | 250/388 [41:06<22:45,  9.90s/it]
 65%|██████▍   | 251/388 [41:14<21:09,  9.26s/it]
                                                 
{'loss': 0.39, 'grad_norm': 0.8939624428749084, 'learning_rate': 3.575129533678757e-06, 'epoch': 1.29}

 65%|██████▍   | 251/388 [41:14<21:09,  9.26s/it]
 65%|██████▍   | 252/388 [41:27<23:11, 10.23s/it]
                                                 
{'loss': 0.3276, 'grad_norm': 0.7347182035446167, 'learning_rate': 3.549222797927461e-06, 'epoch': 1.3}

 65%|██████▍   | 252/388 [41:27<23:11, 10.23s/it]
 65%|██████▌   | 253/388 [41:37<22:56, 10.20s/it]
                                                 
{'loss': 0.4337, 'grad_norm': 0.8054161071777344, 'learning_rate': 3.5233160621761657e-06, 'epoch': 1.3}

 65%|██████▌   | 253/388 [41:37<22:56, 10.20s/it]
 65%|██████▌   | 254/388 [41:45<21:17,  9.54s/it]
                                                 
{'loss': 0.8233, 'grad_norm': 1.0053493976593018, 'learning_rate': 3.497409326424871e-06, 'epoch': 1.31}

 65%|██████▌   | 254/388 [41:45<21:17,  9.54s/it]
 66%|██████▌   | 255/388 [41:56<22:28, 10.14s/it]
                                                 
{'loss': 0.3386, 'grad_norm': 0.9622910022735596, 'learning_rate': 3.4715025906735754e-06, 'epoch': 1.31}

 66%|██████▌   | 255/388 [41:56<22:28, 10.14s/it]
 66%|██████▌   | 256/388 [42:07<22:23, 10.18s/it]
                                                 
{'loss': 0.2494, 'grad_norm': 0.6932687163352966, 'learning_rate': 3.44559585492228e-06, 'epoch': 1.32}

 66%|██████▌   | 256/388 [42:07<22:23, 10.18s/it]
 66%|██████▌   | 257/388 [42:15<21:00,  9.62s/it]
                                                 
{'loss': 0.2686, 'grad_norm': 0.9639967083930969, 'learning_rate': 3.4196891191709847e-06, 'epoch': 1.32}

 66%|██████▌   | 257/388 [42:15<21:00,  9.62s/it]
 66%|██████▋   | 258/388 [42:24<20:28,  9.45s/it]
                                                 
{'loss': 0.3529, 'grad_norm': 1.095858097076416, 'learning_rate': 3.3937823834196893e-06, 'epoch': 1.33}

 66%|██████▋   | 258/388 [42:24<20:28,  9.45s/it]
 67%|██████▋   | 259/388 [42:32<19:24,  9.03s/it]
                                                 
{'loss': 0.1832, 'grad_norm': 0.8448277711868286, 'learning_rate': 3.367875647668394e-06, 'epoch': 1.34}

 67%|██████▋   | 259/388 [42:32<19:24,  9.03s/it]
 67%|██████▋   | 260/388 [42:42<19:57,  9.35s/it]
                                                 
{'loss': 0.4491, 'grad_norm': 0.9058911204338074, 'learning_rate': 3.341968911917099e-06, 'epoch': 1.34}

 67%|██████▋   | 260/388 [42:42<19:57,  9.35s/it]
 67%|██████▋   | 261/388 [42:52<19:56,  9.42s/it]
                                                 
{'loss': 0.2908, 'grad_norm': 0.851806104183197, 'learning_rate': 3.3160621761658036e-06, 'epoch': 1.35}

 67%|██████▋   | 261/388 [42:52<19:56,  9.42s/it]
 68%|██████▊   | 262/388 [43:03<20:53,  9.95s/it]
                                                 
{'loss': 0.4985, 'grad_norm': 0.7144804000854492, 'learning_rate': 3.2901554404145083e-06, 'epoch': 1.35}

 68%|██████▊   | 262/388 [43:03<20:53,  9.95s/it]
 68%|██████▊   | 263/388 [43:13<21:06, 10.13s/it]
                                                 
{'loss': 0.3282, 'grad_norm': 0.8800524473190308, 'learning_rate': 3.2642487046632125e-06, 'epoch': 1.36}

 68%|██████▊   | 263/388 [43:13<21:06, 10.13s/it]
 68%|██████▊   | 264/388 [43:23<20:30,  9.93s/it]
                                                 
{'loss': 0.2549, 'grad_norm': 1.0444000959396362, 'learning_rate': 3.238341968911917e-06, 'epoch': 1.36}

 68%|██████▊   | 264/388 [43:23<20:30,  9.93s/it]
 68%|██████▊   | 265/388 [43:32<19:55,  9.72s/it]
                                                 
{'loss': 0.2926, 'grad_norm': 0.9430922269821167, 'learning_rate': 3.2124352331606218e-06, 'epoch': 1.37}

 68%|██████▊   | 265/388 [43:32<19:55,  9.72s/it]
 69%|██████▊   | 266/388 [43:42<20:07,  9.90s/it]
                                                 
{'loss': 0.4403, 'grad_norm': 0.8296083211898804, 'learning_rate': 3.186528497409327e-06, 'epoch': 1.37}

 69%|██████▊   | 266/388 [43:42<20:07,  9.90s/it]
 69%|██████▉   | 267/388 [43:55<21:49, 10.82s/it]
                                                 
{'loss': 0.2941, 'grad_norm': 0.7523472905158997, 'learning_rate': 3.1606217616580314e-06, 'epoch': 1.38}

 69%|██████▉   | 267/388 [43:55<21:49, 10.82s/it]
 69%|██████▉   | 268/388 [44:06<21:33, 10.78s/it]
                                                 
{'loss': 0.4484, 'grad_norm': 0.8845231533050537, 'learning_rate': 3.134715025906736e-06, 'epoch': 1.38}

 69%|██████▉   | 268/388 [44:06<21:33, 10.78s/it]
 69%|██████▉   | 269/388 [44:17<21:25, 10.80s/it]
                                                 
{'loss': 0.2258, 'grad_norm': 0.9409952759742737, 'learning_rate': 3.1088082901554407e-06, 'epoch': 1.39}

 69%|██████▉   | 269/388 [44:17<21:25, 10.80s/it]
 70%|██████▉   | 270/388 [44:29<21:42, 11.04s/it]
                                                 
{'loss': 0.2232, 'grad_norm': 0.7966182231903076, 'learning_rate': 3.0829015544041453e-06, 'epoch': 1.39}

 70%|██████▉   | 270/388 [44:29<21:42, 11.04s/it]
 70%|██████▉   | 271/388 [44:38<20:36, 10.57s/it]
                                                 
{'loss': 0.3161, 'grad_norm': 0.8845571875572205, 'learning_rate': 3.0569948186528495e-06, 'epoch': 1.4}

 70%|██████▉   | 271/388 [44:38<20:36, 10.57s/it]
 70%|███████   | 272/388 [44:46<19:02,  9.85s/it]
                                                 
{'loss': 0.3974, 'grad_norm': 0.8708981275558472, 'learning_rate': 3.031088082901555e-06, 'epoch': 1.4}

 70%|███████   | 272/388 [44:46<19:02,  9.85s/it]
 70%|███████   | 273/388 [44:56<18:43,  9.77s/it]
                                                 
{'loss': 0.2244, 'grad_norm': 0.7636141180992126, 'learning_rate': 3.0051813471502592e-06, 'epoch': 1.41}

 70%|███████   | 273/388 [44:56<18:43,  9.77s/it]
 71%|███████   | 274/388 [45:08<19:45, 10.40s/it]
                                                 
{'loss': 0.2656, 'grad_norm': 0.9768233299255371, 'learning_rate': 2.979274611398964e-06, 'epoch': 1.41}

 71%|███████   | 274/388 [45:08<19:45, 10.40s/it]
 71%|███████   | 275/388 [45:20<20:58, 11.13s/it]
                                                 
{'loss': 0.417, 'grad_norm': 0.6735494136810303, 'learning_rate': 2.9533678756476685e-06, 'epoch': 1.42}

 71%|███████   | 275/388 [45:20<20:58, 11.13s/it]
 71%|███████   | 276/388 [45:31<20:41, 11.08s/it]
                                                 
{'loss': 0.5136, 'grad_norm': 0.6922116875648499, 'learning_rate': 2.927461139896373e-06, 'epoch': 1.42}

 71%|███████   | 276/388 [45:31<20:41, 11.08s/it]
 71%|███████▏  | 277/388 [45:42<19:57, 10.79s/it]
                                                 
{'loss': 0.2413, 'grad_norm': 0.8229126334190369, 'learning_rate': 2.9015544041450778e-06, 'epoch': 1.43}

 71%|███████▏  | 277/388 [45:42<19:57, 10.79s/it]
 72%|███████▏  | 278/388 [45:54<20:34, 11.22s/it]
                                                 
{'loss': 0.5626, 'grad_norm': 0.7794067859649658, 'learning_rate': 2.875647668393783e-06, 'epoch': 1.43}

 72%|███████▏  | 278/388 [45:54<20:34, 11.22s/it]
 72%|███████▏  | 279/388 [46:06<20:46, 11.44s/it]
                                                 
{'loss': 0.4306, 'grad_norm': 0.7981624007225037, 'learning_rate': 2.8497409326424875e-06, 'epoch': 1.44}

 72%|███████▏  | 279/388 [46:06<20:46, 11.44s/it]
 72%|███████▏  | 280/388 [46:18<21:06, 11.73s/it]
                                                 
{'loss': 0.4386, 'grad_norm': 0.6519434452056885, 'learning_rate': 2.823834196891192e-06, 'epoch': 1.44}

 72%|███████▏  | 280/388 [46:18<21:06, 11.73s/it]
 72%|███████▏  | 281/388 [46:29<20:25, 11.46s/it]
                                                 
{'loss': 0.4146, 'grad_norm': 1.0261698961257935, 'learning_rate': 2.7979274611398967e-06, 'epoch': 1.45}

 72%|███████▏  | 281/388 [46:29<20:25, 11.46s/it]
 73%|███████▎  | 282/388 [46:39<19:29, 11.03s/it]
                                                 
{'loss': 0.2195, 'grad_norm': 0.7037999629974365, 'learning_rate': 2.772020725388601e-06, 'epoch': 1.45}

 73%|███████▎  | 282/388 [46:39<19:29, 11.03s/it]
 73%|███████▎  | 283/388 [46:49<18:48, 10.75s/it]
                                                 
{'loss': 0.3176, 'grad_norm': 0.9701761603355408, 'learning_rate': 2.7461139896373056e-06, 'epoch': 1.46}

 73%|███████▎  | 283/388 [46:49<18:48, 10.75s/it]
 73%|███████▎  | 284/388 [46:57<17:13,  9.94s/it]
                                                 
{'loss': 0.1552, 'grad_norm': 1.1157068014144897, 'learning_rate': 2.7202072538860106e-06, 'epoch': 1.46}

 73%|███████▎  | 284/388 [46:57<17:13,  9.94s/it]
 73%|███████▎  | 285/388 [47:10<18:44, 10.92s/it]
                                                 
{'loss': 0.2293, 'grad_norm': 0.8106536865234375, 'learning_rate': 2.6943005181347152e-06, 'epoch': 1.47}

 73%|███████▎  | 285/388 [47:10<18:44, 10.92s/it]
 74%|███████▎  | 286/388 [47:22<18:52, 11.10s/it]
                                                 
{'loss': 0.4799, 'grad_norm': 0.6931859254837036, 'learning_rate': 2.66839378238342e-06, 'epoch': 1.47}

 74%|███████▎  | 286/388 [47:22<18:52, 11.10s/it]
 74%|███████▍  | 287/388 [47:34<19:17, 11.46s/it]
                                                 
{'loss': 0.525, 'grad_norm': 0.9921584725379944, 'learning_rate': 2.6424870466321245e-06, 'epoch': 1.48}

 74%|███████▍  | 287/388 [47:34<19:17, 11.46s/it]
 74%|███████▍  | 288/388 [47:44<18:04, 10.85s/it]
                                                 
{'loss': 0.83, 'grad_norm': 0.9496335983276367, 'learning_rate': 2.616580310880829e-06, 'epoch': 1.48}

 74%|███████▍  | 288/388 [47:44<18:04, 10.85s/it]
 74%|███████▍  | 289/388 [47:53<17:00, 10.31s/it]
                                                 
{'loss': 0.4281, 'grad_norm': 0.8321980834007263, 'learning_rate': 2.5906735751295338e-06, 'epoch': 1.49}

 74%|███████▍  | 289/388 [47:53<17:00, 10.31s/it]
 75%|███████▍  | 290/388 [48:04<17:33, 10.75s/it]
                                                 
{'loss': 0.6716, 'grad_norm': 1.2119672298431396, 'learning_rate': 2.564766839378239e-06, 'epoch': 1.49}

 75%|███████▍  | 290/388 [48:04<17:33, 10.75s/it]
 75%|███████▌  | 291/388 [48:15<17:19, 10.72s/it]
                                                 
{'loss': 0.4473, 'grad_norm': 1.0420453548431396, 'learning_rate': 2.5388601036269435e-06, 'epoch': 1.5}

 75%|███████▌  | 291/388 [48:15<17:19, 10.72s/it]
 75%|███████▌  | 292/388 [48:29<18:28, 11.55s/it]
                                                 
{'loss': 0.3488, 'grad_norm': 0.758085310459137, 'learning_rate': 2.512953367875648e-06, 'epoch': 1.51}

 75%|███████▌  | 292/388 [48:29<18:28, 11.55s/it]
 76%|███████▌  | 293/388 [48:40<18:26, 11.64s/it]
                                                 
{'loss': 0.2243, 'grad_norm': 0.7834224104881287, 'learning_rate': 2.4870466321243523e-06, 'epoch': 1.51}

 76%|███████▌  | 293/388 [48:40<18:26, 11.64s/it]
 76%|███████▌  | 294/388 [48:50<17:04, 10.90s/it]
                                                 
{'loss': 0.2945, 'grad_norm': 0.8290267586708069, 'learning_rate': 2.461139896373057e-06, 'epoch': 1.52}

 76%|███████▌  | 294/388 [48:50<17:04, 10.90s/it]
 76%|███████▌  | 295/388 [48:58<15:35, 10.06s/it]
                                                 
{'loss': 0.6608, 'grad_norm': 0.9336819648742676, 'learning_rate': 2.435233160621762e-06, 'epoch': 1.52}

 76%|███████▌  | 295/388 [48:58<15:35, 10.06s/it]
 76%|███████▋  | 296/388 [49:08<15:29, 10.11s/it]
                                                 
{'loss': 0.3505, 'grad_norm': 0.9153218269348145, 'learning_rate': 2.4093264248704666e-06, 'epoch': 1.53}

 76%|███████▋  | 296/388 [49:08<15:29, 10.11s/it]
 77%|███████▋  | 297/388 [49:17<14:57,  9.87s/it]
                                                 
{'loss': 0.224, 'grad_norm': 0.9206345677375793, 'learning_rate': 2.383419689119171e-06, 'epoch': 1.53}

 77%|███████▋  | 297/388 [49:17<14:57,  9.87s/it]
 77%|███████▋  | 298/388 [49:25<13:59,  9.33s/it]
                                                 
{'loss': 0.4067, 'grad_norm': 1.0660535097122192, 'learning_rate': 2.357512953367876e-06, 'epoch': 1.54}

 77%|███████▋  | 298/388 [49:25<13:59,  9.33s/it]
 77%|███████▋  | 299/388 [49:36<14:33,  9.82s/it]
                                                 
{'loss': 0.2698, 'grad_norm': 0.7061493396759033, 'learning_rate': 2.3316062176165805e-06, 'epoch': 1.54}

 77%|███████▋  | 299/388 [49:36<14:33,  9.82s/it]
 77%|███████▋  | 300/388 [49:46<14:21,  9.79s/it]
                                                 
{'loss': 0.5898, 'grad_norm': 0.8328151106834412, 'learning_rate': 2.305699481865285e-06, 'epoch': 1.55}

 77%|███████▋  | 300/388 [49:46<14:21,  9.79s/it]
 78%|███████▊  | 301/388 [49:55<13:39,  9.41s/it]
                                                 
{'loss': 0.5594, 'grad_norm': 0.9984943866729736, 'learning_rate': 2.27979274611399e-06, 'epoch': 1.55}

 78%|███████▊  | 301/388 [49:55<13:39,  9.41s/it]
 78%|███████▊  | 302/388 [50:04<13:40,  9.55s/it]
                                                 
{'loss': 0.2083, 'grad_norm': 0.8514673709869385, 'learning_rate': 2.2538860103626944e-06, 'epoch': 1.56}

 78%|███████▊  | 302/388 [50:04<13:40,  9.55s/it]
 78%|███████▊  | 303/388 [50:14<13:22,  9.44s/it]
                                                 
{'loss': 0.3777, 'grad_norm': 0.8462634682655334, 'learning_rate': 2.227979274611399e-06, 'epoch': 1.56}

 78%|███████▊  | 303/388 [50:14<13:22,  9.44s/it]
 78%|███████▊  | 304/388 [50:22<12:37,  9.02s/it]
                                                 
{'loss': 0.3127, 'grad_norm': 0.807926595211029, 'learning_rate': 2.2020725388601037e-06, 'epoch': 1.57}

 78%|███████▊  | 304/388 [50:22<12:37,  9.02s/it]
 79%|███████▊  | 305/388 [50:31<12:30,  9.05s/it]
                                                 
{'loss': 0.3368, 'grad_norm': 0.7272363305091858, 'learning_rate': 2.1761658031088083e-06, 'epoch': 1.57}

 79%|███████▊  | 305/388 [50:31<12:30,  9.05s/it]
 79%|███████▉  | 306/388 [50:40<12:37,  9.24s/it]
                                                 
{'loss': 0.4013, 'grad_norm': 1.1403250694274902, 'learning_rate': 2.150259067357513e-06, 'epoch': 1.58}

 79%|███████▉  | 306/388 [50:40<12:37,  9.24s/it]
 79%|███████▉  | 307/388 [50:49<12:05,  8.95s/it]
                                                 
{'loss': 0.4869, 'grad_norm': 1.037915587425232, 'learning_rate': 2.124352331606218e-06, 'epoch': 1.58}

 79%|███████▉  | 307/388 [50:49<12:05,  8.95s/it]
 79%|███████▉  | 308/388 [50:57<11:48,  8.86s/it]
                                                 
{'loss': 0.3257, 'grad_norm': 0.9236746430397034, 'learning_rate': 2.0984455958549222e-06, 'epoch': 1.59}

 79%|███████▉  | 308/388 [50:57<11:48,  8.86s/it]
 80%|███████▉  | 309/388 [51:10<13:03,  9.91s/it]
                                                 
{'loss': 0.4098, 'grad_norm': 0.8756129145622253, 'learning_rate': 2.072538860103627e-06, 'epoch': 1.59}

 80%|███████▉  | 309/388 [51:10<13:03,  9.91s/it]
 80%|███████▉  | 310/388 [51:23<14:04, 10.83s/it]
                                                 
{'loss': 0.4067, 'grad_norm': 0.8163557052612305, 'learning_rate': 2.046632124352332e-06, 'epoch': 1.6}

 80%|███████▉  | 310/388 [51:23<14:04, 10.83s/it]
 80%|████████  | 311/388 [51:32<13:21, 10.41s/it]
                                                 
{'loss': 0.3805, 'grad_norm': 0.8287012577056885, 'learning_rate': 2.0207253886010365e-06, 'epoch': 1.6}

 80%|████████  | 311/388 [51:32<13:21, 10.41s/it]
 80%|████████  | 312/388 [51:41<12:38,  9.98s/it]
                                                 
{'loss': 0.666, 'grad_norm': 0.9473971724510193, 'learning_rate': 1.994818652849741e-06, 'epoch': 1.61}

 80%|████████  | 312/388 [51:41<12:38,  9.98s/it]
 81%|████████  | 313/388 [51:52<12:55, 10.34s/it]
                                                 
{'loss': 0.2932, 'grad_norm': 0.9730647206306458, 'learning_rate': 1.968911917098446e-06, 'epoch': 1.61}

 81%|████████  | 313/388 [51:52<12:55, 10.34s/it]
 81%|████████  | 314/388 [52:03<12:58, 10.52s/it]
                                                 
{'loss': 0.6934, 'grad_norm': 1.2639105319976807, 'learning_rate': 1.9430051813471504e-06, 'epoch': 1.62}

 81%|████████  | 314/388 [52:03<12:58, 10.52s/it]
 81%|████████  | 315/388 [52:12<12:19, 10.13s/it]
                                                 
{'loss': 0.541, 'grad_norm': 0.7502963542938232, 'learning_rate': 1.917098445595855e-06, 'epoch': 1.62}

 81%|████████  | 315/388 [52:12<12:19, 10.13s/it]
 81%|████████▏ | 316/388 [52:21<11:42,  9.75s/it]
                                                 
{'loss': 0.2526, 'grad_norm': 0.8507408499717712, 'learning_rate': 1.8911917098445597e-06, 'epoch': 1.63}

 81%|████████▏ | 316/388 [52:21<11:42,  9.75s/it]
 82%|████████▏ | 317/388 [52:32<11:53, 10.05s/it]
                                                 
{'loss': 0.6209, 'grad_norm': 0.8207960724830627, 'learning_rate': 1.8652849740932643e-06, 'epoch': 1.63}

 82%|████████▏ | 317/388 [52:32<11:53, 10.05s/it][2025-07-07 20:50:19,830] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time

 82%|████████▏ | 318/388 [52:46<13:14, 11.36s/it]
                                                 
{'loss': 0.4042, 'grad_norm': 0.7555132508277893, 'learning_rate': 1.839378238341969e-06, 'epoch': 1.64}

 82%|████████▏ | 318/388 [52:46<13:14, 11.36s/it]
 82%|████████▏ | 319/388 [52:57<12:50, 11.16s/it]
                                                 
{'loss': 0.2184, 'grad_norm': 0.7625459432601929, 'learning_rate': 1.8134715025906738e-06, 'epoch': 1.64}

 82%|████████▏ | 319/388 [52:57<12:50, 11.16s/it]
 82%|████████▏ | 320/388 [53:10<13:15, 11.69s/it]
                                                 
{'loss': 0.491, 'grad_norm': 0.6898732781410217, 'learning_rate': 1.7875647668393784e-06, 'epoch': 1.65}

 82%|████████▏ | 320/388 [53:10<13:15, 11.69s/it]
 83%|████████▎ | 321/388 [53:18<11:49, 10.59s/it]
                                                 
{'loss': 0.1889, 'grad_norm': 0.8527777791023254, 'learning_rate': 1.7616580310880829e-06, 'epoch': 1.65}

 83%|████████▎ | 321/388 [53:18<11:49, 10.59s/it]
 83%|████████▎ | 322/388 [53:29<11:38, 10.58s/it]
                                                 
{'loss': 0.3732, 'grad_norm': 0.9009307026863098, 'learning_rate': 1.7357512953367877e-06, 'epoch': 1.66}

 83%|████████▎ | 322/388 [53:29<11:38, 10.58s/it]
 83%|████████▎ | 323/388 [53:41<12:07, 11.20s/it]
                                                 
{'loss': 0.6212, 'grad_norm': 0.7492113709449768, 'learning_rate': 1.7098445595854923e-06, 'epoch': 1.66}

 83%|████████▎ | 323/388 [53:41<12:07, 11.20s/it]
 84%|████████▎ | 324/388 [53:52<11:41, 10.96s/it]
                                                 
{'loss': 0.2619, 'grad_norm': 1.1161123514175415, 'learning_rate': 1.683937823834197e-06, 'epoch': 1.67}

 84%|████████▎ | 324/388 [53:52<11:41, 10.96s/it]
 84%|████████▍ | 325/388 [54:01<10:53, 10.38s/it]
                                                 
{'loss': 0.325, 'grad_norm': 1.0563406944274902, 'learning_rate': 1.6580310880829018e-06, 'epoch': 1.68}

 84%|████████▍ | 325/388 [54:01<10:53, 10.38s/it]
 84%|████████▍ | 326/388 [54:10<10:14,  9.91s/it]
                                                 
{'loss': 0.3224, 'grad_norm': 0.8605097532272339, 'learning_rate': 1.6321243523316062e-06, 'epoch': 1.68}

 84%|████████▍ | 326/388 [54:10<10:14,  9.91s/it]
 84%|████████▍ | 327/388 [54:19<10:01,  9.87s/it]
                                                 
{'loss': 0.3217, 'grad_norm': 0.7220029830932617, 'learning_rate': 1.6062176165803109e-06, 'epoch': 1.69}

 84%|████████▍ | 327/388 [54:19<10:01,  9.87s/it]
 85%|████████▍ | 328/388 [54:28<09:24,  9.40s/it]
                                                 
{'loss': 0.5041, 'grad_norm': 0.9134677648544312, 'learning_rate': 1.5803108808290157e-06, 'epoch': 1.69}

 85%|████████▍ | 328/388 [54:28<09:24,  9.40s/it]
 85%|████████▍ | 329/388 [54:39<09:52, 10.04s/it]
                                                 
{'loss': 0.2374, 'grad_norm': 1.0126835107803345, 'learning_rate': 1.5544041450777204e-06, 'epoch': 1.7}

 85%|████████▍ | 329/388 [54:39<09:52, 10.04s/it]
 85%|████████▌ | 330/388 [54:48<09:24,  9.73s/it]
                                                 
{'loss': 0.3007, 'grad_norm': 0.7978687286376953, 'learning_rate': 1.5284974093264248e-06, 'epoch': 1.7}

 85%|████████▌ | 330/388 [54:48<09:24,  9.73s/it]
 85%|████████▌ | 331/388 [54:57<09:07,  9.61s/it]
                                                 
{'loss': 0.5242, 'grad_norm': 0.7326058745384216, 'learning_rate': 1.5025906735751296e-06, 'epoch': 1.71}

 85%|████████▌ | 331/388 [54:57<09:07,  9.61s/it]
 86%|████████▌ | 332/388 [55:08<09:13,  9.89s/it]
                                                 
{'loss': 0.1781, 'grad_norm': 0.8722479343414307, 'learning_rate': 1.4766839378238342e-06, 'epoch': 1.71}

 86%|████████▌ | 332/388 [55:08<09:13,  9.89s/it]
 86%|████████▌ | 333/388 [55:18<09:09,  9.99s/it]
                                                 
{'loss': 0.3536, 'grad_norm': 0.870640218257904, 'learning_rate': 1.4507772020725389e-06, 'epoch': 1.72}

 86%|████████▌ | 333/388 [55:18<09:09,  9.99s/it]
 86%|████████▌ | 334/388 [55:28<08:52,  9.86s/it]
                                                 
{'loss': 0.3361, 'grad_norm': 0.9238669872283936, 'learning_rate': 1.4248704663212437e-06, 'epoch': 1.72}

 86%|████████▌ | 334/388 [55:28<08:52,  9.86s/it]
 86%|████████▋ | 335/388 [55:40<09:13, 10.44s/it]
                                                 
{'loss': 0.3833, 'grad_norm': 0.8625023365020752, 'learning_rate': 1.3989637305699484e-06, 'epoch': 1.73}

 86%|████████▋ | 335/388 [55:40<09:13, 10.44s/it]
 87%|████████▋ | 336/388 [55:51<09:10, 10.58s/it]
                                                 
{'loss': 0.3797, 'grad_norm': 0.8655543923377991, 'learning_rate': 1.3730569948186528e-06, 'epoch': 1.73}

 87%|████████▋ | 336/388 [55:51<09:10, 10.58s/it]
 87%|████████▋ | 337/388 [56:04<09:38, 11.34s/it]
                                                 
{'loss': 0.2133, 'grad_norm': 0.7673391699790955, 'learning_rate': 1.3471502590673576e-06, 'epoch': 1.74}

 87%|████████▋ | 337/388 [56:04<09:38, 11.34s/it]
 87%|████████▋ | 338/388 [56:16<09:42, 11.66s/it]
                                                 
{'loss': 0.3146, 'grad_norm': 0.8131647706031799, 'learning_rate': 1.3212435233160623e-06, 'epoch': 1.74}

 87%|████████▋ | 338/388 [56:16<09:42, 11.66s/it]
 87%|████████▋ | 339/388 [56:24<08:31, 10.45s/it]
                                                 
{'loss': 0.2396, 'grad_norm': 0.9960036277770996, 'learning_rate': 1.2953367875647669e-06, 'epoch': 1.75}

 87%|████████▋ | 339/388 [56:24<08:31, 10.45s/it]
 88%|████████▊ | 340/388 [56:37<09:01, 11.28s/it]
                                                 
{'loss': 0.4431, 'grad_norm': 0.7692868113517761, 'learning_rate': 1.2694300518134717e-06, 'epoch': 1.75}

 88%|████████▊ | 340/388 [56:37<09:01, 11.28s/it]
 88%|████████▊ | 341/388 [56:47<08:38, 11.03s/it]
                                                 
{'loss': 0.3941, 'grad_norm': 1.1909233331680298, 'learning_rate': 1.2435233160621762e-06, 'epoch': 1.76}

 88%|████████▊ | 341/388 [56:47<08:38, 11.03s/it]
 88%|████████▊ | 342/388 [56:55<07:48, 10.17s/it]
                                                 
{'loss': 0.3755, 'grad_norm': 1.0892987251281738, 'learning_rate': 1.217616580310881e-06, 'epoch': 1.76}

 88%|████████▊ | 342/388 [56:55<07:48, 10.17s/it]
 88%|████████▊ | 343/388 [57:06<07:45, 10.35s/it]
                                                 
{'loss': 0.5534, 'grad_norm': 0.8232518434524536, 'learning_rate': 1.1917098445595854e-06, 'epoch': 1.77}

 88%|████████▊ | 343/388 [57:06<07:45, 10.35s/it]
 89%|████████▊ | 344/388 [57:18<07:56, 10.82s/it]
                                                 
{'loss': 0.2867, 'grad_norm': 1.032169222831726, 'learning_rate': 1.1658031088082903e-06, 'epoch': 1.77}

 89%|████████▊ | 344/388 [57:18<07:56, 10.82s/it]
 89%|████████▉ | 345/388 [57:27<07:16, 10.14s/it]
                                                 
{'loss': 0.2733, 'grad_norm': 0.842463493347168, 'learning_rate': 1.139896373056995e-06, 'epoch': 1.78}

 89%|████████▉ | 345/388 [57:27<07:16, 10.14s/it]
 89%|████████▉ | 346/388 [57:37<07:09, 10.22s/it]
                                                 
{'loss': 0.7245, 'grad_norm': 0.9842871427536011, 'learning_rate': 1.1139896373056995e-06, 'epoch': 1.78}

 89%|████████▉ | 346/388 [57:37<07:09, 10.22s/it]
 89%|████████▉ | 347/388 [57:47<06:52, 10.07s/it]
                                                 
{'loss': 0.5642, 'grad_norm': 0.9444430470466614, 'learning_rate': 1.0880829015544042e-06, 'epoch': 1.79}

 89%|████████▉ | 347/388 [57:47<06:52, 10.07s/it]
 90%|████████▉ | 348/388 [57:55<06:19,  9.48s/it]
                                                 
{'loss': 0.2328, 'grad_norm': 0.9961406588554382, 'learning_rate': 1.062176165803109e-06, 'epoch': 1.79}

 90%|████████▉ | 348/388 [57:55<06:19,  9.48s/it]
 90%|████████▉ | 349/388 [58:07<06:34, 10.11s/it]
                                                 
{'loss': 0.5595, 'grad_norm': 0.7799118161201477, 'learning_rate': 1.0362694300518134e-06, 'epoch': 1.8}

 90%|████████▉ | 349/388 [58:07<06:34, 10.11s/it]
 90%|█████████ | 350/388 [58:14<05:56,  9.39s/it]
                                                 
{'loss': 0.2481, 'grad_norm': 2.573274850845337, 'learning_rate': 1.0103626943005183e-06, 'epoch': 1.8}

 90%|█████████ | 350/388 [58:14<05:56,  9.39s/it]
 90%|█████████ | 351/388 [58:25<06:07,  9.94s/it]
                                                 
{'loss': 0.2936, 'grad_norm': 0.7279102802276611, 'learning_rate': 9.84455958549223e-07, 'epoch': 1.81}

 90%|█████████ | 351/388 [58:25<06:07,  9.94s/it]
 91%|█████████ | 352/388 [58:35<05:51,  9.77s/it]
                                                 
{'loss': 0.5409, 'grad_norm': 0.9081263542175293, 'learning_rate': 9.585492227979275e-07, 'epoch': 1.81}

 91%|█████████ | 352/388 [58:35<05:51,  9.77s/it]
 91%|█████████ | 353/388 [58:46<05:56, 10.20s/it]
                                                 
{'loss': 0.4123, 'grad_norm': 0.7428257465362549, 'learning_rate': 9.326424870466322e-07, 'epoch': 1.82}

 91%|█████████ | 353/388 [58:46<05:56, 10.20s/it]
 91%|█████████ | 354/388 [58:57<05:56, 10.50s/it]
                                                 
{'loss': 0.2173, 'grad_norm': 0.7378343939781189, 'learning_rate': 9.067357512953369e-07, 'epoch': 1.82}

 91%|█████████ | 354/388 [58:57<05:56, 10.50s/it]
 91%|█████████▏| 355/388 [59:08<05:50, 10.62s/it]
                                                 
{'loss': 0.2758, 'grad_norm': 0.7557251453399658, 'learning_rate': 8.808290155440414e-07, 'epoch': 1.83}

 91%|█████████▏| 355/388 [59:08<05:50, 10.62s/it]
 92%|█████████▏| 356/388 [59:18<05:35, 10.49s/it]
                                                 
{'loss': 0.3972, 'grad_norm': 0.6878364086151123, 'learning_rate': 8.549222797927462e-07, 'epoch': 1.84}

 92%|█████████▏| 356/388 [59:18<05:35, 10.49s/it]
 92%|█████████▏| 357/388 [59:31<05:47, 11.21s/it]
                                                 
{'loss': 0.2774, 'grad_norm': 0.7395130395889282, 'learning_rate': 8.290155440414509e-07, 'epoch': 1.84}

 92%|█████████▏| 357/388 [59:31<05:47, 11.21s/it]
 92%|█████████▏| 358/388 [59:39<05:06, 10.20s/it]
                                                 
{'loss': 0.4064, 'grad_norm': 0.8822534084320068, 'learning_rate': 8.031088082901554e-07, 'epoch': 1.85}

 92%|█████████▏| 358/388 [59:39<05:06, 10.20s/it]
 93%|█████████▎| 359/388 [59:52<05:20, 11.06s/it]
                                                 
{'loss': 0.2104, 'grad_norm': 0.7297852039337158, 'learning_rate': 7.772020725388602e-07, 'epoch': 1.85}

 93%|█████████▎| 359/388 [59:52<05:20, 11.06s/it]
 93%|█████████▎| 360/388 [1:00:01<04:49, 10.33s/it]
                                                   
{'loss': 0.5216, 'grad_norm': 0.8908668160438538, 'learning_rate': 7.512953367875648e-07, 'epoch': 1.86}

 93%|█████████▎| 360/388 [1:00:01<04:49, 10.33s/it]
 93%|█████████▎| 361/388 [1:00:10<04:34, 10.15s/it]
                                                   
{'loss': 0.4021, 'grad_norm': 0.8597674369812012, 'learning_rate': 7.253886010362694e-07, 'epoch': 1.86}

 93%|█████████▎| 361/388 [1:00:10<04:34, 10.15s/it]
 93%|█████████▎| 362/388 [1:00:23<04:42, 10.87s/it]
                                                   
{'loss': 0.2504, 'grad_norm': 0.7925522327423096, 'learning_rate': 6.994818652849742e-07, 'epoch': 1.87}

 93%|█████████▎| 362/388 [1:00:23<04:42, 10.87s/it]
 94%|█████████▎| 363/388 [1:00:34<04:29, 10.80s/it]
                                                   
{'loss': 0.5021, 'grad_norm': 0.9896957278251648, 'learning_rate': 6.735751295336788e-07, 'epoch': 1.87}

 94%|█████████▎| 363/388 [1:00:34<04:29, 10.80s/it]
 94%|█████████▍| 364/388 [1:00:45<04:20, 10.83s/it]
                                                   
{'loss': 0.2783, 'grad_norm': 0.6748519539833069, 'learning_rate': 6.476683937823834e-07, 'epoch': 1.88}

 94%|█████████▍| 364/388 [1:00:45<04:20, 10.83s/it]
 94%|█████████▍| 365/388 [1:00:56<04:10, 10.88s/it]
                                                   
{'loss': 0.4051, 'grad_norm': 0.8480097055435181, 'learning_rate': 6.217616580310881e-07, 'epoch': 1.88}

 94%|█████████▍| 365/388 [1:00:56<04:10, 10.88s/it]
 94%|█████████▍| 366/388 [1:01:08<04:06, 11.20s/it]
                                                   
{'loss': 0.3374, 'grad_norm': 0.8139077425003052, 'learning_rate': 5.958549222797927e-07, 'epoch': 1.89}

 94%|█████████▍| 366/388 [1:01:08<04:06, 11.20s/it]
 95%|█████████▍| 367/388 [1:01:18<03:49, 10.91s/it]
                                                   
{'loss': 0.6448, 'grad_norm': 0.7533369660377502, 'learning_rate': 5.699481865284974e-07, 'epoch': 1.89}

 95%|█████████▍| 367/388 [1:01:18<03:49, 10.91s/it]
 95%|█████████▍| 368/388 [1:01:28<03:36, 10.82s/it]
                                                   
{'loss': 0.4828, 'grad_norm': 0.8040704727172852, 'learning_rate': 5.440414507772021e-07, 'epoch': 1.9}

 95%|█████████▍| 368/388 [1:01:28<03:36, 10.82s/it]
 95%|█████████▌| 369/388 [1:01:38<03:20, 10.56s/it]
                                                   
{'loss': 0.3311, 'grad_norm': 1.2289057970046997, 'learning_rate': 5.181347150259067e-07, 'epoch': 1.9}

 95%|█████████▌| 369/388 [1:01:38<03:20, 10.56s/it]
 95%|█████████▌| 370/388 [1:01:48<03:06, 10.35s/it]
                                                   
{'loss': 0.4625, 'grad_norm': 0.7637282609939575, 'learning_rate': 4.922279792746115e-07, 'epoch': 1.91}

 95%|█████████▌| 370/388 [1:01:48<03:06, 10.35s/it]
 96%|█████████▌| 371/388 [1:01:59<02:57, 10.46s/it]
                                                   
{'loss': 0.3358, 'grad_norm': 0.7972410321235657, 'learning_rate': 4.663212435233161e-07, 'epoch': 1.91}

 96%|█████████▌| 371/388 [1:01:59<02:57, 10.46s/it]
 96%|█████████▌| 372/388 [1:02:12<03:01, 11.35s/it]
                                                   
{'loss': 0.4232, 'grad_norm': 0.6649189591407776, 'learning_rate': 4.404145077720207e-07, 'epoch': 1.92}

 96%|█████████▌| 372/388 [1:02:12<03:01, 11.35s/it]
 96%|█████████▌| 373/388 [1:02:20<02:34, 10.27s/it]
                                                   
{'loss': 0.1765, 'grad_norm': 0.8117662668228149, 'learning_rate': 4.1450777202072546e-07, 'epoch': 1.92}

 96%|█████████▌| 373/388 [1:02:20<02:34, 10.27s/it]
 96%|█████████▋| 374/388 [1:02:30<02:20, 10.04s/it]
                                                   
{'loss': 0.3167, 'grad_norm': 0.7998349070549011, 'learning_rate': 3.886010362694301e-07, 'epoch': 1.93}

 96%|█████████▋| 374/388 [1:02:30<02:20, 10.04s/it]
 97%|█████████▋| 375/388 [1:02:38<02:02,  9.45s/it]
                                                   
{'loss': 0.3374, 'grad_norm': 0.8259660005569458, 'learning_rate': 3.626943005181347e-07, 'epoch': 1.93}

 97%|█████████▋| 375/388 [1:02:38<02:02,  9.45s/it]
 97%|█████████▋| 376/388 [1:02:49<02:00, 10.04s/it]
                                                   
{'loss': 0.6101, 'grad_norm': 1.8678282499313354, 'learning_rate': 3.367875647668394e-07, 'epoch': 1.94}

 97%|█████████▋| 376/388 [1:02:49<02:00, 10.04s/it]
 97%|█████████▋| 377/388 [1:02:59<01:49,  9.99s/it]
                                                   
{'loss': 0.3394, 'grad_norm': 1.0159757137298584, 'learning_rate': 3.1088082901554404e-07, 'epoch': 1.94}

 97%|█████████▋| 377/388 [1:02:59<01:49,  9.99s/it]
 97%|█████████▋| 378/388 [1:03:09<01:41, 10.13s/it]
                                                   
{'loss': 0.2926, 'grad_norm': 0.7176821827888489, 'learning_rate': 2.849740932642487e-07, 'epoch': 1.95}

 97%|█████████▋| 378/388 [1:03:09<01:41, 10.13s/it]
 98%|█████████▊| 379/388 [1:03:20<01:32, 10.24s/it]
                                                   
{'loss': 0.2626, 'grad_norm': 0.9229880571365356, 'learning_rate': 2.5906735751295336e-07, 'epoch': 1.95}

 98%|█████████▊| 379/388 [1:03:20<01:32, 10.24s/it]
 98%|█████████▊| 380/388 [1:03:33<01:28, 11.06s/it]
                                                   
{'loss': 0.5018, 'grad_norm': 0.8421505689620972, 'learning_rate': 2.3316062176165804e-07, 'epoch': 1.96}

 98%|█████████▊| 380/388 [1:03:33<01:28, 11.06s/it]
 98%|█████████▊| 381/388 [1:03:42<01:13, 10.48s/it]
                                                   
{'loss': 0.7074, 'grad_norm': 0.8830316662788391, 'learning_rate': 2.0725388601036273e-07, 'epoch': 1.96}

 98%|█████████▊| 381/388 [1:03:42<01:13, 10.48s/it]
 98%|█████████▊| 382/388 [1:03:53<01:04, 10.69s/it]
                                                   
{'loss': 0.4983, 'grad_norm': 0.7526780366897583, 'learning_rate': 1.8134715025906736e-07, 'epoch': 1.97}

 98%|█████████▊| 382/388 [1:03:53<01:04, 10.69s/it]
 99%|█████████▊| 383/388 [1:04:04<00:54, 10.84s/it]
                                                   
{'loss': 0.4592, 'grad_norm': 1.0883328914642334, 'learning_rate': 1.5544041450777202e-07, 'epoch': 1.97}

 99%|█████████▊| 383/388 [1:04:04<00:54, 10.84s/it]
 99%|█████████▉| 384/388 [1:04:13<00:40, 10.23s/it]
                                                   
{'loss': 0.1414, 'grad_norm': 1.4465148448944092, 'learning_rate': 1.2953367875647668e-07, 'epoch': 1.98}

 99%|█████████▉| 384/388 [1:04:13<00:40, 10.23s/it]
 99%|█████████▉| 385/388 [1:04:22<00:29,  9.76s/it]
                                                   
{'loss': 0.6172, 'grad_norm': 0.8788369297981262, 'learning_rate': 1.0362694300518136e-07, 'epoch': 1.98}

 99%|█████████▉| 385/388 [1:04:22<00:29,  9.76s/it]
 99%|█████████▉| 386/388 [1:04:32<00:19,  9.78s/it]
                                                   
{'loss': 0.6235, 'grad_norm': 0.8910044431686401, 'learning_rate': 7.772020725388601e-08, 'epoch': 1.99}

 99%|█████████▉| 386/388 [1:04:32<00:19,  9.78s/it]
100%|█████████▉| 387/388 [1:04:44<00:10, 10.58s/it]
                                                   
{'loss': 0.616, 'grad_norm': 0.8766142129898071, 'learning_rate': 5.181347150259068e-08, 'epoch': 1.99}

100%|█████████▉| 387/388 [1:04:44<00:10, 10.58s/it]
100%|██████████| 388/388 [1:04:56<00:00, 10.90s/it]
                                                   
{'loss': 0.267, 'grad_norm': 0.8077130317687988, 'learning_rate': 2.590673575129534e-08, 'epoch': 2.0}

100%|██████████| 388/388 [1:04:56<00:00, 10.90s/it]
                                                   
{'train_runtime': 4068.7106, 'train_samples_per_second': 0.38, 'train_steps_per_second': 0.095, 'train_loss': 0.44620604797736885, 'epoch': 2.0}

100%|██████████| 388/388 [1:07:47<00:00, 10.90s/it]
100%|██████████| 388/388 [1:07:47<00:00, 10.48s/it]
[2025-07-07 21:05:41,515] [INFO] [launch.py:351:main] Process 2630892 exits successfully.
[2025-07-07 21:05:44,517] [INFO] [launch.py:351:main] Process 2630893 exits successfully.
[2025-07-07 21:05:47,521] [INFO] [launch.py:351:main] Process 2630891 exits successfully.
[1;34mwandb[0m: 
[1;34mwandb[0m: 🚀 View run [33mtrainer_output[0m at: [34mhttps://wandb.ai/junkim/kullm-pro/runs/429qzo86[0m
[1;34mwandb[0m: Find logs at: [1;35mwandb/run-20250707_195731-429qzo86/logs[0m
[2025-07-07 21:08:30,693] [INFO] [launch.py:351:main] Process 2630890 exits successfully.
