  0%|          | 0/388 [00:00<?, ?it/s]`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
                                                  
{'loss': 1.0613, 'grad_norm': 5.972428798675537, 'learning_rate': 0.0, 'epoch': 0.01}
{'loss': 0.7394, 'grad_norm': 3.9083375930786133, 'learning_rate': 1e-05, 'epoch': 0.01}
{'loss': 1.039, 'grad_norm': 5.037124156951904, 'learning_rate': 1e-05, 'epoch': 0.02}
{'loss': 0.8418, 'grad_norm': 2.822321653366089, 'learning_rate': 9.974093264248705e-06, 'epoch': 0.02}
{'loss': 0.7798, 'grad_norm': 5.261780738830566, 'learning_rate': 9.94818652849741e-06, 'epoch': 0.03}
{'loss': 0.7993, 'grad_norm': 3.8250977993011475, 'learning_rate': 9.922279792746115e-06, 'epoch': 0.03}
{'loss': 0.9744, 'grad_norm': 4.040095329284668, 'learning_rate': 9.89637305699482e-06, 'epoch': 0.04}
{'loss': 1.1334, 'grad_norm': 3.3422718048095703, 'learning_rate': 9.870466321243524e-06, 'epoch': 0.04}
{'loss': 1.0422, 'grad_norm': 3.6680266857147217, 'learning_rate': 9.844559585492228e-06, 'epoch': 0.05}
{'loss': 0.7601, 'grad_norm': 4.0741095542907715, 'learning_rate': 9.818652849740934e-06, 'epoch': 0.05}
{'loss': 0.8571, 'grad_norm': 3.0370264053344727, 'learning_rate': 9.792746113989638e-06, 'epoch': 0.06}
{'loss': 0.8824, 'grad_norm': 2.6689648628234863, 'learning_rate': 9.766839378238344e-06, 'epoch': 0.06}
{'loss': 0.8879, 'grad_norm': 2.9273457527160645, 'learning_rate': 9.740932642487048e-06, 'epoch': 0.07}
{'loss': 0.8117, 'grad_norm': 2.7669601440429688, 'learning_rate': 9.715025906735752e-06, 'epoch': 0.07}
{'loss': 0.7426, 'grad_norm': 2.4161019325256348, 'learning_rate': 9.689119170984456e-06, 'epoch': 0.08}
{'loss': 0.8518, 'grad_norm': 2.307830572128296, 'learning_rate': 9.66321243523316e-06, 'epoch': 0.08}
{'loss': 0.9133, 'grad_norm': 2.167433261871338, 'learning_rate': 9.637305699481867e-06, 'epoch': 0.09}
{'loss': 0.6976, 'grad_norm': 2.4613585472106934, 'learning_rate': 9.61139896373057e-06, 'epoch': 0.09}
{'loss': 0.824, 'grad_norm': 2.515002965927124, 'learning_rate': 9.585492227979275e-06, 'epoch': 0.1}
{'loss': 0.9698, 'grad_norm': 2.5747175216674805, 'learning_rate': 9.559585492227979e-06, 'epoch': 0.1}
{'loss': 0.7327, 'grad_norm': 3.076650619506836, 'learning_rate': 9.533678756476683e-06, 'epoch': 0.11}
{'loss': 0.8011, 'grad_norm': 3.3015217781066895, 'learning_rate': 9.50777202072539e-06, 'epoch': 0.11}
{'loss': 0.6903, 'grad_norm': 2.6446707248687744, 'learning_rate': 9.481865284974095e-06, 'epoch': 0.12}
{'loss': 0.7495, 'grad_norm': 2.679511308670044, 'learning_rate': 9.4559585492228e-06, 'epoch': 0.12}
{'loss': 0.6137, 'grad_norm': 2.0696640014648438, 'learning_rate': 9.430051813471504e-06, 'epoch': 0.13}
{'loss': 0.6033, 'grad_norm': 2.506619930267334, 'learning_rate': 9.404145077720208e-06, 'epoch': 0.13}
{'loss': 0.9127, 'grad_norm': 2.3916053771972656, 'learning_rate': 9.378238341968912e-06, 'epoch': 0.14}
{'loss': 0.6533, 'grad_norm': 2.373687505722046, 'learning_rate': 9.352331606217618e-06, 'epoch': 0.14}
{'loss': 1.137, 'grad_norm': 2.58476185798645, 'learning_rate': 9.326424870466322e-06, 'epoch': 0.15}
{'loss': 1.004, 'grad_norm': 2.3435304164886475, 'learning_rate': 9.300518134715026e-06, 'epoch': 0.15}
{'loss': 0.6218, 'grad_norm': 2.7886364459991455, 'learning_rate': 9.27461139896373e-06, 'epoch': 0.16}
{'loss': 0.7332, 'grad_norm': 2.8591411113739014, 'learning_rate': 9.248704663212435e-06, 'epoch': 0.16}
{'loss': 0.7658, 'grad_norm': 2.5078628063201904, 'learning_rate': 9.22279792746114e-06, 'epoch': 0.17}
{'loss': 0.9093, 'grad_norm': 2.322923421859741, 'learning_rate': 9.196891191709847e-06, 'epoch': 0.18}
{'loss': 0.9376, 'grad_norm': 2.553248405456543, 'learning_rate': 9.17098445595855e-06, 'epoch': 0.18}
{'loss': 0.6415, 'grad_norm': 2.6050047874450684, 'learning_rate': 9.145077720207255e-06, 'epoch': 0.19}
{'loss': 0.7948, 'grad_norm': 2.4142885208129883, 'learning_rate': 9.11917098445596e-06, 'epoch': 0.19}
{'loss': 0.6776, 'grad_norm': 2.33914852142334, 'learning_rate': 9.093264248704663e-06, 'epoch': 0.2}
{'loss': 1.2934, 'grad_norm': 2.9091343879699707, 'learning_rate': 9.06735751295337e-06, 'epoch': 0.2}
{'loss': 0.7059, 'grad_norm': 2.4093809127807617, 'learning_rate': 9.041450777202073e-06, 'epoch': 0.21}
{'loss': 0.8125, 'grad_norm': 2.805210590362549, 'learning_rate': 9.015544041450778e-06, 'epoch': 0.21}
{'loss': 0.7355, 'grad_norm': 2.395639419555664, 'learning_rate': 8.989637305699482e-06, 'epoch': 0.22}
{'loss': 0.7558, 'grad_norm': 2.558945894241333, 'learning_rate': 8.963730569948186e-06, 'epoch': 0.22}
{'loss': 0.5813, 'grad_norm': 2.971144437789917, 'learning_rate': 8.937823834196892e-06, 'epoch': 0.23}
{'loss': 0.7257, 'grad_norm': 2.6035735607147217, 'learning_rate': 8.911917098445596e-06, 'epoch': 0.23}
[2025-07-08 19:07:39,094] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 0.6401, 'grad_norm': 3.035959243774414, 'learning_rate': 8.886010362694302e-06, 'epoch': 0.24}
{'loss': 0.6298, 'grad_norm': 2.4860098361968994, 'learning_rate': 8.860103626943006e-06, 'epoch': 0.24}
{'loss': 0.7476, 'grad_norm': 2.293618679046631, 'learning_rate': 8.83419689119171e-06, 'epoch': 0.25}
{'loss': 0.848, 'grad_norm': 2.499324083328247, 'learning_rate': 8.808290155440415e-06, 'epoch': 0.25}
{'loss': 0.7049, 'grad_norm': 2.6589033603668213, 'learning_rate': 8.78238341968912e-06, 'epoch': 0.26}
{'loss': 0.8705, 'grad_norm': 2.1938395500183105, 'learning_rate': 8.756476683937825e-06, 'epoch': 0.26}
{'loss': 0.9104, 'grad_norm': 2.4931790828704834, 'learning_rate': 8.730569948186529e-06, 'epoch': 0.27}
{'loss': 0.833, 'grad_norm': 2.2161130905151367, 'learning_rate': 8.704663212435233e-06, 'epoch': 0.27}
{'loss': 0.7508, 'grad_norm': 2.7588376998901367, 'learning_rate': 8.678756476683938e-06, 'epoch': 0.28}
{'loss': 0.8628, 'grad_norm': 2.7748560905456543, 'learning_rate': 8.652849740932643e-06, 'epoch': 0.28}
{'loss': 0.7079, 'grad_norm': 2.1448283195495605, 'learning_rate': 8.626943005181348e-06, 'epoch': 0.29}
{'loss': 1.2438, 'grad_norm': 2.4516265392303467, 'learning_rate': 8.601036269430052e-06, 'epoch': 0.29}
{'loss': 0.5436, 'grad_norm': 2.87629771232605, 'learning_rate': 8.575129533678758e-06, 'epoch': 0.3}
{'loss': 0.5823, 'grad_norm': 2.6505439281463623, 'learning_rate': 8.549222797927462e-06, 'epoch': 0.3}
{'loss': 1.2081, 'grad_norm': 2.7029812335968018, 'learning_rate': 8.523316062176166e-06, 'epoch': 0.31}
{'loss': 0.5786, 'grad_norm': 2.386359214782715, 'learning_rate': 8.497409326424872e-06, 'epoch': 0.31}
{'loss': 0.8214, 'grad_norm': 2.2240004539489746, 'learning_rate': 8.471502590673576e-06, 'epoch': 0.32}
{'loss': 0.8158, 'grad_norm': 2.523404598236084, 'learning_rate': 8.44559585492228e-06, 'epoch': 0.32}
{'loss': 0.7062, 'grad_norm': 2.5156991481781006, 'learning_rate': 8.419689119170985e-06, 'epoch': 0.33}
{'loss': 0.6367, 'grad_norm': 2.2868878841400146, 'learning_rate': 8.393782383419689e-06, 'epoch': 0.34}
{'loss': 0.9194, 'grad_norm': 3.0570547580718994, 'learning_rate': 8.367875647668395e-06, 'epoch': 0.34}
{'loss': 0.6803, 'grad_norm': 2.5580966472625732, 'learning_rate': 8.341968911917099e-06, 'epoch': 0.35}
{'loss': 0.6019, 'grad_norm': 2.4723732471466064, 'learning_rate': 8.316062176165803e-06, 'epoch': 0.35}
{'loss': 0.9282, 'grad_norm': 2.831188440322876, 'learning_rate': 8.290155440414507e-06, 'epoch': 0.36}
{'loss': 0.8331, 'grad_norm': 2.535067081451416, 'learning_rate': 8.264248704663213e-06, 'epoch': 0.36}
{'loss': 0.7635, 'grad_norm': 2.6289937496185303, 'learning_rate': 8.238341968911918e-06, 'epoch': 0.37}
{'loss': 0.6756, 'grad_norm': 2.524071455001831, 'learning_rate': 8.212435233160623e-06, 'epoch': 0.37}
{'loss': 0.8577, 'grad_norm': 2.2408902645111084, 'learning_rate': 8.186528497409328e-06, 'epoch': 0.38}
{'loss': 0.709, 'grad_norm': 1.9304513931274414, 'learning_rate': 8.160621761658032e-06, 'epoch': 0.38}
{'loss': 0.9485, 'grad_norm': 2.5184836387634277, 'learning_rate': 8.134715025906736e-06, 'epoch': 0.39}
{'loss': 0.7096, 'grad_norm': 2.1354572772979736, 'learning_rate': 8.10880829015544e-06, 'epoch': 0.39}
{'loss': 0.6038, 'grad_norm': 1.9624686241149902, 'learning_rate': 8.082901554404146e-06, 'epoch': 0.4}
{'loss': 0.7366, 'grad_norm': 2.488140344619751, 'learning_rate': 8.05699481865285e-06, 'epoch': 0.4}
{'loss': 0.8224, 'grad_norm': 2.462656021118164, 'learning_rate': 8.031088082901555e-06, 'epoch': 0.41}
{'loss': 0.966, 'grad_norm': 1.9545427560806274, 'learning_rate': 8.005181347150259e-06, 'epoch': 0.41}
{'loss': 0.6323, 'grad_norm': 2.3197035789489746, 'learning_rate': 7.979274611398965e-06, 'epoch': 0.42}
{'loss': 0.8814, 'grad_norm': 2.51138973236084, 'learning_rate': 7.953367875647669e-06, 'epoch': 0.42}
{'loss': 0.7239, 'grad_norm': 2.2599854469299316, 'learning_rate': 7.927461139896375e-06, 'epoch': 0.43}
{'loss': 0.8482, 'grad_norm': 2.1153910160064697, 'learning_rate': 7.901554404145079e-06, 'epoch': 0.43}
{'loss': 0.6717, 'grad_norm': 2.4645915031433105, 'learning_rate': 7.875647668393783e-06, 'epoch': 0.44}
{'loss': 0.5643, 'grad_norm': 2.230720043182373, 'learning_rate': 7.849740932642487e-06, 'epoch': 0.44}
{'loss': 1.1067, 'grad_norm': 2.247462749481201, 'learning_rate': 7.823834196891192e-06, 'epoch': 0.45}
{'loss': 0.5903, 'grad_norm': 2.122015953063965, 'learning_rate': 7.797927461139898e-06, 'epoch': 0.45}
{'loss': 0.8035, 'grad_norm': 2.2608187198638916, 'learning_rate': 7.772020725388602e-06, 'epoch': 0.46}
{'loss': 0.9305, 'grad_norm': 2.020524740219116, 'learning_rate': 7.746113989637306e-06, 'epoch': 0.46}
{'loss': 0.8977, 'grad_norm': 2.1622703075408936, 'learning_rate': 7.72020725388601e-06, 'epoch': 0.47}
{'loss': 0.5507, 'grad_norm': 2.5529732704162598, 'learning_rate': 7.694300518134716e-06, 'epoch': 0.47}
{'loss': 0.6802, 'grad_norm': 2.2691476345062256, 'learning_rate': 7.66839378238342e-06, 'epoch': 0.48}
{'loss': 0.546, 'grad_norm': 2.7194442749023438, 'learning_rate': 7.642487046632126e-06, 'epoch': 0.48}
{'loss': 0.7549, 'grad_norm': 2.5331454277038574, 'learning_rate': 7.61658031088083e-06, 'epoch': 0.49}
{'loss': 1.0136, 'grad_norm': 2.519665241241455, 'learning_rate': 7.590673575129535e-06, 'epoch': 0.49}
{'loss': 0.6214, 'grad_norm': 2.548218250274658, 'learning_rate': 7.564766839378239e-06, 'epoch': 0.5}
{'loss': 1.031, 'grad_norm': 2.20029354095459, 'learning_rate': 7.538860103626944e-06, 'epoch': 0.51}
{'loss': 0.708, 'grad_norm': 2.1590566635131836, 'learning_rate': 7.512953367875648e-06, 'epoch': 0.51}
{'loss': 0.7503, 'grad_norm': 2.317505121231079, 'learning_rate': 7.487046632124353e-06, 'epoch': 0.52}
{'loss': 0.6039, 'grad_norm': 2.3594632148742676, 'learning_rate': 7.461139896373057e-06, 'epoch': 0.52}
{'loss': 0.7126, 'grad_norm': 1.7658730745315552, 'learning_rate': 7.435233160621762e-06, 'epoch': 0.53}
{'loss': 0.7095, 'grad_norm': 2.122969388961792, 'learning_rate': 7.409326424870467e-06, 'epoch': 0.53}
{'loss': 0.5636, 'grad_norm': 2.3028645515441895, 'learning_rate': 7.383419689119171e-06, 'epoch': 0.54}
{'loss': 0.9669, 'grad_norm': 2.465442419052124, 'learning_rate': 7.357512953367876e-06, 'epoch': 0.54}
{'loss': 0.515, 'grad_norm': 2.96199631690979, 'learning_rate': 7.331606217616582e-06, 'epoch': 0.55}
{'loss': 0.6113, 'grad_norm': 2.0609958171844482, 'learning_rate': 7.305699481865286e-06, 'epoch': 0.55}
{'loss': 0.7473, 'grad_norm': 2.623793840408325, 'learning_rate': 7.27979274611399e-06, 'epoch': 0.56}
{'loss': 0.6019, 'grad_norm': 2.7164416313171387, 'learning_rate': 7.253886010362695e-06, 'epoch': 0.56}
{'loss': 0.5818, 'grad_norm': 2.022935152053833, 'learning_rate': 7.2279792746113995e-06, 'epoch': 0.57}
{'loss': 0.4846, 'grad_norm': 2.591858386993408, 'learning_rate': 7.2020725388601045e-06, 'epoch': 0.57}
{'loss': 0.8831, 'grad_norm': 2.279107093811035, 'learning_rate': 7.176165803108809e-06, 'epoch': 0.58}
{'loss': 0.7941, 'grad_norm': 2.376699924468994, 'learning_rate': 7.150259067357514e-06, 'epoch': 0.58}
{'loss': 0.6602, 'grad_norm': 2.5077431201934814, 'learning_rate': 7.124352331606218e-06, 'epoch': 0.59}
{'loss': 0.7356, 'grad_norm': 2.730398416519165, 'learning_rate': 7.098445595854922e-06, 'epoch': 0.59}
{'loss': 0.5004, 'grad_norm': 2.6164727210998535, 'learning_rate': 7.072538860103627e-06, 'epoch': 0.6}
{'loss': 0.7939, 'grad_norm': 2.6170222759246826, 'learning_rate': 7.0466321243523315e-06, 'epoch': 0.6}
{'loss': 0.6828, 'grad_norm': 2.3522651195526123, 'learning_rate': 7.020725388601037e-06, 'epoch': 0.61}
{'loss': 1.0209, 'grad_norm': 2.4204940795898438, 'learning_rate': 6.994818652849742e-06, 'epoch': 0.61}
{'loss': 0.5828, 'grad_norm': 2.4277994632720947, 'learning_rate': 6.968911917098447e-06, 'epoch': 0.62}
{'loss': 0.8186, 'grad_norm': 2.5694141387939453, 'learning_rate': 6.943005181347151e-06, 'epoch': 0.62}
{'loss': 0.5802, 'grad_norm': 2.2575204372406006, 'learning_rate': 6.917098445595856e-06, 'epoch': 0.63}
{'loss': 0.7907, 'grad_norm': 2.4539308547973633, 'learning_rate': 6.89119170984456e-06, 'epoch': 0.63}
{'loss': 0.5764, 'grad_norm': 2.5156893730163574, 'learning_rate': 6.865284974093265e-06, 'epoch': 0.64}
{'loss': 1.0345, 'grad_norm': 2.1608986854553223, 'learning_rate': 6.839378238341969e-06, 'epoch': 0.64}
{'loss': 0.7751, 'grad_norm': 1.9052895307540894, 'learning_rate': 6.813471502590674e-06, 'epoch': 0.65}
{'loss': 0.6016, 'grad_norm': 1.7114657163619995, 'learning_rate': 6.787564766839379e-06, 'epoch': 0.65}
{'loss': 0.7309, 'grad_norm': 1.8957144021987915, 'learning_rate': 6.761658031088083e-06, 'epoch': 0.66}
{'loss': 0.8768, 'grad_norm': 2.0205330848693848, 'learning_rate': 6.735751295336788e-06, 'epoch': 0.66}
{'loss': 0.6047, 'grad_norm': 2.091603994369507, 'learning_rate': 6.709844559585493e-06, 'epoch': 0.67}
{'loss': 0.8586, 'grad_norm': 2.113060474395752, 'learning_rate': 6.683937823834198e-06, 'epoch': 0.68}
{'loss': 1.0116, 'grad_norm': 2.217136859893799, 'learning_rate': 6.658031088082902e-06, 'epoch': 0.68}
{'loss': 0.637, 'grad_norm': 2.951040744781494, 'learning_rate': 6.632124352331607e-06, 'epoch': 0.69}
{'loss': 0.5337, 'grad_norm': 2.380998134613037, 'learning_rate': 6.6062176165803115e-06, 'epoch': 0.69}
{'loss': 1.0006, 'grad_norm': 2.2579574584960938, 'learning_rate': 6.5803108808290166e-06, 'epoch': 0.7}
{'loss': 0.7635, 'grad_norm': 2.230733633041382, 'learning_rate': 6.554404145077721e-06, 'epoch': 0.7}
{'loss': 0.7646, 'grad_norm': 2.244591236114502, 'learning_rate': 6.528497409326425e-06, 'epoch': 0.71}
{'loss': 0.7348, 'grad_norm': 2.008380651473999, 'learning_rate': 6.50259067357513e-06, 'epoch': 0.71}
{'loss': 0.9412, 'grad_norm': 1.8554458618164062, 'learning_rate': 6.476683937823834e-06, 'epoch': 0.72}
{'loss': 0.477, 'grad_norm': 1.9788316488265991, 'learning_rate': 6.450777202072539e-06, 'epoch': 0.72}
{'loss': 1.0519, 'grad_norm': 2.246572732925415, 'learning_rate': 6.4248704663212435e-06, 'epoch': 0.73}
{'loss': 0.6619, 'grad_norm': 2.317051410675049, 'learning_rate': 6.398963730569949e-06, 'epoch': 0.73}
{'loss': 0.9211, 'grad_norm': 2.367633581161499, 'learning_rate': 6.373056994818654e-06, 'epoch': 0.74}
{'loss': 0.6537, 'grad_norm': 2.689427375793457, 'learning_rate': 6.347150259067359e-06, 'epoch': 0.74}
{'loss': 0.6335, 'grad_norm': 2.0595192909240723, 'learning_rate': 6.321243523316063e-06, 'epoch': 0.75}
{'loss': 1.0649, 'grad_norm': 2.8765666484832764, 'learning_rate': 6.295336787564768e-06, 'epoch': 0.75}
{'loss': 0.7946, 'grad_norm': 2.6361300945281982, 'learning_rate': 6.269430051813472e-06, 'epoch': 0.76}
{'loss': 0.6266, 'grad_norm': 2.265388011932373, 'learning_rate': 6.243523316062176e-06, 'epoch': 0.76}
{'loss': 0.6959, 'grad_norm': 2.150740623474121, 'learning_rate': 6.217616580310881e-06, 'epoch': 0.77}
{'loss': 1.1691, 'grad_norm': 2.1308672428131104, 'learning_rate': 6.191709844559586e-06, 'epoch': 0.77}
{'loss': 0.7522, 'grad_norm': 2.356973171234131, 'learning_rate': 6.165803108808291e-06, 'epoch': 0.78}
{'loss': 0.7463, 'grad_norm': 1.7162338495254517, 'learning_rate': 6.139896373056995e-06, 'epoch': 0.78}
{'loss': 0.8641, 'grad_norm': 2.0620827674865723, 'learning_rate': 6.113989637305699e-06, 'epoch': 0.79}
{'loss': 0.8537, 'grad_norm': 2.4712204933166504, 'learning_rate': 6.088082901554405e-06, 'epoch': 0.79}
{'loss': 0.6013, 'grad_norm': 1.9165736436843872, 'learning_rate': 6.06217616580311e-06, 'epoch': 0.8}
{'loss': 0.8943, 'grad_norm': 2.1998610496520996, 'learning_rate': 6.036269430051814e-06, 'epoch': 0.8}
{'loss': 0.6887, 'grad_norm': 1.972203254699707, 'learning_rate': 6.0103626943005185e-06, 'epoch': 0.81}
{'loss': 0.5238, 'grad_norm': 2.0836496353149414, 'learning_rate': 5.9844559585492235e-06, 'epoch': 0.81}
{'loss': 0.7665, 'grad_norm': 2.111018419265747, 'learning_rate': 5.958549222797928e-06, 'epoch': 0.82}
{'loss': 0.6519, 'grad_norm': 1.956825613975525, 'learning_rate': 5.932642487046633e-06, 'epoch': 0.82}
{'loss': 0.5579, 'grad_norm': 2.002511501312256, 'learning_rate': 5.906735751295337e-06, 'epoch': 0.83}
{'loss': 0.4828, 'grad_norm': 2.447082996368408, 'learning_rate': 5.880829015544042e-06, 'epoch': 0.84}
{'loss': 0.4924, 'grad_norm': 2.4028160572052, 'learning_rate': 5.854922279792746e-06, 'epoch': 0.84}
{'loss': 0.6621, 'grad_norm': 2.1808340549468994, 'learning_rate': 5.8290155440414505e-06, 'epoch': 0.85}
{'loss': 0.8003, 'grad_norm': 2.14582896232605, 'learning_rate': 5.8031088082901555e-06, 'epoch': 0.85}
{'loss': 0.7265, 'grad_norm': 2.1482536792755127, 'learning_rate': 5.7772020725388614e-06, 'epoch': 0.86}
{'loss': 1.1708, 'grad_norm': 2.4824554920196533, 'learning_rate': 5.751295336787566e-06, 'epoch': 0.86}
{'loss': 0.5824, 'grad_norm': 2.3513762950897217, 'learning_rate': 5.72538860103627e-06, 'epoch': 0.87}
{'loss': 0.5978, 'grad_norm': 2.6317238807678223, 'learning_rate': 5.699481865284975e-06, 'epoch': 0.87}
{'loss': 0.6302, 'grad_norm': 2.019167900085449, 'learning_rate': 5.673575129533679e-06, 'epoch': 0.88}
{'loss': 0.5126, 'grad_norm': 1.8639978170394897, 'learning_rate': 5.647668393782384e-06, 'epoch': 0.88}
{'loss': 0.5394, 'grad_norm': 2.1784842014312744, 'learning_rate': 5.621761658031088e-06, 'epoch': 0.89}
{'loss': 0.5318, 'grad_norm': 2.814495801925659, 'learning_rate': 5.5958549222797934e-06, 'epoch': 0.89}
{'loss': 0.5904, 'grad_norm': 1.883297085762024, 'learning_rate': 5.569948186528498e-06, 'epoch': 0.9}
{'loss': 0.9498, 'grad_norm': 2.2201955318450928, 'learning_rate': 5.544041450777202e-06, 'epoch': 0.9}
{'loss': 0.8025, 'grad_norm': 2.072869062423706, 'learning_rate': 5.518134715025907e-06, 'epoch': 0.91}
{'loss': 0.4942, 'grad_norm': 2.1868302822113037, 'learning_rate': 5.492227979274611e-06, 'epoch': 0.91}
{'loss': 0.8148, 'grad_norm': 2.412132978439331, 'learning_rate': 5.466321243523317e-06, 'epoch': 0.92}
{'loss': 0.8432, 'grad_norm': 2.23472261428833, 'learning_rate': 5.440414507772021e-06, 'epoch': 0.92}
{'loss': 0.8168, 'grad_norm': 1.9256441593170166, 'learning_rate': 5.414507772020726e-06, 'epoch': 0.93}
{'loss': 0.6928, 'grad_norm': 2.193864345550537, 'learning_rate': 5.3886010362694305e-06, 'epoch': 0.93}
{'loss': 0.6571, 'grad_norm': 2.026597023010254, 'learning_rate': 5.3626943005181356e-06, 'epoch': 0.94}
{'loss': 0.6279, 'grad_norm': 2.291626453399658, 'learning_rate': 5.33678756476684e-06, 'epoch': 0.94}
{'loss': 0.6739, 'grad_norm': 1.8444104194641113, 'learning_rate': 5.310880829015545e-06, 'epoch': 0.95}
{'loss': 1.0054, 'grad_norm': 2.1798253059387207, 'learning_rate': 5.284974093264249e-06, 'epoch': 0.95}
{'loss': 0.9729, 'grad_norm': 2.4138057231903076, 'learning_rate': 5.259067357512953e-06, 'epoch': 0.96}
{'loss': 0.6446, 'grad_norm': 2.078237771987915, 'learning_rate': 5.233160621761658e-06, 'epoch': 0.96}
{'loss': 1.0359, 'grad_norm': 2.258807420730591, 'learning_rate': 5.2072538860103625e-06, 'epoch': 0.97}
{'loss': 0.6271, 'grad_norm': 1.9247407913208008, 'learning_rate': 5.1813471502590676e-06, 'epoch': 0.97}
{'loss': 0.9354, 'grad_norm': 2.414335012435913, 'learning_rate': 5.155440414507773e-06, 'epoch': 0.98}
{'loss': 0.7737, 'grad_norm': 2.6068100929260254, 'learning_rate': 5.129533678756478e-06, 'epoch': 0.98}
{'loss': 0.629, 'grad_norm': 2.1987404823303223, 'learning_rate': 5.103626943005182e-06, 'epoch': 0.99}
{'loss': 0.664, 'grad_norm': 2.374802350997925, 'learning_rate': 5.077720207253887e-06, 'epoch': 0.99}
{'loss': 0.5583, 'grad_norm': 3.1563501358032227, 'learning_rate': 5.051813471502591e-06, 'epoch': 1.0}
{'loss': 0.7121, 'grad_norm': 2.0836429595947266, 'learning_rate': 5.025906735751296e-06, 'epoch': 1.01}
{'loss': 0.499, 'grad_norm': 2.0512752532958984, 'learning_rate': 5e-06, 'epoch': 1.01}
{'loss': 0.3842, 'grad_norm': 1.8030757904052734, 'learning_rate': 4.974093264248705e-06, 'epoch': 1.02}
{'loss': 0.5511, 'grad_norm': 2.8820579051971436, 'learning_rate': 4.94818652849741e-06, 'epoch': 1.02}
{'loss': 0.5513, 'grad_norm': 2.530095100402832, 'learning_rate': 4.922279792746114e-06, 'epoch': 1.03}
{'loss': 0.6154, 'grad_norm': 2.7577624320983887, 'learning_rate': 4.896373056994819e-06, 'epoch': 1.03}
{'loss': 0.5226, 'grad_norm': 2.301081895828247, 'learning_rate': 4.870466321243524e-06, 'epoch': 1.04}
{'loss': 0.4099, 'grad_norm': 2.4583969116210938, 'learning_rate': 4.844559585492228e-06, 'epoch': 1.04}
{'loss': 0.6281, 'grad_norm': 2.238999366760254, 'learning_rate': 4.818652849740933e-06, 'epoch': 1.05}
{'loss': 0.6042, 'grad_norm': 2.4057085514068604, 'learning_rate': 4.7927461139896375e-06, 'epoch': 1.05}
{'loss': 0.6049, 'grad_norm': 2.283625364303589, 'learning_rate': 4.766839378238342e-06, 'epoch': 1.06}
{'loss': 0.4113, 'grad_norm': 2.3003745079040527, 'learning_rate': 4.740932642487048e-06, 'epoch': 1.06}
{'loss': 0.6683, 'grad_norm': 2.209561586380005, 'learning_rate': 4.715025906735752e-06, 'epoch': 1.07}
{'loss': 0.4816, 'grad_norm': 1.7584269046783447, 'learning_rate': 4.689119170984456e-06, 'epoch': 1.07}
{'loss': 0.4937, 'grad_norm': 2.759676218032837, 'learning_rate': 4.663212435233161e-06, 'epoch': 1.08}
{'loss': 0.5405, 'grad_norm': 2.448197603225708, 'learning_rate': 4.637305699481865e-06, 'epoch': 1.08}
{'loss': 0.5399, 'grad_norm': 2.5964267253875732, 'learning_rate': 4.61139896373057e-06, 'epoch': 1.09}
{'loss': 0.2167, 'grad_norm': 2.8341469764709473, 'learning_rate': 4.585492227979275e-06, 'epoch': 1.09}
{'loss': 0.5542, 'grad_norm': 4.033329486846924, 'learning_rate': 4.55958549222798e-06, 'epoch': 1.1}
{'loss': 0.2834, 'grad_norm': 2.854736804962158, 'learning_rate': 4.533678756476685e-06, 'epoch': 1.1}
{'loss': 0.4439, 'grad_norm': 2.79582142829895, 'learning_rate': 4.507772020725389e-06, 'epoch': 1.11}
{'loss': 0.4934, 'grad_norm': 2.369055986404419, 'learning_rate': 4.481865284974093e-06, 'epoch': 1.11}
{'loss': 0.6405, 'grad_norm': 2.2721002101898193, 'learning_rate': 4.455958549222798e-06, 'epoch': 1.12}
{'loss': 0.6702, 'grad_norm': 2.5050008296966553, 'learning_rate': 4.430051813471503e-06, 'epoch': 1.12}
{'loss': 0.4025, 'grad_norm': 2.191049575805664, 'learning_rate': 4.404145077720207e-06, 'epoch': 1.13}
{'loss': 0.3161, 'grad_norm': 3.585730791091919, 'learning_rate': 4.3782383419689124e-06, 'epoch': 1.13}
{'loss': 0.4529, 'grad_norm': 2.921163320541382, 'learning_rate': 4.352331606217617e-06, 'epoch': 1.14}
{'loss': 0.3962, 'grad_norm': 2.3244917392730713, 'learning_rate': 4.326424870466322e-06, 'epoch': 1.14}
{'loss': 0.3131, 'grad_norm': 2.107811212539673, 'learning_rate': 4.300518134715026e-06, 'epoch': 1.15}
{'loss': 0.5665, 'grad_norm': 1.9965029954910278, 'learning_rate': 4.274611398963731e-06, 'epoch': 1.15}
{'loss': 0.7093, 'grad_norm': 2.1175429821014404, 'learning_rate': 4.248704663212436e-06, 'epoch': 1.16}
{'loss': 0.4905, 'grad_norm': 2.3432583808898926, 'learning_rate': 4.22279792746114e-06, 'epoch': 1.16}
{'loss': 0.7729, 'grad_norm': 2.5018038749694824, 'learning_rate': 4.1968911917098444e-06, 'epoch': 1.17}
{'loss': 0.6847, 'grad_norm': 2.631490707397461, 'learning_rate': 4.1709844559585495e-06, 'epoch': 1.18}
{'loss': 0.551, 'grad_norm': 2.2843759059906006, 'learning_rate': 4.145077720207254e-06, 'epoch': 1.18}
{'loss': 0.7523, 'grad_norm': 2.279371976852417, 'learning_rate': 4.119170984455959e-06, 'epoch': 1.19}
{'loss': 0.3825, 'grad_norm': 2.154730796813965, 'learning_rate': 4.093264248704664e-06, 'epoch': 1.19}
{'loss': 0.6958, 'grad_norm': 2.1635422706604004, 'learning_rate': 4.067357512953368e-06, 'epoch': 1.2}
{'loss': 0.4847, 'grad_norm': 2.636568069458008, 'learning_rate': 4.041450777202073e-06, 'epoch': 1.2}
{'loss': 0.6932, 'grad_norm': 2.0792791843414307, 'learning_rate': 4.015544041450777e-06, 'epoch': 1.21}
{'loss': 0.7191, 'grad_norm': 2.0182125568389893, 'learning_rate': 3.989637305699482e-06, 'epoch': 1.21}
{'loss': 0.3539, 'grad_norm': 2.325082302093506, 'learning_rate': 3.963730569948187e-06, 'epoch': 1.22}
{'loss': 0.6365, 'grad_norm': 2.777247905731201, 'learning_rate': 3.937823834196892e-06, 'epoch': 1.22}
{'loss': 0.357, 'grad_norm': 2.4695026874542236, 'learning_rate': 3.911917098445596e-06, 'epoch': 1.23}
{'loss': 0.5525, 'grad_norm': 2.4125864505767822, 'learning_rate': 3.886010362694301e-06, 'epoch': 1.23}
{'loss': 0.5571, 'grad_norm': 2.771406650543213, 'learning_rate': 3.860103626943005e-06, 'epoch': 1.24}
{'loss': 0.369, 'grad_norm': 2.086074113845825, 'learning_rate': 3.83419689119171e-06, 'epoch': 1.24}
{'loss': 0.4846, 'grad_norm': 2.6102590560913086, 'learning_rate': 3.808290155440415e-06, 'epoch': 1.25}
{'loss': 0.3644, 'grad_norm': 2.324188232421875, 'learning_rate': 3.7823834196891194e-06, 'epoch': 1.25}
{'loss': 0.6624, 'grad_norm': 2.483508348464966, 'learning_rate': 3.756476683937824e-06, 'epoch': 1.26}
{'loss': 0.4537, 'grad_norm': 2.391200542449951, 'learning_rate': 3.7305699481865287e-06, 'epoch': 1.26}
{'loss': 0.5079, 'grad_norm': 2.1135330200195312, 'learning_rate': 3.7046632124352333e-06, 'epoch': 1.27}
{'loss': 0.2855, 'grad_norm': 2.4372682571411133, 'learning_rate': 3.678756476683938e-06, 'epoch': 1.27}
{'loss': 0.4011, 'grad_norm': 1.8742483854293823, 'learning_rate': 3.652849740932643e-06, 'epoch': 1.28}
{'loss': 0.5521, 'grad_norm': 1.9814589023590088, 'learning_rate': 3.6269430051813476e-06, 'epoch': 1.28}
{'loss': 0.5789, 'grad_norm': 2.020714044570923, 'learning_rate': 3.6010362694300523e-06, 'epoch': 1.29}
{'loss': 0.5045, 'grad_norm': 2.4552905559539795, 'learning_rate': 3.575129533678757e-06, 'epoch': 1.29}
{'loss': 0.4487, 'grad_norm': 2.143057346343994, 'learning_rate': 3.549222797927461e-06, 'epoch': 1.3}
{'loss': 0.562, 'grad_norm': 2.016857147216797, 'learning_rate': 3.5233160621761657e-06, 'epoch': 1.3}
{'loss': 0.9041, 'grad_norm': 2.293997287750244, 'learning_rate': 3.497409326424871e-06, 'epoch': 1.31}
{'loss': 0.4674, 'grad_norm': 2.3336353302001953, 'learning_rate': 3.4715025906735754e-06, 'epoch': 1.31}
{'loss': 0.3567, 'grad_norm': 1.8112561702728271, 'learning_rate': 3.44559585492228e-06, 'epoch': 1.32}
{'loss': 0.3736, 'grad_norm': 2.2844161987304688, 'learning_rate': 3.4196891191709847e-06, 'epoch': 1.32}
{'loss': 0.4566, 'grad_norm': 2.512300968170166, 'learning_rate': 3.3937823834196893e-06, 'epoch': 1.33}
{'loss': 0.289, 'grad_norm': 2.7513668537139893, 'learning_rate': 3.367875647668394e-06, 'epoch': 1.34}
{'loss': 0.5362, 'grad_norm': 2.3287911415100098, 'learning_rate': 3.341968911917099e-06, 'epoch': 1.34}
{'loss': 0.3982, 'grad_norm': 2.460958242416382, 'learning_rate': 3.3160621761658036e-06, 'epoch': 1.35}
{'loss': 0.6603, 'grad_norm': 2.2614171504974365, 'learning_rate': 3.2901554404145083e-06, 'epoch': 1.35}
{'loss': 0.3947, 'grad_norm': 2.1517562866210938, 'learning_rate': 3.2642487046632125e-06, 'epoch': 1.36}
{'loss': 0.3566, 'grad_norm': 2.6794075965881348, 'learning_rate': 3.238341968911917e-06, 'epoch': 1.36}
{'loss': 0.3596, 'grad_norm': 2.277747869491577, 'learning_rate': 3.2124352331606218e-06, 'epoch': 1.37}
{'loss': 0.5912, 'grad_norm': 2.5137126445770264, 'learning_rate': 3.186528497409327e-06, 'epoch': 1.37}
[2025-07-08 19:44:00,590] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 0.4655, 'grad_norm': 2.1888692378997803, 'learning_rate': 3.1606217616580314e-06, 'epoch': 1.38}
{'loss': 0.576, 'grad_norm': 2.2129290103912354, 'learning_rate': 3.134715025906736e-06, 'epoch': 1.38}
{'loss': 0.355, 'grad_norm': 3.1906845569610596, 'learning_rate': 3.1088082901554407e-06, 'epoch': 1.39}
{'loss': 0.3613, 'grad_norm': 2.2897989749908447, 'learning_rate': 3.0829015544041453e-06, 'epoch': 1.39}
{'loss': 0.4327, 'grad_norm': 2.437657356262207, 'learning_rate': 3.0569948186528495e-06, 'epoch': 1.4}
{'loss': 0.4898, 'grad_norm': 2.3233554363250732, 'learning_rate': 3.031088082901555e-06, 'epoch': 1.4}
{'loss': 0.3231, 'grad_norm': 2.113227367401123, 'learning_rate': 3.0051813471502592e-06, 'epoch': 1.41}
{'loss': 0.3608, 'grad_norm': 2.2778213024139404, 'learning_rate': 2.979274611398964e-06, 'epoch': 1.41}
{'loss': 0.5681, 'grad_norm': 1.8296815156936646, 'learning_rate': 2.9533678756476685e-06, 'epoch': 1.42}
{'loss': 0.6308, 'grad_norm': 1.8510407209396362, 'learning_rate': 2.927461139896373e-06, 'epoch': 1.42}
{'loss': 0.3525, 'grad_norm': 2.0309178829193115, 'learning_rate': 2.9015544041450778e-06, 'epoch': 1.43}
{'loss': 0.7015, 'grad_norm': 2.1533703804016113, 'learning_rate': 2.875647668393783e-06, 'epoch': 1.43}
{'loss': 0.5073, 'grad_norm': 2.123189687728882, 'learning_rate': 2.8497409326424875e-06, 'epoch': 1.44}
{'loss': 0.5581, 'grad_norm': 1.8934084177017212, 'learning_rate': 2.823834196891192e-06, 'epoch': 1.44}
{'loss': 0.4605, 'grad_norm': 2.3454744815826416, 'learning_rate': 2.7979274611398967e-06, 'epoch': 1.45}
{'loss': 0.3607, 'grad_norm': 1.9981955289840698, 'learning_rate': 2.772020725388601e-06, 'epoch': 1.45}
{'loss': 0.4104, 'grad_norm': 2.231193780899048, 'learning_rate': 2.7461139896373056e-06, 'epoch': 1.46}
{'loss': 0.2536, 'grad_norm': 3.4163265228271484, 'learning_rate': 2.7202072538860106e-06, 'epoch': 1.46}
{'loss': 0.3403, 'grad_norm': 2.154046058654785, 'learning_rate': 2.6943005181347152e-06, 'epoch': 1.47}
{'loss': 0.6198, 'grad_norm': 2.1074230670928955, 'learning_rate': 2.66839378238342e-06, 'epoch': 1.47}
{'loss': 0.5823, 'grad_norm': 2.559756278991699, 'learning_rate': 2.6424870466321245e-06, 'epoch': 1.48}
{'loss': 0.8734, 'grad_norm': 2.415984869003296, 'learning_rate': 2.616580310880829e-06, 'epoch': 1.48}
{'loss': 0.556, 'grad_norm': 2.7131714820861816, 'learning_rate': 2.5906735751295338e-06, 'epoch': 1.49}
{'loss': 0.8368, 'grad_norm': 2.8106203079223633, 'learning_rate': 2.564766839378239e-06, 'epoch': 1.49}
{'loss': 0.5506, 'grad_norm': 2.2179758548736572, 'learning_rate': 2.5388601036269435e-06, 'epoch': 1.5}
{'loss': 0.4746, 'grad_norm': 1.8650314807891846, 'learning_rate': 2.512953367875648e-06, 'epoch': 1.51}
{'loss': 0.3436, 'grad_norm': 2.1396875381469727, 'learning_rate': 2.4870466321243523e-06, 'epoch': 1.51}
{'loss': 0.4165, 'grad_norm': 1.8841402530670166, 'learning_rate': 2.461139896373057e-06, 'epoch': 1.52}
{'loss': 0.7539, 'grad_norm': 2.113678216934204, 'learning_rate': 2.435233160621762e-06, 'epoch': 1.52}
{'loss': 0.4493, 'grad_norm': 2.5732593536376953, 'learning_rate': 2.4093264248704666e-06, 'epoch': 1.53}
{'loss': 0.3467, 'grad_norm': 2.224625825881958, 'learning_rate': 2.383419689119171e-06, 'epoch': 1.53}
{'loss': 0.4526, 'grad_norm': 2.6423981189727783, 'learning_rate': 2.357512953367876e-06, 'epoch': 1.54}
{'loss': 0.3433, 'grad_norm': 1.8617727756500244, 'learning_rate': 2.3316062176165805e-06, 'epoch': 1.54}
{'loss': 0.6761, 'grad_norm': 2.285628318786621, 'learning_rate': 2.305699481865285e-06, 'epoch': 1.55}
{'loss': 0.6779, 'grad_norm': 2.2116644382476807, 'learning_rate': 2.27979274611399e-06, 'epoch': 1.55}
{'loss': 0.321, 'grad_norm': 2.1227314472198486, 'learning_rate': 2.2538860103626944e-06, 'epoch': 1.56}
{'loss': 0.4848, 'grad_norm': 2.27170991897583, 'learning_rate': 2.227979274611399e-06, 'epoch': 1.56}
{'loss': 0.4081, 'grad_norm': 2.157994031906128, 'learning_rate': 2.2020725388601037e-06, 'epoch': 1.57}
{'loss': 0.4548, 'grad_norm': 2.0860350131988525, 'learning_rate': 2.1761658031088083e-06, 'epoch': 1.57}
{'loss': 0.4788, 'grad_norm': 2.903682231903076, 'learning_rate': 2.150259067357513e-06, 'epoch': 1.58}
{'loss': 0.5734, 'grad_norm': 2.398547649383545, 'learning_rate': 2.124352331606218e-06, 'epoch': 1.58}
{'loss': 0.446, 'grad_norm': 2.53910493850708, 'learning_rate': 2.0984455958549222e-06, 'epoch': 1.59}
{'loss': 0.5528, 'grad_norm': 2.0708515644073486, 'learning_rate': 2.072538860103627e-06, 'epoch': 1.59}
{'loss': 0.5587, 'grad_norm': 2.0458717346191406, 'learning_rate': 2.046632124352332e-06, 'epoch': 1.6}
{'loss': 0.4448, 'grad_norm': 2.2896485328674316, 'learning_rate': 2.0207253886010365e-06, 'epoch': 1.6}
{'loss': 0.7287, 'grad_norm': 2.3465869426727295, 'learning_rate': 1.994818652849741e-06, 'epoch': 1.61}
{'loss': 0.423, 'grad_norm': 2.393112897872925, 'learning_rate': 1.968911917098446e-06, 'epoch': 1.61}
{'loss': 0.7744, 'grad_norm': 2.5381312370300293, 'learning_rate': 1.9430051813471504e-06, 'epoch': 1.62}
{'loss': 0.6686, 'grad_norm': 1.9643605947494507, 'learning_rate': 1.917098445595855e-06, 'epoch': 1.62}
{'loss': 0.3454, 'grad_norm': 2.169778823852539, 'learning_rate': 1.8911917098445597e-06, 'epoch': 1.63}
{'loss': 0.7417, 'grad_norm': 2.059842824935913, 'learning_rate': 1.8652849740932643e-06, 'epoch': 1.63}
{'loss': 0.5585, 'grad_norm': 1.7974945306777954, 'learning_rate': 1.839378238341969e-06, 'epoch': 1.64}
{'loss': 0.3245, 'grad_norm': 2.0723960399627686, 'learning_rate': 1.8134715025906738e-06, 'epoch': 1.64}
{'loss': 0.6335, 'grad_norm': 1.7244951725006104, 'learning_rate': 1.7875647668393784e-06, 'epoch': 1.65}
{'loss': 0.2948, 'grad_norm': 2.3602211475372314, 'learning_rate': 1.7616580310880829e-06, 'epoch': 1.65}
{'loss': 0.4723, 'grad_norm': 2.367523670196533, 'learning_rate': 1.7357512953367877e-06, 'epoch': 1.66}
{'loss': 0.7215, 'grad_norm': 1.9810079336166382, 'learning_rate': 1.7098445595854923e-06, 'epoch': 1.66}
{'loss': 0.375, 'grad_norm': 2.228205919265747, 'learning_rate': 1.683937823834197e-06, 'epoch': 1.67}
{'loss': 0.4098, 'grad_norm': 2.2735326290130615, 'learning_rate': 1.6580310880829018e-06, 'epoch': 1.68}
{'loss': 0.4386, 'grad_norm': 2.075532913208008, 'learning_rate': 1.6321243523316062e-06, 'epoch': 1.68}
{'loss': 0.4276, 'grad_norm': 1.7820451259613037, 'learning_rate': 1.6062176165803109e-06, 'epoch': 1.69}
{'loss': 0.5588, 'grad_norm': 2.103607654571533, 'learning_rate': 1.5803108808290157e-06, 'epoch': 1.69}
{'loss': 0.3427, 'grad_norm': 2.3997738361358643, 'learning_rate': 1.5544041450777204e-06, 'epoch': 1.7}
{'loss': 0.3634, 'grad_norm': 1.9919472932815552, 'learning_rate': 1.5284974093264248e-06, 'epoch': 1.7}
{'loss': 0.6021, 'grad_norm': 2.0764970779418945, 'learning_rate': 1.5025906735751296e-06, 'epoch': 1.71}
{'loss': 0.2526, 'grad_norm': 2.3495702743530273, 'learning_rate': 1.4766839378238342e-06, 'epoch': 1.71}
{'loss': 0.456, 'grad_norm': 2.2948033809661865, 'learning_rate': 1.4507772020725389e-06, 'epoch': 1.72}
{'loss': 0.4439, 'grad_norm': 2.115891933441162, 'learning_rate': 1.4248704663212437e-06, 'epoch': 1.72}
{'loss': 0.475, 'grad_norm': 2.1751835346221924, 'learning_rate': 1.3989637305699484e-06, 'epoch': 1.73}
{'loss': 0.4867, 'grad_norm': 2.033535957336426, 'learning_rate': 1.3730569948186528e-06, 'epoch': 1.73}
{'loss': 0.31, 'grad_norm': 2.0619421005249023, 'learning_rate': 1.3471502590673576e-06, 'epoch': 1.74}
{'loss': 0.4221, 'grad_norm': 2.047929048538208, 'learning_rate': 1.3212435233160623e-06, 'epoch': 1.74}
{'loss': 0.2444, 'grad_norm': 2.6819007396698, 'learning_rate': 1.2953367875647669e-06, 'epoch': 1.75}
{'loss': 0.5839, 'grad_norm': 1.826359748840332, 'learning_rate': 1.2694300518134717e-06, 'epoch': 1.75}
{'loss': 0.4928, 'grad_norm': 2.54584002494812, 'learning_rate': 1.2435233160621762e-06, 'epoch': 1.76}
{'loss': 0.4494, 'grad_norm': 2.2615675926208496, 'learning_rate': 1.217616580310881e-06, 'epoch': 1.76}
{'loss': 0.6856, 'grad_norm': 2.0922272205352783, 'learning_rate': 1.1917098445595854e-06, 'epoch': 1.77}
{'loss': 0.4049, 'grad_norm': 2.0808823108673096, 'learning_rate': 1.1658031088082903e-06, 'epoch': 1.77}
{'loss': 0.3212, 'grad_norm': 2.256464719772339, 'learning_rate': 1.139896373056995e-06, 'epoch': 1.78}
{'loss': 0.8571, 'grad_norm': 2.229799509048462, 'learning_rate': 1.1139896373056995e-06, 'epoch': 1.78}
{'loss': 0.6851, 'grad_norm': 2.236447811126709, 'learning_rate': 1.0880829015544042e-06, 'epoch': 1.79}
{'loss': 0.3286, 'grad_norm': 2.494565486907959, 'learning_rate': 1.062176165803109e-06, 'epoch': 1.79}
{'loss': 0.6564, 'grad_norm': 1.8351247310638428, 'learning_rate': 1.0362694300518134e-06, 'epoch': 1.8}
{'loss': 0.3133, 'grad_norm': 2.295924425125122, 'learning_rate': 1.0103626943005183e-06, 'epoch': 1.8}
{'loss': 0.4372, 'grad_norm': 1.86611807346344, 'learning_rate': 9.84455958549223e-07, 'epoch': 1.81}
{'loss': 0.6069, 'grad_norm': 2.324321985244751, 'learning_rate': 9.585492227979275e-07, 'epoch': 1.81}
{'loss': 0.5003, 'grad_norm': 1.989992380142212, 'learning_rate': 9.326424870466322e-07, 'epoch': 1.82}
{'loss': 0.3092, 'grad_norm': 2.012613534927368, 'learning_rate': 9.067357512953369e-07, 'epoch': 1.82}
{'loss': 0.3514, 'grad_norm': 2.139753580093384, 'learning_rate': 8.808290155440414e-07, 'epoch': 1.83}
{'loss': 0.4866, 'grad_norm': 1.8078922033309937, 'learning_rate': 8.549222797927462e-07, 'epoch': 1.84}
{'loss': 0.4126, 'grad_norm': 1.9590295553207397, 'learning_rate': 8.290155440414509e-07, 'epoch': 1.84}
{'loss': 0.4893, 'grad_norm': 2.1362719535827637, 'learning_rate': 8.031088082901554e-07, 'epoch': 1.85}
{'loss': 0.3293, 'grad_norm': 2.2367093563079834, 'learning_rate': 7.772020725388602e-07, 'epoch': 1.85}
{'loss': 0.5683, 'grad_norm': 2.1948885917663574, 'learning_rate': 7.512953367875648e-07, 'epoch': 1.86}
{'loss': 0.5219, 'grad_norm': 2.2769477367401123, 'learning_rate': 7.253886010362694e-07, 'epoch': 1.86}
{'loss': 0.3705, 'grad_norm': 1.9062994718551636, 'learning_rate': 6.994818652849742e-07, 'epoch': 1.87}
{'loss': 0.641, 'grad_norm': 2.6360771656036377, 'learning_rate': 6.735751295336788e-07, 'epoch': 1.87}
{'loss': 0.4342, 'grad_norm': 1.8327797651290894, 'learning_rate': 6.476683937823834e-07, 'epoch': 1.88}
{'loss': 0.5184, 'grad_norm': 2.3718812465667725, 'learning_rate': 6.217616580310881e-07, 'epoch': 1.88}
{'loss': 0.4358, 'grad_norm': 2.274719476699829, 'learning_rate': 5.958549222797927e-07, 'epoch': 1.89}
{'loss': 0.8156, 'grad_norm': 2.0248847007751465, 'learning_rate': 5.699481865284974e-07, 'epoch': 1.89}
{'loss': 0.5811, 'grad_norm': 1.902496099472046, 'learning_rate': 5.440414507772021e-07, 'epoch': 1.9}
{'loss': 0.4191, 'grad_norm': 2.6043856143951416, 'learning_rate': 5.181347150259067e-07, 'epoch': 1.9}
{'loss': 0.5595, 'grad_norm': 2.029062032699585, 'learning_rate': 4.922279792746115e-07, 'epoch': 1.91}
{'loss': 0.4161, 'grad_norm': 2.047677993774414, 'learning_rate': 4.663212435233161e-07, 'epoch': 1.91}
{'loss': 0.5255, 'grad_norm': 1.6333986520767212, 'learning_rate': 4.404145077720207e-07, 'epoch': 1.92}
{'loss': 0.2504, 'grad_norm': 2.160090446472168, 'learning_rate': 4.1450777202072546e-07, 'epoch': 1.92}
{'loss': 0.4424, 'grad_norm': 2.3422210216522217, 'learning_rate': 3.886010362694301e-07, 'epoch': 1.93}
{'loss': 0.4118, 'grad_norm': 2.092709541320801, 'learning_rate': 3.626943005181347e-07, 'epoch': 1.93}
{'loss': 0.7061, 'grad_norm': 2.4941372871398926, 'learning_rate': 3.367875647668394e-07, 'epoch': 1.94}
{'loss': 0.413, 'grad_norm': 2.7243452072143555, 'learning_rate': 3.1088082901554404e-07, 'epoch': 1.94}
{'loss': 0.3881, 'grad_norm': 1.8690640926361084, 'learning_rate': 2.849740932642487e-07, 'epoch': 1.95}
{'loss': 0.3744, 'grad_norm': 2.0858564376831055, 'learning_rate': 2.5906735751295336e-07, 'epoch': 1.95}
{'loss': 0.6137, 'grad_norm': 1.994149923324585, 'learning_rate': 2.3316062176165804e-07, 'epoch': 1.96}
{'loss': 0.802, 'grad_norm': 2.0269532203674316, 'learning_rate': 2.0725388601036273e-07, 'epoch': 1.96}
{'loss': 0.619, 'grad_norm': 1.7717715501785278, 'learning_rate': 1.8134715025906736e-07, 'epoch': 1.97}
{'loss': 0.5915, 'grad_norm': 2.2780117988586426, 'learning_rate': 1.5544041450777202e-07, 'epoch': 1.97}
{'loss': 0.2116, 'grad_norm': 2.4663374423980713, 'learning_rate': 1.2953367875647668e-07, 'epoch': 1.98}
{'loss': 0.695, 'grad_norm': 2.1671128273010254, 'learning_rate': 1.0362694300518136e-07, 'epoch': 1.98}
{'loss': 0.7034, 'grad_norm': 1.8202472925186157, 'learning_rate': 7.772020725388601e-08, 'epoch': 1.99}
{'loss': 0.756, 'grad_norm': 2.7760379314422607, 'learning_rate': 5.181347150259068e-08, 'epoch': 1.99}
{'loss': 0.3306, 'grad_norm': 1.888333797454834, 'learning_rate': 2.590673575129534e-08, 'epoch': 2.0}
{'train_runtime': 4134.3311, 'train_samples_per_second': 0.374, 'train_steps_per_second': 0.094, 'train_loss': 0.6326200074141788, 'epoch': 2.0}
