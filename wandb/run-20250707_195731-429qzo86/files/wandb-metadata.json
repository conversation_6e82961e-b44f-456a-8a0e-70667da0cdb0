{"os": "Linux-5.15.0-134-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.0", "startedAt": "2025-07-07T10:57:31.773608Z", "args": ["--local_rank=0", "--deep<PERSON><PERSON>", "deepspeed3.json", "--proctitle", "junkim100", "--model_name_or_path", "Qwen3-8B", "--data_name", "/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl", "--wb_project", "kullm-pro", "--wb_name", "LIMO_Qwen3-8B", "--output_name", "LIMO_Qwen3-8B", "--max_length", "16384", "--num_train_epochs", "2", "--per_device_train_batch_size", "1", "--per_device_eval_batch_size", "1", "--gradient_accumulation_steps", "1", "--save_only_model", "--learning_rate", "1e-5", "--weight_decay", "0.", "--warmup_ratio", "0.", "--lr_scheduler_type", "cosine", "--bf16", "True", "--tf32", "True", "--gradient_checkpointing", "True", "--logging_steps", "1"], "program": "/data_x/junkim100/projects/finetune/train.py", "codePath": "train.py", "codePathLocal": "train.py", "email": "<EMAIL>", "root": "/data_x/junkim100/projects/finetune", "host": "nlp-server-18", "executable": "/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10", "cpu_count": 64, "cpu_count_logical": 128, "gpu": "NVIDIA A100-SXM4-80GB", "gpu_count": 8, "disk": {"/": {"total": "1966736678912", "used": "************"}}, "memory": {"total": "1081929859072"}, "gpu_nvidia": [{"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-8582443b-e127-d8fb-a634-b004b4e0ef48"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-bd237d9e-e3bc-da56-4072-e5ee4dcc64e4"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-c1520184-6972-679b-3986-5663223af001"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-78c7d72e-6a02-a97f-731d-6fa52d333b6d"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-a77a05bb-32c7-28e0-267c-21603a593370"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-d75897ae-51c2-373d-512d-d5ab6841c250"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-c38a24a2-5d7f-8213-2cee-94525f4d3183"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-655fcb36-1ca7-ef64-b16e-30e48f5635e4"}], "cudaVersion": "12.4", "writerId": "elkazyizst9f8vstwcpipvcma2ir4rmj"}