  0%|          | 0/388 [00:00<?, ?it/s]`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
                                                  
{'loss': 0.6118, 'grad_norm': 3.037013053894043, 'learning_rate': 0.0, 'epoch': 0.01}
{'loss': 0.4477, 'grad_norm': 4.997533798217773, 'learning_rate': 1e-05, 'epoch': 0.01}
{'loss': 0.6518, 'grad_norm': 3.441406726837158, 'learning_rate': 1e-05, 'epoch': 0.02}
{'loss': 0.5909, 'grad_norm': 2.857480764389038, 'learning_rate': 9.974093264248705e-06, 'epoch': 0.02}
{'loss': 0.4532, 'grad_norm': 1.358364462852478, 'learning_rate': 9.94818652849741e-06, 'epoch': 0.03}
{'loss': 0.4716, 'grad_norm': 2.034412145614624, 'learning_rate': 9.922279792746115e-06, 'epoch': 0.03}
{'loss': 0.5401, 'grad_norm': 1.3364737033843994, 'learning_rate': 9.89637305699482e-06, 'epoch': 0.04}
{'loss': 0.8155, 'grad_norm': 1.8729639053344727, 'learning_rate': 9.870466321243524e-06, 'epoch': 0.04}
{'loss': 0.6926, 'grad_norm': 1.7938966751098633, 'learning_rate': 9.844559585492228e-06, 'epoch': 0.05}
{'loss': 0.3846, 'grad_norm': 1.4358798265457153, 'learning_rate': 9.818652849740934e-06, 'epoch': 0.05}
{'loss': 0.5504, 'grad_norm': 1.3389484882354736, 'learning_rate': 9.792746113989638e-06, 'epoch': 0.06}
{'loss': 0.5422, 'grad_norm': 1.162811279296875, 'learning_rate': 9.766839378238344e-06, 'epoch': 0.06}
{'loss': 0.5769, 'grad_norm': 0.9763242602348328, 'learning_rate': 9.740932642487048e-06, 'epoch': 0.07}
{'loss': 0.5118, 'grad_norm': 1.156229019165039, 'learning_rate': 9.715025906735752e-06, 'epoch': 0.07}
{'loss': 0.4388, 'grad_norm': 0.8329430818557739, 'learning_rate': 9.689119170984456e-06, 'epoch': 0.08}
{'loss': 0.5285, 'grad_norm': 0.907092273235321, 'learning_rate': 9.66321243523316e-06, 'epoch': 0.08}
{'loss': 0.627, 'grad_norm': 0.9273800849914551, 'learning_rate': 9.637305699481867e-06, 'epoch': 0.09}
{'loss': 0.4012, 'grad_norm': 0.7798483967781067, 'learning_rate': 9.61139896373057e-06, 'epoch': 0.09}
{'loss': 0.5275, 'grad_norm': 0.9151303768157959, 'learning_rate': 9.585492227979275e-06, 'epoch': 0.1}
{'loss': 0.6498, 'grad_norm': 0.9406693577766418, 'learning_rate': 9.559585492227979e-06, 'epoch': 0.1}
{'loss': 0.4066, 'grad_norm': 1.1001673936843872, 'learning_rate': 9.533678756476683e-06, 'epoch': 0.11}
{'loss': 0.4623, 'grad_norm': 0.8436588644981384, 'learning_rate': 9.50777202072539e-06, 'epoch': 0.11}
{'loss': 0.3532, 'grad_norm': 0.85975581407547, 'learning_rate': 9.481865284974095e-06, 'epoch': 0.12}
{'loss': 0.4455, 'grad_norm': 1.0105911493301392, 'learning_rate': 9.4559585492228e-06, 'epoch': 0.12}
{'loss': 0.3336, 'grad_norm': 0.8208414316177368, 'learning_rate': 9.430051813471504e-06, 'epoch': 0.13}
{'loss': 0.327, 'grad_norm': 0.9341593384742737, 'learning_rate': 9.404145077720208e-06, 'epoch': 0.13}
{'loss': 0.5783, 'grad_norm': 0.733910322189331, 'learning_rate': 9.378238341968912e-06, 'epoch': 0.14}
{'loss': 0.3499, 'grad_norm': 0.7290202379226685, 'learning_rate': 9.352331606217618e-06, 'epoch': 0.14}
{'loss': 0.8039, 'grad_norm': 1.4489502906799316, 'learning_rate': 9.326424870466322e-06, 'epoch': 0.15}
{'loss': 0.6505, 'grad_norm': 1.0298844575881958, 'learning_rate': 9.300518134715026e-06, 'epoch': 0.15}
{'loss': 0.2983, 'grad_norm': 0.8450474739074707, 'learning_rate': 9.27461139896373e-06, 'epoch': 0.16}
{'loss': 0.4557, 'grad_norm': 1.1032010316848755, 'learning_rate': 9.248704663212435e-06, 'epoch': 0.16}
{'loss': 0.4925, 'grad_norm': 1.0421240329742432, 'learning_rate': 9.22279792746114e-06, 'epoch': 0.17}
{'loss': 0.5736, 'grad_norm': 0.9763453006744385, 'learning_rate': 9.196891191709847e-06, 'epoch': 0.18}
{'loss': 0.6538, 'grad_norm': 1.0696004629135132, 'learning_rate': 9.17098445595855e-06, 'epoch': 0.18}
{'loss': 0.3677, 'grad_norm': 0.8796079158782959, 'learning_rate': 9.145077720207255e-06, 'epoch': 0.19}
{'loss': 0.5165, 'grad_norm': 0.9399876594543457, 'learning_rate': 9.11917098445596e-06, 'epoch': 0.19}
{'loss': 0.3586, 'grad_norm': 0.8276817202568054, 'learning_rate': 9.093264248704663e-06, 'epoch': 0.2}
{'loss': 1.0234, 'grad_norm': 1.2928184270858765, 'learning_rate': 9.06735751295337e-06, 'epoch': 0.2}
{'loss': 0.4397, 'grad_norm': 0.84074866771698, 'learning_rate': 9.041450777202073e-06, 'epoch': 0.21}
{'loss': 0.508, 'grad_norm': 1.0501139163970947, 'learning_rate': 9.015544041450778e-06, 'epoch': 0.21}
{'loss': 0.4575, 'grad_norm': 0.8736107349395752, 'learning_rate': 8.989637305699482e-06, 'epoch': 0.22}
{'loss': 0.4772, 'grad_norm': 0.9019767642021179, 'learning_rate': 8.963730569948186e-06, 'epoch': 0.22}
{'loss': 0.3033, 'grad_norm': 0.9801058769226074, 'learning_rate': 8.937823834196892e-06, 'epoch': 0.23}
{'loss': 0.4267, 'grad_norm': 0.8529039621353149, 'learning_rate': 8.911917098445596e-06, 'epoch': 0.23}
[2025-07-07 20:05:13,026] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 0.3449, 'grad_norm': 0.9937889575958252, 'learning_rate': 8.886010362694302e-06, 'epoch': 0.24}
{'loss': 0.4012, 'grad_norm': 1.0137683153152466, 'learning_rate': 8.860103626943006e-06, 'epoch': 0.24}
{'loss': 0.499, 'grad_norm': 1.0287439823150635, 'learning_rate': 8.83419689119171e-06, 'epoch': 0.25}
{'loss': 0.5593, 'grad_norm': 0.9435105919837952, 'learning_rate': 8.808290155440415e-06, 'epoch': 0.25}
{'loss': 0.4125, 'grad_norm': 1.0088106393814087, 'learning_rate': 8.78238341968912e-06, 'epoch': 0.26}
{'loss': 0.5531, 'grad_norm': 0.7858838438987732, 'learning_rate': 8.756476683937825e-06, 'epoch': 0.26}
{'loss': 0.5977, 'grad_norm': 0.7622793912887573, 'learning_rate': 8.730569948186529e-06, 'epoch': 0.27}
{'loss': 0.5597, 'grad_norm': 1.03977370262146, 'learning_rate': 8.704663212435233e-06, 'epoch': 0.27}
{'loss': 0.4562, 'grad_norm': 0.9040418863296509, 'learning_rate': 8.678756476683938e-06, 'epoch': 0.28}
{'loss': 0.567, 'grad_norm': 0.9234251379966736, 'learning_rate': 8.652849740932643e-06, 'epoch': 0.28}
{'loss': 0.484, 'grad_norm': 0.8023905754089355, 'learning_rate': 8.626943005181348e-06, 'epoch': 0.29}
{'loss': 0.9456, 'grad_norm': 1.074746012687683, 'learning_rate': 8.601036269430052e-06, 'epoch': 0.29}
{'loss': 0.3013, 'grad_norm': 0.9573298692703247, 'learning_rate': 8.575129533678758e-06, 'epoch': 0.3}
{'loss': 0.2912, 'grad_norm': 0.8359588980674744, 'learning_rate': 8.549222797927462e-06, 'epoch': 0.3}
{'loss': 0.9056, 'grad_norm': 1.1320743560791016, 'learning_rate': 8.523316062176166e-06, 'epoch': 0.31}
{'loss': 0.2991, 'grad_norm': 0.8571169376373291, 'learning_rate': 8.497409326424872e-06, 'epoch': 0.31}
{'loss': 0.5322, 'grad_norm': 0.8584021329879761, 'learning_rate': 8.471502590673576e-06, 'epoch': 0.32}
{'loss': 0.5388, 'grad_norm': 0.912481963634491, 'learning_rate': 8.44559585492228e-06, 'epoch': 0.32}
{'loss': 0.4445, 'grad_norm': 0.9597629904747009, 'learning_rate': 8.419689119170985e-06, 'epoch': 0.33}
{'loss': 0.3828, 'grad_norm': 0.9330686926841736, 'learning_rate': 8.393782383419689e-06, 'epoch': 0.34}
{'loss': 0.6254, 'grad_norm': 1.016761302947998, 'learning_rate': 8.367875647668395e-06, 'epoch': 0.34}
{'loss': 0.393, 'grad_norm': 0.8420445322990417, 'learning_rate': 8.341968911917099e-06, 'epoch': 0.35}
{'loss': 0.3739, 'grad_norm': 4.204626083374023, 'learning_rate': 8.316062176165803e-06, 'epoch': 0.35}
{'loss': 0.6238, 'grad_norm': 1.0802873373031616, 'learning_rate': 8.290155440414507e-06, 'epoch': 0.36}
{'loss': 0.5613, 'grad_norm': 0.9659601449966431, 'learning_rate': 8.264248704663213e-06, 'epoch': 0.36}
{'loss': 0.5156, 'grad_norm': 0.8848123550415039, 'learning_rate': 8.238341968911918e-06, 'epoch': 0.37}
{'loss': 0.3952, 'grad_norm': 0.8703460693359375, 'learning_rate': 8.212435233160623e-06, 'epoch': 0.37}
{'loss': 0.5856, 'grad_norm': 0.723524808883667, 'learning_rate': 8.186528497409328e-06, 'epoch': 0.38}
{'loss': 0.4317, 'grad_norm': 0.7903366088867188, 'learning_rate': 8.160621761658032e-06, 'epoch': 0.38}
{'loss': 0.6356, 'grad_norm': 0.8691446781158447, 'learning_rate': 8.134715025906736e-06, 'epoch': 0.39}
{'loss': 0.4639, 'grad_norm': 0.8351852893829346, 'learning_rate': 8.10880829015544e-06, 'epoch': 0.39}
{'loss': 0.3514, 'grad_norm': 0.6321220993995667, 'learning_rate': 8.082901554404146e-06, 'epoch': 0.4}
{'loss': 0.4798, 'grad_norm': 0.9055750370025635, 'learning_rate': 8.05699481865285e-06, 'epoch': 0.4}
{'loss': 0.551, 'grad_norm': 0.9771506190299988, 'learning_rate': 8.031088082901555e-06, 'epoch': 0.41}
{'loss': 0.7073, 'grad_norm': 1.014744758605957, 'learning_rate': 8.005181347150259e-06, 'epoch': 0.41}
{'loss': 0.3534, 'grad_norm': 0.880586564540863, 'learning_rate': 7.979274611398965e-06, 'epoch': 0.42}
{'loss': 0.5754, 'grad_norm': 0.8685542345046997, 'learning_rate': 7.953367875647669e-06, 'epoch': 0.42}
{'loss': 0.4483, 'grad_norm': 0.8849413990974426, 'learning_rate': 7.927461139896375e-06, 'epoch': 0.43}
{'loss': 0.5691, 'grad_norm': 0.832760751247406, 'learning_rate': 7.901554404145079e-06, 'epoch': 0.43}
{'loss': 0.3668, 'grad_norm': 0.896132230758667, 'learning_rate': 7.875647668393783e-06, 'epoch': 0.44}
{'loss': 0.329, 'grad_norm': 0.8391252160072327, 'learning_rate': 7.849740932642487e-06, 'epoch': 0.44}
{'loss': 0.8363, 'grad_norm': 0.9460362792015076, 'learning_rate': 7.823834196891192e-06, 'epoch': 0.45}
{'loss': 0.3265, 'grad_norm': 0.6968737840652466, 'learning_rate': 7.797927461139898e-06, 'epoch': 0.45}
{'loss': 0.5725, 'grad_norm': 0.8940482139587402, 'learning_rate': 7.772020725388602e-06, 'epoch': 0.46}
{'loss': 0.6128, 'grad_norm': 0.7471238374710083, 'learning_rate': 7.746113989637306e-06, 'epoch': 0.46}
{'loss': 0.6115, 'grad_norm': 0.9247841835021973, 'learning_rate': 7.72020725388601e-06, 'epoch': 0.47}
{'loss': 0.3097, 'grad_norm': 1.023402214050293, 'learning_rate': 7.694300518134716e-06, 'epoch': 0.47}
{'loss': 0.4119, 'grad_norm': 0.9374239444732666, 'learning_rate': 7.66839378238342e-06, 'epoch': 0.48}
{'loss': 0.2883, 'grad_norm': 0.9606110453605652, 'learning_rate': 7.642487046632126e-06, 'epoch': 0.48}
{'loss': 0.5141, 'grad_norm': 1.0684373378753662, 'learning_rate': 7.61658031088083e-06, 'epoch': 0.49}
{'loss': 0.7329, 'grad_norm': 0.907875120639801, 'learning_rate': 7.590673575129535e-06, 'epoch': 0.49}
{'loss': 0.3581, 'grad_norm': 1.4813743829727173, 'learning_rate': 7.564766839378239e-06, 'epoch': 0.5}
{'loss': 0.7921, 'grad_norm': 1.053281307220459, 'learning_rate': 7.538860103626944e-06, 'epoch': 0.51}
{'loss': 0.4022, 'grad_norm': 0.6693784594535828, 'learning_rate': 7.512953367875648e-06, 'epoch': 0.51}
{'loss': 0.5226, 'grad_norm': 0.8689789772033691, 'learning_rate': 7.487046632124353e-06, 'epoch': 0.52}
{'loss': 0.3559, 'grad_norm': 0.859642744064331, 'learning_rate': 7.461139896373057e-06, 'epoch': 0.52}
{'loss': 0.5043, 'grad_norm': 0.925703763961792, 'learning_rate': 7.435233160621762e-06, 'epoch': 0.53}
{'loss': 0.445, 'grad_norm': 0.849816620349884, 'learning_rate': 7.409326424870467e-06, 'epoch': 0.53}
{'loss': 0.3361, 'grad_norm': 0.8914594054222107, 'learning_rate': 7.383419689119171e-06, 'epoch': 0.54}
{'loss': 0.6787, 'grad_norm': 0.9576083421707153, 'learning_rate': 7.357512953367876e-06, 'epoch': 0.54}
{'loss': 0.2507, 'grad_norm': 0.9256339073181152, 'learning_rate': 7.331606217616582e-06, 'epoch': 0.55}
{'loss': 0.3721, 'grad_norm': 0.7709092497825623, 'learning_rate': 7.305699481865286e-06, 'epoch': 0.55}
{'loss': 0.4938, 'grad_norm': 1.0105618238449097, 'learning_rate': 7.27979274611399e-06, 'epoch': 0.56}
{'loss': 0.3601, 'grad_norm': 1.143574595451355, 'learning_rate': 7.253886010362695e-06, 'epoch': 0.56}
{'loss': 0.3356, 'grad_norm': 0.784905195236206, 'learning_rate': 7.2279792746113995e-06, 'epoch': 0.57}
{'loss': 0.2662, 'grad_norm': 0.9888504147529602, 'learning_rate': 7.2020725388601045e-06, 'epoch': 0.57}
{'loss': 0.6064, 'grad_norm': 0.815376877784729, 'learning_rate': 7.176165803108809e-06, 'epoch': 0.58}
{'loss': 0.5501, 'grad_norm': 0.9281860589981079, 'learning_rate': 7.150259067357514e-06, 'epoch': 0.58}
{'loss': 0.4158, 'grad_norm': 0.9554753303527832, 'learning_rate': 7.124352331606218e-06, 'epoch': 0.59}
{'loss': 0.473, 'grad_norm': 0.8598635792732239, 'learning_rate': 7.098445595854922e-06, 'epoch': 0.59}
{'loss': 0.2477, 'grad_norm': 0.8450217843055725, 'learning_rate': 7.072538860103627e-06, 'epoch': 0.6}
{'loss': 0.5415, 'grad_norm': 0.9790889024734497, 'learning_rate': 7.0466321243523315e-06, 'epoch': 0.6}
{'loss': 0.4417, 'grad_norm': 1.092555284500122, 'learning_rate': 7.020725388601037e-06, 'epoch': 0.61}
{'loss': 0.7875, 'grad_norm': 1.0388628244400024, 'learning_rate': 6.994818652849742e-06, 'epoch': 0.61}
{'loss': 0.3303, 'grad_norm': 0.8189808130264282, 'learning_rate': 6.968911917098447e-06, 'epoch': 0.62}
{'loss': 0.584, 'grad_norm': 1.0733768939971924, 'learning_rate': 6.943005181347151e-06, 'epoch': 0.62}
{'loss': 0.3183, 'grad_norm': 0.8364907503128052, 'learning_rate': 6.917098445595856e-06, 'epoch': 0.63}
{'loss': 0.5751, 'grad_norm': 1.023781418800354, 'learning_rate': 6.89119170984456e-06, 'epoch': 0.63}
{'loss': 0.3403, 'grad_norm': 0.9007506966590881, 'learning_rate': 6.865284974093265e-06, 'epoch': 0.64}
{'loss': 0.7758, 'grad_norm': 1.030462384223938, 'learning_rate': 6.839378238341969e-06, 'epoch': 0.64}
{'loss': 0.5537, 'grad_norm': 0.7548647522926331, 'learning_rate': 6.813471502590674e-06, 'epoch': 0.65}
{'loss': 0.3662, 'grad_norm': 0.6818444132804871, 'learning_rate': 6.787564766839379e-06, 'epoch': 0.65}
{'loss': 0.492, 'grad_norm': 0.6560463905334473, 'learning_rate': 6.761658031088083e-06, 'epoch': 0.66}
{'loss': 0.592, 'grad_norm': 0.7523697018623352, 'learning_rate': 6.735751295336788e-06, 'epoch': 0.66}
{'loss': 0.3868, 'grad_norm': 0.8241267204284668, 'learning_rate': 6.709844559585493e-06, 'epoch': 0.67}
{'loss': 0.6122, 'grad_norm': 0.8949790596961975, 'learning_rate': 6.683937823834198e-06, 'epoch': 0.68}
{'loss': 0.7691, 'grad_norm': 0.934779942035675, 'learning_rate': 6.658031088082902e-06, 'epoch': 0.68}
{'loss': 0.3908, 'grad_norm': 0.9307014346122742, 'learning_rate': 6.632124352331607e-06, 'epoch': 0.69}
{'loss': 0.3137, 'grad_norm': 0.841033935546875, 'learning_rate': 6.6062176165803115e-06, 'epoch': 0.69}
{'loss': 0.7522, 'grad_norm': 1.0251457691192627, 'learning_rate': 6.5803108808290166e-06, 'epoch': 0.7}
{'loss': 0.5229, 'grad_norm': 0.8711403012275696, 'learning_rate': 6.554404145077721e-06, 'epoch': 0.7}
{'loss': 0.532, 'grad_norm': 0.9510254859924316, 'learning_rate': 6.528497409326425e-06, 'epoch': 0.71}
{'loss': 0.5194, 'grad_norm': 0.8545109033584595, 'learning_rate': 6.50259067357513e-06, 'epoch': 0.71}
{'loss': 0.698, 'grad_norm': 0.8434369564056396, 'learning_rate': 6.476683937823834e-06, 'epoch': 0.72}
{'loss': 0.2716, 'grad_norm': 0.7308311462402344, 'learning_rate': 6.450777202072539e-06, 'epoch': 0.72}
{'loss': 0.8146, 'grad_norm': 0.8369062542915344, 'learning_rate': 6.4248704663212435e-06, 'epoch': 0.73}
{'loss': 0.4138, 'grad_norm': 0.9072701930999756, 'learning_rate': 6.398963730569949e-06, 'epoch': 0.73}
{'loss': 0.6456, 'grad_norm': 1.0603035688400269, 'learning_rate': 6.373056994818654e-06, 'epoch': 0.74}
{'loss': 0.3786, 'grad_norm': 1.019129991531372, 'learning_rate': 6.347150259067359e-06, 'epoch': 0.74}
{'loss': 0.3916, 'grad_norm': 0.7640241980552673, 'learning_rate': 6.321243523316063e-06, 'epoch': 0.75}
{'loss': 0.7937, 'grad_norm': 2.3868517875671387, 'learning_rate': 6.295336787564768e-06, 'epoch': 0.75}
{'loss': 0.542, 'grad_norm': 0.9608807563781738, 'learning_rate': 6.269430051813472e-06, 'epoch': 0.76}
{'loss': 0.3933, 'grad_norm': 0.971348762512207, 'learning_rate': 6.243523316062176e-06, 'epoch': 0.76}
{'loss': 0.4675, 'grad_norm': 1.2680869102478027, 'learning_rate': 6.217616580310881e-06, 'epoch': 0.77}
{'loss': 0.8772, 'grad_norm': 0.8467795848846436, 'learning_rate': 6.191709844559586e-06, 'epoch': 0.77}
{'loss': 0.5283, 'grad_norm': 0.9661357998847961, 'learning_rate': 6.165803108808291e-06, 'epoch': 0.78}
{'loss': 0.522, 'grad_norm': 0.7277345061302185, 'learning_rate': 6.139896373056995e-06, 'epoch': 0.78}
{'loss': 0.6123, 'grad_norm': 0.8264330625534058, 'learning_rate': 6.113989637305699e-06, 'epoch': 0.79}
{'loss': 0.6348, 'grad_norm': 0.9117377400398254, 'learning_rate': 6.088082901554405e-06, 'epoch': 0.79}
{'loss': 0.365, 'grad_norm': 0.7495144009590149, 'learning_rate': 6.06217616580311e-06, 'epoch': 0.8}
{'loss': 0.629, 'grad_norm': 0.9202139377593994, 'learning_rate': 6.036269430051814e-06, 'epoch': 0.8}
{'loss': 0.4574, 'grad_norm': 0.817642092704773, 'learning_rate': 6.0103626943005185e-06, 'epoch': 0.81}
{'loss': 0.3118, 'grad_norm': 0.7683699131011963, 'learning_rate': 5.9844559585492235e-06, 'epoch': 0.81}
{'loss': 0.5434, 'grad_norm': 0.7886037826538086, 'learning_rate': 5.958549222797928e-06, 'epoch': 0.82}
{'loss': 0.3869, 'grad_norm': 0.79804527759552, 'learning_rate': 5.932642487046633e-06, 'epoch': 0.82}
{'loss': 0.3377, 'grad_norm': 0.7386977076530457, 'learning_rate': 5.906735751295337e-06, 'epoch': 0.83}
{'loss': 0.2733, 'grad_norm': 0.9634663462638855, 'learning_rate': 5.880829015544042e-06, 'epoch': 0.84}
{'loss': 0.2873, 'grad_norm': 0.9033777117729187, 'learning_rate': 5.854922279792746e-06, 'epoch': 0.84}
{'loss': 0.3791, 'grad_norm': 0.7759392857551575, 'learning_rate': 5.8290155440414505e-06, 'epoch': 0.85}
{'loss': 0.555, 'grad_norm': 0.9811961650848389, 'learning_rate': 5.8031088082901555e-06, 'epoch': 0.85}
{'loss': 0.4975, 'grad_norm': 0.8664981126785278, 'learning_rate': 5.7772020725388614e-06, 'epoch': 0.86}
{'loss': 0.9302, 'grad_norm': 1.1479095220565796, 'learning_rate': 5.751295336787566e-06, 'epoch': 0.86}
{'loss': 0.3664, 'grad_norm': 0.8097944855690002, 'learning_rate': 5.72538860103627e-06, 'epoch': 0.87}
{'loss': 0.359, 'grad_norm': 1.001518726348877, 'learning_rate': 5.699481865284975e-06, 'epoch': 0.87}
{'loss': 0.3865, 'grad_norm': 0.7037398219108582, 'learning_rate': 5.673575129533679e-06, 'epoch': 0.88}
{'loss': 0.2764, 'grad_norm': 0.6667627096176147, 'learning_rate': 5.647668393782384e-06, 'epoch': 0.88}
{'loss': 0.3071, 'grad_norm': 0.8470035195350647, 'learning_rate': 5.621761658031088e-06, 'epoch': 0.89}
{'loss': 0.2946, 'grad_norm': 0.9179544448852539, 'learning_rate': 5.5958549222797934e-06, 'epoch': 0.89}
{'loss': 0.3683, 'grad_norm': 0.7452818155288696, 'learning_rate': 5.569948186528498e-06, 'epoch': 0.9}
{'loss': 0.6684, 'grad_norm': 0.8301803469657898, 'learning_rate': 5.544041450777202e-06, 'epoch': 0.9}
{'loss': 0.5862, 'grad_norm': 0.9258779883384705, 'learning_rate': 5.518134715025907e-06, 'epoch': 0.91}
{'loss': 0.2908, 'grad_norm': 0.7997896671295166, 'learning_rate': 5.492227979274611e-06, 'epoch': 0.91}
{'loss': 0.5515, 'grad_norm': 0.9105134606361389, 'learning_rate': 5.466321243523317e-06, 'epoch': 0.92}
{'loss': 0.6012, 'grad_norm': 0.83064866065979, 'learning_rate': 5.440414507772021e-06, 'epoch': 0.92}
{'loss': 0.5579, 'grad_norm': 0.760399580001831, 'learning_rate': 5.414507772020726e-06, 'epoch': 0.93}
{'loss': 0.4926, 'grad_norm': 0.9484172463417053, 'learning_rate': 5.3886010362694305e-06, 'epoch': 0.93}
{'loss': 0.4309, 'grad_norm': 0.824654757976532, 'learning_rate': 5.3626943005181356e-06, 'epoch': 0.94}
{'loss': 0.4282, 'grad_norm': 0.951941728591919, 'learning_rate': 5.33678756476684e-06, 'epoch': 0.94}
{'loss': 0.4861, 'grad_norm': 1.0191141366958618, 'learning_rate': 5.310880829015545e-06, 'epoch': 0.95}
{'loss': 0.7317, 'grad_norm': 0.9370522499084473, 'learning_rate': 5.284974093264249e-06, 'epoch': 0.95}
{'loss': 0.7262, 'grad_norm': 1.106014370918274, 'learning_rate': 5.259067357512953e-06, 'epoch': 0.96}
{'loss': 0.4155, 'grad_norm': 0.8690452575683594, 'learning_rate': 5.233160621761658e-06, 'epoch': 0.96}
{'loss': 0.7695, 'grad_norm': 1.0348173379898071, 'learning_rate': 5.2072538860103625e-06, 'epoch': 0.97}
{'loss': 0.3921, 'grad_norm': 0.7686324119567871, 'learning_rate': 5.1813471502590676e-06, 'epoch': 0.97}
{'loss': 0.7081, 'grad_norm': 1.04275643825531, 'learning_rate': 5.155440414507773e-06, 'epoch': 0.98}
{'loss': 0.5294, 'grad_norm': 1.056119441986084, 'learning_rate': 5.129533678756478e-06, 'epoch': 0.98}
{'loss': 0.3886, 'grad_norm': 0.7871842384338379, 'learning_rate': 5.103626943005182e-06, 'epoch': 0.99}
{'loss': 0.4465, 'grad_norm': 1.0403472185134888, 'learning_rate': 5.077720207253887e-06, 'epoch': 0.99}
{'loss': 0.3627, 'grad_norm': 1.0968815088272095, 'learning_rate': 5.051813471502591e-06, 'epoch': 1.0}
{'loss': 0.5637, 'grad_norm': 0.8739809393882751, 'learning_rate': 5.025906735751296e-06, 'epoch': 1.01}
{'loss': 0.3663, 'grad_norm': 1.0730175971984863, 'learning_rate': 5e-06, 'epoch': 1.01}
{'loss': 0.2489, 'grad_norm': 0.642601490020752, 'learning_rate': 4.974093264248705e-06, 'epoch': 1.02}
{'loss': 0.4683, 'grad_norm': 1.270029902458191, 'learning_rate': 4.94818652849741e-06, 'epoch': 1.02}
{'loss': 0.3966, 'grad_norm': 0.7098864912986755, 'learning_rate': 4.922279792746114e-06, 'epoch': 1.03}
{'loss': 0.5352, 'grad_norm': 1.1087995767593384, 'learning_rate': 4.896373056994819e-06, 'epoch': 1.03}
{'loss': 0.3561, 'grad_norm': 0.7115294337272644, 'learning_rate': 4.870466321243524e-06, 'epoch': 1.04}
{'loss': 0.3001, 'grad_norm': 0.7292823791503906, 'learning_rate': 4.844559585492228e-06, 'epoch': 1.04}
{'loss': 0.5047, 'grad_norm': 0.8520799875259399, 'learning_rate': 4.818652849740933e-06, 'epoch': 1.05}
{'loss': 0.4968, 'grad_norm': 0.9761709570884705, 'learning_rate': 4.7927461139896375e-06, 'epoch': 1.05}
{'loss': 0.4803, 'grad_norm': 0.941666841506958, 'learning_rate': 4.766839378238342e-06, 'epoch': 1.06}
{'loss': 0.2413, 'grad_norm': 0.857654869556427, 'learning_rate': 4.740932642487048e-06, 'epoch': 1.06}
{'loss': 0.501, 'grad_norm': 0.8872515559196472, 'learning_rate': 4.715025906735752e-06, 'epoch': 1.07}
{'loss': 0.3562, 'grad_norm': 0.6613529920578003, 'learning_rate': 4.689119170984456e-06, 'epoch': 1.07}
{'loss': 0.4087, 'grad_norm': 1.10734224319458, 'learning_rate': 4.663212435233161e-06, 'epoch': 1.08}
{'loss': 0.4626, 'grad_norm': 1.0690429210662842, 'learning_rate': 4.637305699481865e-06, 'epoch': 1.08}
{'loss': 0.4665, 'grad_norm': 1.0308058261871338, 'learning_rate': 4.61139896373057e-06, 'epoch': 1.09}
{'loss': 0.1303, 'grad_norm': 0.9486850500106812, 'learning_rate': 4.585492227979275e-06, 'epoch': 1.09}
{'loss': 0.4269, 'grad_norm': 1.4386814832687378, 'learning_rate': 4.55958549222798e-06, 'epoch': 1.1}
{'loss': 0.2116, 'grad_norm': 1.227901577949524, 'learning_rate': 4.533678756476685e-06, 'epoch': 1.1}
{'loss': 0.3246, 'grad_norm': 0.983674168586731, 'learning_rate': 4.507772020725389e-06, 'epoch': 1.11}
{'loss': 0.4171, 'grad_norm': 1.1157034635543823, 'learning_rate': 4.481865284974093e-06, 'epoch': 1.11}
{'loss': 0.5354, 'grad_norm': 0.7598445415496826, 'learning_rate': 4.455958549222798e-06, 'epoch': 1.12}
{'loss': 0.55, 'grad_norm': 1.0720133781433105, 'learning_rate': 4.430051813471503e-06, 'epoch': 1.12}
{'loss': 0.2955, 'grad_norm': 0.7510941624641418, 'learning_rate': 4.404145077720207e-06, 'epoch': 1.13}
{'loss': 0.218, 'grad_norm': 0.9709896445274353, 'learning_rate': 4.3782383419689124e-06, 'epoch': 1.13}
{'loss': 0.3604, 'grad_norm': 1.016642689704895, 'learning_rate': 4.352331606217617e-06, 'epoch': 1.14}
{'loss': 0.2208, 'grad_norm': 0.7355630993843079, 'learning_rate': 4.326424870466322e-06, 'epoch': 1.14}
{'loss': 0.2242, 'grad_norm': 0.7518354654312134, 'learning_rate': 4.300518134715026e-06, 'epoch': 1.15}
{'loss': 0.4313, 'grad_norm': 0.8330957293510437, 'learning_rate': 4.274611398963731e-06, 'epoch': 1.15}
{'loss': 0.5839, 'grad_norm': 0.739578902721405, 'learning_rate': 4.248704663212436e-06, 'epoch': 1.16}
{'loss': 0.417, 'grad_norm': 0.8069799542427063, 'learning_rate': 4.22279792746114e-06, 'epoch': 1.16}
{'loss': 0.6776, 'grad_norm': 0.9648683071136475, 'learning_rate': 4.1968911917098444e-06, 'epoch': 1.17}
{'loss': 0.5574, 'grad_norm': 1.0480639934539795, 'learning_rate': 4.1709844559585495e-06, 'epoch': 1.18}
{'loss': 0.484, 'grad_norm': 0.761233389377594, 'learning_rate': 4.145077720207254e-06, 'epoch': 1.18}
{'loss': 0.6625, 'grad_norm': 0.8255932331085205, 'learning_rate': 4.119170984455959e-06, 'epoch': 1.19}
{'loss': 0.2215, 'grad_norm': 0.7817264199256897, 'learning_rate': 4.093264248704664e-06, 'epoch': 1.19}
{'loss': 0.6005, 'grad_norm': 0.8821240067481995, 'learning_rate': 4.067357512953368e-06, 'epoch': 1.2}
{'loss': 0.3526, 'grad_norm': 1.122470736503601, 'learning_rate': 4.041450777202073e-06, 'epoch': 1.2}
{'loss': 0.5916, 'grad_norm': 0.7580505609512329, 'learning_rate': 4.015544041450777e-06, 'epoch': 1.21}
{'loss': 0.5727, 'grad_norm': 0.6954234838485718, 'learning_rate': 3.989637305699482e-06, 'epoch': 1.21}
{'loss': 0.2446, 'grad_norm': 0.8904476165771484, 'learning_rate': 3.963730569948187e-06, 'epoch': 1.22}
{'loss': 0.5475, 'grad_norm': 1.04800546169281, 'learning_rate': 3.937823834196892e-06, 'epoch': 1.22}
{'loss': 0.2318, 'grad_norm': 0.9867790937423706, 'learning_rate': 3.911917098445596e-06, 'epoch': 1.23}
{'loss': 0.4378, 'grad_norm': 0.8790474534034729, 'learning_rate': 3.886010362694301e-06, 'epoch': 1.23}
{'loss': 0.4314, 'grad_norm': 0.9075559377670288, 'learning_rate': 3.860103626943005e-06, 'epoch': 1.24}
{'loss': 0.2503, 'grad_norm': 0.8689141273498535, 'learning_rate': 3.83419689119171e-06, 'epoch': 1.24}
{'loss': 0.406, 'grad_norm': 0.9015355706214905, 'learning_rate': 3.808290155440415e-06, 'epoch': 1.25}
{'loss': 0.2272, 'grad_norm': 0.8865298628807068, 'learning_rate': 3.7823834196891194e-06, 'epoch': 1.25}
{'loss': 0.5826, 'grad_norm': 0.9343792796134949, 'learning_rate': 3.756476683937824e-06, 'epoch': 1.26}
{'loss': 0.3145, 'grad_norm': 0.7708160877227783, 'learning_rate': 3.7305699481865287e-06, 'epoch': 1.26}
{'loss': 0.3848, 'grad_norm': 0.7378104329109192, 'learning_rate': 3.7046632124352333e-06, 'epoch': 1.27}
{'loss': 0.2129, 'grad_norm': 0.8466808199882507, 'learning_rate': 3.678756476683938e-06, 'epoch': 1.27}
{'loss': 0.2917, 'grad_norm': 0.7484762072563171, 'learning_rate': 3.652849740932643e-06, 'epoch': 1.28}
{'loss': 0.4156, 'grad_norm': 0.7783294320106506, 'learning_rate': 3.6269430051813476e-06, 'epoch': 1.28}
{'loss': 0.4921, 'grad_norm': 0.8098664283752441, 'learning_rate': 3.6010362694300523e-06, 'epoch': 1.29}
{'loss': 0.39, 'grad_norm': 0.8939624428749084, 'learning_rate': 3.575129533678757e-06, 'epoch': 1.29}
{'loss': 0.3276, 'grad_norm': 0.7347182035446167, 'learning_rate': 3.549222797927461e-06, 'epoch': 1.3}
{'loss': 0.4337, 'grad_norm': 0.8054161071777344, 'learning_rate': 3.5233160621761657e-06, 'epoch': 1.3}
{'loss': 0.8233, 'grad_norm': 1.0053493976593018, 'learning_rate': 3.497409326424871e-06, 'epoch': 1.31}
{'loss': 0.3386, 'grad_norm': 0.9622910022735596, 'learning_rate': 3.4715025906735754e-06, 'epoch': 1.31}
{'loss': 0.2494, 'grad_norm': 0.6932687163352966, 'learning_rate': 3.44559585492228e-06, 'epoch': 1.32}
{'loss': 0.2686, 'grad_norm': 0.9639967083930969, 'learning_rate': 3.4196891191709847e-06, 'epoch': 1.32}
{'loss': 0.3529, 'grad_norm': 1.095858097076416, 'learning_rate': 3.3937823834196893e-06, 'epoch': 1.33}
{'loss': 0.1832, 'grad_norm': 0.8448277711868286, 'learning_rate': 3.367875647668394e-06, 'epoch': 1.34}
{'loss': 0.4491, 'grad_norm': 0.9058911204338074, 'learning_rate': 3.341968911917099e-06, 'epoch': 1.34}
{'loss': 0.2908, 'grad_norm': 0.851806104183197, 'learning_rate': 3.3160621761658036e-06, 'epoch': 1.35}
{'loss': 0.4985, 'grad_norm': 0.7144804000854492, 'learning_rate': 3.2901554404145083e-06, 'epoch': 1.35}
{'loss': 0.3282, 'grad_norm': 0.8800524473190308, 'learning_rate': 3.2642487046632125e-06, 'epoch': 1.36}
{'loss': 0.2549, 'grad_norm': 1.0444000959396362, 'learning_rate': 3.238341968911917e-06, 'epoch': 1.36}
{'loss': 0.2926, 'grad_norm': 0.9430922269821167, 'learning_rate': 3.2124352331606218e-06, 'epoch': 1.37}
{'loss': 0.4403, 'grad_norm': 0.8296083211898804, 'learning_rate': 3.186528497409327e-06, 'epoch': 1.37}
{'loss': 0.2941, 'grad_norm': 0.7523472905158997, 'learning_rate': 3.1606217616580314e-06, 'epoch': 1.38}
{'loss': 0.4484, 'grad_norm': 0.8845231533050537, 'learning_rate': 3.134715025906736e-06, 'epoch': 1.38}
{'loss': 0.2258, 'grad_norm': 0.9409952759742737, 'learning_rate': 3.1088082901554407e-06, 'epoch': 1.39}
{'loss': 0.2232, 'grad_norm': 0.7966182231903076, 'learning_rate': 3.0829015544041453e-06, 'epoch': 1.39}
{'loss': 0.3161, 'grad_norm': 0.8845571875572205, 'learning_rate': 3.0569948186528495e-06, 'epoch': 1.4}
{'loss': 0.3974, 'grad_norm': 0.8708981275558472, 'learning_rate': 3.031088082901555e-06, 'epoch': 1.4}
{'loss': 0.2244, 'grad_norm': 0.7636141180992126, 'learning_rate': 3.0051813471502592e-06, 'epoch': 1.41}
{'loss': 0.2656, 'grad_norm': 0.9768233299255371, 'learning_rate': 2.979274611398964e-06, 'epoch': 1.41}
{'loss': 0.417, 'grad_norm': 0.6735494136810303, 'learning_rate': 2.9533678756476685e-06, 'epoch': 1.42}
{'loss': 0.5136, 'grad_norm': 0.6922116875648499, 'learning_rate': 2.927461139896373e-06, 'epoch': 1.42}
{'loss': 0.2413, 'grad_norm': 0.8229126334190369, 'learning_rate': 2.9015544041450778e-06, 'epoch': 1.43}
{'loss': 0.5626, 'grad_norm': 0.7794067859649658, 'learning_rate': 2.875647668393783e-06, 'epoch': 1.43}
{'loss': 0.4306, 'grad_norm': 0.7981624007225037, 'learning_rate': 2.8497409326424875e-06, 'epoch': 1.44}
{'loss': 0.4386, 'grad_norm': 0.6519434452056885, 'learning_rate': 2.823834196891192e-06, 'epoch': 1.44}
{'loss': 0.4146, 'grad_norm': 1.0261698961257935, 'learning_rate': 2.7979274611398967e-06, 'epoch': 1.45}
{'loss': 0.2195, 'grad_norm': 0.7037999629974365, 'learning_rate': 2.772020725388601e-06, 'epoch': 1.45}
{'loss': 0.3176, 'grad_norm': 0.9701761603355408, 'learning_rate': 2.7461139896373056e-06, 'epoch': 1.46}
{'loss': 0.1552, 'grad_norm': 1.1157068014144897, 'learning_rate': 2.7202072538860106e-06, 'epoch': 1.46}
{'loss': 0.2293, 'grad_norm': 0.8106536865234375, 'learning_rate': 2.6943005181347152e-06, 'epoch': 1.47}
{'loss': 0.4799, 'grad_norm': 0.6931859254837036, 'learning_rate': 2.66839378238342e-06, 'epoch': 1.47}
{'loss': 0.525, 'grad_norm': 0.9921584725379944, 'learning_rate': 2.6424870466321245e-06, 'epoch': 1.48}
{'loss': 0.83, 'grad_norm': 0.9496335983276367, 'learning_rate': 2.616580310880829e-06, 'epoch': 1.48}
{'loss': 0.4281, 'grad_norm': 0.8321980834007263, 'learning_rate': 2.5906735751295338e-06, 'epoch': 1.49}
{'loss': 0.6716, 'grad_norm': 1.2119672298431396, 'learning_rate': 2.564766839378239e-06, 'epoch': 1.49}
{'loss': 0.4473, 'grad_norm': 1.0420453548431396, 'learning_rate': 2.5388601036269435e-06, 'epoch': 1.5}
{'loss': 0.3488, 'grad_norm': 0.758085310459137, 'learning_rate': 2.512953367875648e-06, 'epoch': 1.51}
{'loss': 0.2243, 'grad_norm': 0.7834224104881287, 'learning_rate': 2.4870466321243523e-06, 'epoch': 1.51}
{'loss': 0.2945, 'grad_norm': 0.8290267586708069, 'learning_rate': 2.461139896373057e-06, 'epoch': 1.52}
{'loss': 0.6608, 'grad_norm': 0.9336819648742676, 'learning_rate': 2.435233160621762e-06, 'epoch': 1.52}
{'loss': 0.3505, 'grad_norm': 0.9153218269348145, 'learning_rate': 2.4093264248704666e-06, 'epoch': 1.53}
{'loss': 0.224, 'grad_norm': 0.9206345677375793, 'learning_rate': 2.383419689119171e-06, 'epoch': 1.53}
{'loss': 0.4067, 'grad_norm': 1.0660535097122192, 'learning_rate': 2.357512953367876e-06, 'epoch': 1.54}
{'loss': 0.2698, 'grad_norm': 0.7061493396759033, 'learning_rate': 2.3316062176165805e-06, 'epoch': 1.54}
{'loss': 0.5898, 'grad_norm': 0.8328151106834412, 'learning_rate': 2.305699481865285e-06, 'epoch': 1.55}
{'loss': 0.5594, 'grad_norm': 0.9984943866729736, 'learning_rate': 2.27979274611399e-06, 'epoch': 1.55}
{'loss': 0.2083, 'grad_norm': 0.8514673709869385, 'learning_rate': 2.2538860103626944e-06, 'epoch': 1.56}
{'loss': 0.3777, 'grad_norm': 0.8462634682655334, 'learning_rate': 2.227979274611399e-06, 'epoch': 1.56}
{'loss': 0.3127, 'grad_norm': 0.807926595211029, 'learning_rate': 2.2020725388601037e-06, 'epoch': 1.57}
{'loss': 0.3368, 'grad_norm': 0.7272363305091858, 'learning_rate': 2.1761658031088083e-06, 'epoch': 1.57}
{'loss': 0.4013, 'grad_norm': 1.1403250694274902, 'learning_rate': 2.150259067357513e-06, 'epoch': 1.58}
{'loss': 0.4869, 'grad_norm': 1.037915587425232, 'learning_rate': 2.124352331606218e-06, 'epoch': 1.58}
{'loss': 0.3257, 'grad_norm': 0.9236746430397034, 'learning_rate': 2.0984455958549222e-06, 'epoch': 1.59}
{'loss': 0.4098, 'grad_norm': 0.8756129145622253, 'learning_rate': 2.072538860103627e-06, 'epoch': 1.59}
{'loss': 0.4067, 'grad_norm': 0.8163557052612305, 'learning_rate': 2.046632124352332e-06, 'epoch': 1.6}
{'loss': 0.3805, 'grad_norm': 0.8287012577056885, 'learning_rate': 2.0207253886010365e-06, 'epoch': 1.6}
{'loss': 0.666, 'grad_norm': 0.9473971724510193, 'learning_rate': 1.994818652849741e-06, 'epoch': 1.61}
{'loss': 0.2932, 'grad_norm': 0.9730647206306458, 'learning_rate': 1.968911917098446e-06, 'epoch': 1.61}
{'loss': 0.6934, 'grad_norm': 1.2639105319976807, 'learning_rate': 1.9430051813471504e-06, 'epoch': 1.62}
{'loss': 0.541, 'grad_norm': 0.7502963542938232, 'learning_rate': 1.917098445595855e-06, 'epoch': 1.62}
{'loss': 0.2526, 'grad_norm': 0.8507408499717712, 'learning_rate': 1.8911917098445597e-06, 'epoch': 1.63}
{'loss': 0.6209, 'grad_norm': 0.8207960724830627, 'learning_rate': 1.8652849740932643e-06, 'epoch': 1.63}
[2025-07-07 20:50:19,830] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 0.4042, 'grad_norm': 0.7555132508277893, 'learning_rate': 1.839378238341969e-06, 'epoch': 1.64}
{'loss': 0.2184, 'grad_norm': 0.7625459432601929, 'learning_rate': 1.8134715025906738e-06, 'epoch': 1.64}
{'loss': 0.491, 'grad_norm': 0.6898732781410217, 'learning_rate': 1.7875647668393784e-06, 'epoch': 1.65}
{'loss': 0.1889, 'grad_norm': 0.8527777791023254, 'learning_rate': 1.7616580310880829e-06, 'epoch': 1.65}
{'loss': 0.3732, 'grad_norm': 0.9009307026863098, 'learning_rate': 1.7357512953367877e-06, 'epoch': 1.66}
{'loss': 0.6212, 'grad_norm': 0.7492113709449768, 'learning_rate': 1.7098445595854923e-06, 'epoch': 1.66}
{'loss': 0.2619, 'grad_norm': 1.1161123514175415, 'learning_rate': 1.683937823834197e-06, 'epoch': 1.67}
{'loss': 0.325, 'grad_norm': 1.0563406944274902, 'learning_rate': 1.6580310880829018e-06, 'epoch': 1.68}
{'loss': 0.3224, 'grad_norm': 0.8605097532272339, 'learning_rate': 1.6321243523316062e-06, 'epoch': 1.68}
{'loss': 0.3217, 'grad_norm': 0.7220029830932617, 'learning_rate': 1.6062176165803109e-06, 'epoch': 1.69}
{'loss': 0.5041, 'grad_norm': 0.9134677648544312, 'learning_rate': 1.5803108808290157e-06, 'epoch': 1.69}
{'loss': 0.2374, 'grad_norm': 1.0126835107803345, 'learning_rate': 1.5544041450777204e-06, 'epoch': 1.7}
{'loss': 0.3007, 'grad_norm': 0.7978687286376953, 'learning_rate': 1.5284974093264248e-06, 'epoch': 1.7}
{'loss': 0.5242, 'grad_norm': 0.7326058745384216, 'learning_rate': 1.5025906735751296e-06, 'epoch': 1.71}
{'loss': 0.1781, 'grad_norm': 0.8722479343414307, 'learning_rate': 1.4766839378238342e-06, 'epoch': 1.71}
{'loss': 0.3536, 'grad_norm': 0.870640218257904, 'learning_rate': 1.4507772020725389e-06, 'epoch': 1.72}
{'loss': 0.3361, 'grad_norm': 0.9238669872283936, 'learning_rate': 1.4248704663212437e-06, 'epoch': 1.72}
{'loss': 0.3833, 'grad_norm': 0.8625023365020752, 'learning_rate': 1.3989637305699484e-06, 'epoch': 1.73}
{'loss': 0.3797, 'grad_norm': 0.8655543923377991, 'learning_rate': 1.3730569948186528e-06, 'epoch': 1.73}
{'loss': 0.2133, 'grad_norm': 0.7673391699790955, 'learning_rate': 1.3471502590673576e-06, 'epoch': 1.74}
{'loss': 0.3146, 'grad_norm': 0.8131647706031799, 'learning_rate': 1.3212435233160623e-06, 'epoch': 1.74}
{'loss': 0.2396, 'grad_norm': 0.9960036277770996, 'learning_rate': 1.2953367875647669e-06, 'epoch': 1.75}
{'loss': 0.4431, 'grad_norm': 0.7692868113517761, 'learning_rate': 1.2694300518134717e-06, 'epoch': 1.75}
{'loss': 0.3941, 'grad_norm': 1.1909233331680298, 'learning_rate': 1.2435233160621762e-06, 'epoch': 1.76}
{'loss': 0.3755, 'grad_norm': 1.0892987251281738, 'learning_rate': 1.217616580310881e-06, 'epoch': 1.76}
{'loss': 0.5534, 'grad_norm': 0.8232518434524536, 'learning_rate': 1.1917098445595854e-06, 'epoch': 1.77}
{'loss': 0.2867, 'grad_norm': 1.032169222831726, 'learning_rate': 1.1658031088082903e-06, 'epoch': 1.77}
{'loss': 0.2733, 'grad_norm': 0.842463493347168, 'learning_rate': 1.139896373056995e-06, 'epoch': 1.78}
{'loss': 0.7245, 'grad_norm': 0.9842871427536011, 'learning_rate': 1.1139896373056995e-06, 'epoch': 1.78}
{'loss': 0.5642, 'grad_norm': 0.9444430470466614, 'learning_rate': 1.0880829015544042e-06, 'epoch': 1.79}
{'loss': 0.2328, 'grad_norm': 0.9961406588554382, 'learning_rate': 1.062176165803109e-06, 'epoch': 1.79}
{'loss': 0.5595, 'grad_norm': 0.7799118161201477, 'learning_rate': 1.0362694300518134e-06, 'epoch': 1.8}
{'loss': 0.2481, 'grad_norm': 2.573274850845337, 'learning_rate': 1.0103626943005183e-06, 'epoch': 1.8}
{'loss': 0.2936, 'grad_norm': 0.7279102802276611, 'learning_rate': 9.84455958549223e-07, 'epoch': 1.81}
{'loss': 0.5409, 'grad_norm': 0.9081263542175293, 'learning_rate': 9.585492227979275e-07, 'epoch': 1.81}
{'loss': 0.4123, 'grad_norm': 0.7428257465362549, 'learning_rate': 9.326424870466322e-07, 'epoch': 1.82}
{'loss': 0.2173, 'grad_norm': 0.7378343939781189, 'learning_rate': 9.067357512953369e-07, 'epoch': 1.82}
{'loss': 0.2758, 'grad_norm': 0.7557251453399658, 'learning_rate': 8.808290155440414e-07, 'epoch': 1.83}
{'loss': 0.3972, 'grad_norm': 0.6878364086151123, 'learning_rate': 8.549222797927462e-07, 'epoch': 1.84}
{'loss': 0.2774, 'grad_norm': 0.7395130395889282, 'learning_rate': 8.290155440414509e-07, 'epoch': 1.84}
{'loss': 0.4064, 'grad_norm': 0.8822534084320068, 'learning_rate': 8.031088082901554e-07, 'epoch': 1.85}
{'loss': 0.2104, 'grad_norm': 0.7297852039337158, 'learning_rate': 7.772020725388602e-07, 'epoch': 1.85}
{'loss': 0.5216, 'grad_norm': 0.8908668160438538, 'learning_rate': 7.512953367875648e-07, 'epoch': 1.86}
{'loss': 0.4021, 'grad_norm': 0.8597674369812012, 'learning_rate': 7.253886010362694e-07, 'epoch': 1.86}
{'loss': 0.2504, 'grad_norm': 0.7925522327423096, 'learning_rate': 6.994818652849742e-07, 'epoch': 1.87}
{'loss': 0.5021, 'grad_norm': 0.9896957278251648, 'learning_rate': 6.735751295336788e-07, 'epoch': 1.87}
{'loss': 0.2783, 'grad_norm': 0.6748519539833069, 'learning_rate': 6.476683937823834e-07, 'epoch': 1.88}
{'loss': 0.4051, 'grad_norm': 0.8480097055435181, 'learning_rate': 6.217616580310881e-07, 'epoch': 1.88}
{'loss': 0.3374, 'grad_norm': 0.8139077425003052, 'learning_rate': 5.958549222797927e-07, 'epoch': 1.89}
{'loss': 0.6448, 'grad_norm': 0.7533369660377502, 'learning_rate': 5.699481865284974e-07, 'epoch': 1.89}
{'loss': 0.4828, 'grad_norm': 0.8040704727172852, 'learning_rate': 5.440414507772021e-07, 'epoch': 1.9}
{'loss': 0.3311, 'grad_norm': 1.2289057970046997, 'learning_rate': 5.181347150259067e-07, 'epoch': 1.9}
{'loss': 0.4625, 'grad_norm': 0.7637282609939575, 'learning_rate': 4.922279792746115e-07, 'epoch': 1.91}
{'loss': 0.3358, 'grad_norm': 0.7972410321235657, 'learning_rate': 4.663212435233161e-07, 'epoch': 1.91}
{'loss': 0.4232, 'grad_norm': 0.6649189591407776, 'learning_rate': 4.404145077720207e-07, 'epoch': 1.92}
{'loss': 0.1765, 'grad_norm': 0.8117662668228149, 'learning_rate': 4.1450777202072546e-07, 'epoch': 1.92}
{'loss': 0.3167, 'grad_norm': 0.7998349070549011, 'learning_rate': 3.886010362694301e-07, 'epoch': 1.93}
{'loss': 0.3374, 'grad_norm': 0.8259660005569458, 'learning_rate': 3.626943005181347e-07, 'epoch': 1.93}
{'loss': 0.6101, 'grad_norm': 1.8678282499313354, 'learning_rate': 3.367875647668394e-07, 'epoch': 1.94}
{'loss': 0.3394, 'grad_norm': 1.0159757137298584, 'learning_rate': 3.1088082901554404e-07, 'epoch': 1.94}
{'loss': 0.2926, 'grad_norm': 0.7176821827888489, 'learning_rate': 2.849740932642487e-07, 'epoch': 1.95}
{'loss': 0.2626, 'grad_norm': 0.9229880571365356, 'learning_rate': 2.5906735751295336e-07, 'epoch': 1.95}
{'loss': 0.5018, 'grad_norm': 0.8421505689620972, 'learning_rate': 2.3316062176165804e-07, 'epoch': 1.96}
{'loss': 0.7074, 'grad_norm': 0.8830316662788391, 'learning_rate': 2.0725388601036273e-07, 'epoch': 1.96}
{'loss': 0.4983, 'grad_norm': 0.7526780366897583, 'learning_rate': 1.8134715025906736e-07, 'epoch': 1.97}
{'loss': 0.4592, 'grad_norm': 1.0883328914642334, 'learning_rate': 1.5544041450777202e-07, 'epoch': 1.97}
{'loss': 0.1414, 'grad_norm': 1.4465148448944092, 'learning_rate': 1.2953367875647668e-07, 'epoch': 1.98}
{'loss': 0.6172, 'grad_norm': 0.8788369297981262, 'learning_rate': 1.0362694300518136e-07, 'epoch': 1.98}
{'loss': 0.6235, 'grad_norm': 0.8910044431686401, 'learning_rate': 7.772020725388601e-08, 'epoch': 1.99}
{'loss': 0.616, 'grad_norm': 0.8766142129898071, 'learning_rate': 5.181347150259068e-08, 'epoch': 1.99}
{'loss': 0.267, 'grad_norm': 0.8077130317687988, 'learning_rate': 2.590673575129534e-08, 'epoch': 2.0}
{'train_runtime': 4068.7106, 'train_samples_per_second': 0.38, 'train_steps_per_second': 0.095, 'train_loss': 0.44620604797736885, 'epoch': 2.0}
