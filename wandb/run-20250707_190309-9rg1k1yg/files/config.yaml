_name_or_path:
    value: /data_x/junkim100/projects/finetune/models/Qwen3-8B
_wandb:
    value:
        cli_version: 0.21.0
        e:
            lm3zuz7bhiisxfc8o87pztyin59tolyz:
                args:
                    - --local_rank=0
                    - --deepspeed
                    - deepspeed3.json
                    - --proctitle
                    - junkim100
                    - --model_name_or_path
                    - Qwen3-8B
                    - --data_name
                    - /data_x/junkim100/projects/finetune/data/OpenMathInstruct-2/train.jsonl
                    - --wb_project
                    - kullm-pro
                    - --wb_name
                    - OpenMathInstruct2_Qwen3-8B
                    - --output_name
                    - OpenMathInstruct2_Qwen3-8B
                    - --max_length
                    - "16384"
                    - --num_train_epochs
                    - "2"
                    - --per_device_train_batch_size
                    - "1"
                    - --per_device_eval_batch_size
                    - "1"
                    - --gradient_accumulation_steps
                    - "1"
                    - --save_only_model
                    - --learning_rate
                    - "1e-5"
                    - --weight_decay
                    - "0."
                    - --warmup_ratio
                    - "0."
                    - --lr_scheduler_type
                    - cosine
                    - --bf16
                    - "True"
                    - --tf32
                    - "True"
                    - --gradient_checkpointing
                    - "True"
                    - --logging_steps
                    - "1"
                codePath: train.py
                codePathLocal: train.py
                cpu_count: 64
                cpu_count_logical: 128
                cudaVersion: "12.4"
                disk:
                    /:
                        total: "1966736678912"
                        used: "************"
                email: <EMAIL>
                executable: /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10
                gpu: NVIDIA A100-SXM4-80GB
                gpu_count: 8
                gpu_nvidia:
                    - architecture: Ampere
                      cudaCores: 6912
                      memoryTotal: "85899345920"
                      name: NVIDIA A100-SXM4-80GB
                      uuid: GPU-8582443b-e127-d8fb-a634-b004b4e0ef48
                    - architecture: Ampere
                      cudaCores: 6912
                      memoryTotal: "85899345920"
                      name: NVIDIA A100-SXM4-80GB
                      uuid: GPU-bd237d9e-e3bc-da56-4072-e5ee4dcc64e4
                    - architecture: Ampere
                      cudaCores: 6912
                      memoryTotal: "85899345920"
                      name: NVIDIA A100-SXM4-80GB
                      uuid: GPU-c1520184-6972-679b-3986-5663223af001
                    - architecture: Ampere
                      cudaCores: 6912
                      memoryTotal: "85899345920"
                      name: NVIDIA A100-SXM4-80GB
                      uuid: GPU-78c7d72e-6a02-a97f-731d-6fa52d333b6d
                    - architecture: Ampere
                      cudaCores: 6912
                      memoryTotal: "85899345920"
                      name: NVIDIA A100-SXM4-80GB
                      uuid: GPU-a77a05bb-32c7-28e0-267c-21603a593370
                    - architecture: Ampere
                      cudaCores: 6912
                      memoryTotal: "85899345920"
                      name: NVIDIA A100-SXM4-80GB
                      uuid: GPU-d75897ae-51c2-373d-512d-d5ab6841c250
                    - architecture: Ampere
                      cudaCores: 6912
                      memoryTotal: "85899345920"
                      name: NVIDIA A100-SXM4-80GB
                      uuid: GPU-c38a24a2-5d7f-8213-2cee-94525f4d3183
                    - architecture: Ampere
                      cudaCores: 6912
                      memoryTotal: "85899345920"
                      name: NVIDIA A100-SXM4-80GB
                      uuid: GPU-655fcb36-1ca7-ef64-b16e-30e48f5635e4
                host: nlp-server-18
                memory:
                    total: "1081929859072"
                os: Linux-5.15.0-134-generic-x86_64-with-glibc2.35
                program: /data_x/junkim100/projects/finetune/train.py
                python: CPython 3.10.0
                root: /data_x/junkim100/projects/finetune
                startedAt: "2025-07-07T10:03:09.140733Z"
                writerId: lm3zuz7bhiisxfc8o87pztyin59tolyz
        m:
            - "1": train/global_step
              "6":
                - 3
              "7": []
            - "2": '*'
              "5": 1
              "6":
                - 1
              "7": []
        python_version: 3.10.0
        t:
            "1":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 105
            "2":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 105
            "3":
                - 7
                - 13
                - 19
                - 62
                - 66
            "4": 3.10.0
            "5": 0.21.0
            "6": 4.52.3
            "9":
                "1": transformers_trainer
            "12": 0.21.0
            "13": linux-x86_64
accelerator_config:
    value:
        dispatch_batches: null
        even_batches: true
        gradient_accumulation_kwargs: null
        non_blocking: false
        split_batches: false
        use_seedable_sampler: true
adafactor:
    value: false
adam_beta1:
    value: 0.9
adam_beta2:
    value: 0.999
adam_epsilon:
    value: 1e-08
add_cross_attention:
    value: false
architectures:
    value:
        - Qwen3ForCausalLM
attention_bias:
    value: false
attention_dropout:
    value: 0
auto_find_batch_size:
    value: false
average_tokens_across_devices:
    value: false
bad_words_ids:
    value: null
batch_eval_metrics:
    value: false
begin_suppress_tokens:
    value: null
bf16:
    value: true
bf16_full_eval:
    value: false
bos_token_id:
    value: 151643
cache_dir:
    value: null
chunk_size_feed_forward:
    value: 0
cross_attention_hidden_size:
    value: null
data_seed:
    value: null
dataloader_drop_last:
    value: false
dataloader_num_workers:
    value: 0
dataloader_persistent_workers:
    value: false
dataloader_pin_memory:
    value: true
dataloader_prefetch_factor:
    value: null
ddp_backend:
    value: null
ddp_broadcast_buffers:
    value: null
ddp_bucket_cap_mb:
    value: null
ddp_find_unused_parameters:
    value: null
ddp_timeout:
    value: 1800
debug:
    value: []
decoder_start_token_id:
    value: null
deepspeed:
    value: deepspeed3.json
disable_tqdm:
    value: false
diversity_penalty:
    value: 0
do_eval:
    value: false
do_predict:
    value: false
do_sample:
    value: false
do_train:
    value: false
early_stopping:
    value: false
encoder_no_repeat_ngram_size:
    value: 0
eos_token_id:
    value: 151645
eval_accumulation_steps:
    value: null
eval_delay:
    value: 0
eval_do_concat_batches:
    value: true
eval_on_start:
    value: false
eval_steps:
    value: null
eval_strategy:
    value: "no"
eval_use_gather_object:
    value: false
exponential_decay_length_penalty:
    value: null
finetuning_task:
    value: null
forced_bos_token_id:
    value: null
forced_eos_token_id:
    value: null
fp16:
    value: false
fp16_backend:
    value: auto
fp16_full_eval:
    value: false
fp16_opt_level:
    value: O1
fsdp:
    value: []
fsdp_config:
    value:
        min_num_params: 0
        xla: false
        xla_fsdp_grad_ckpt: false
        xla_fsdp_v2: false
fsdp_min_num_params:
    value: 0
fsdp_transformer_layer_cls_to_wrap:
    value: null
full_determinism:
    value: false
gradient_accumulation_steps:
    value: 1
gradient_checkpointing:
    value: true
gradient_checkpointing_kwargs:
    value: null
greater_is_better:
    value: null
group_by_length:
    value: false
half_precision_backend:
    value: auto
head_dim:
    value: 128
hidden_act:
    value: silu
hidden_size:
    value: 4096
hub_always_push:
    value: false
hub_model_id:
    value: null
hub_private_repo:
    value: null
hub_strategy:
    value: every_save
hub_token:
    value: <HUB_TOKEN>
id2label:
    value:
        "0": LABEL_0
        "1": LABEL_1
ignore_data_skip:
    value: false
include_for_metrics:
    value: []
include_inputs_for_metrics:
    value: false
include_num_input_tokens_seen:
    value: false
include_tokens_per_second:
    value: false
initializer_range:
    value: 0.02
intermediate_size:
    value: 12288
is_decoder:
    value: false
is_encoder_decoder:
    value: false
jit_mode_eval:
    value: false
label_names:
    value: null
label_smoothing_factor:
    value: 0
label2id:
    value:
        LABEL_0: 0
        LABEL_1: 1
learning_rate:
    value: 1e-05
length_column_name:
    value: length
length_penalty:
    value: 1
load_best_model_at_end:
    value: false
local_rank:
    value: 0
log_level:
    value: passive
log_level_replica:
    value: warning
log_on_each_node:
    value: true
logging_dir:
    value: trainer_output/runs/Jul07_18-57-55_nlp-server-18
logging_first_step:
    value: false
logging_nan_inf_filter:
    value: true
logging_steps:
    value: 1
logging_strategy:
    value: steps
lr_scheduler_type:
    value: cosine
max_grad_norm:
    value: 0.3
max_length:
    value: 20
max_position_embeddings:
    value: 40960
max_steps:
    value: -1
max_window_layers:
    value: 36
metric_for_best_model:
    value: null
min_length:
    value: 0
model/num_parameters:
    value: 0
model_type:
    value: qwen3
mp_parameters:
    value: ""
neftune_noise_alpha:
    value: null
no_cuda:
    value: false
no_repeat_ngram_size:
    value: 0
num_attention_heads:
    value: 32
num_beam_groups:
    value: 1
num_beams:
    value: 1
num_hidden_layers:
    value: 36
num_key_value_heads:
    value: 8
num_return_sequences:
    value: 1
num_train_epochs:
    value: 2
optim:
    value: adamw_torch
optim_args:
    value: null
optim_target_modules:
    value: null
output_attentions:
    value: false
output_dir:
    value: ./output/Qwen3-8B/OpenMathInstruct2_Qwen3-8B
output_hidden_states:
    value: false
output_scores:
    value: false
overwrite_output_dir:
    value: false
pad_token_id:
    value: null
past_index:
    value: -1
per_device_eval_batch_size:
    value: 1
per_device_train_batch_size:
    value: 1
per_gpu_eval_batch_size:
    value: null
per_gpu_train_batch_size:
    value: null
prediction_loss_only:
    value: false
prefix:
    value: null
problem_type:
    value: null
proctitle:
    value: junkim100
push_to_hub:
    value: false
push_to_hub_model_id:
    value: null
push_to_hub_organization:
    value: null
push_to_hub_token:
    value: <PUSH_TO_HUB_TOKEN>
ray_scope:
    value: last
remove_invalid_values:
    value: false
remove_unused_columns:
    value: true
repetition_penalty:
    value: 1
report_to:
    value:
        - wandb
restore_callback_states_from_checkpoint:
    value: false
resume_from_checkpoint:
    value: null
return_dict:
    value: true
return_dict_in_generate:
    value: false
rms_norm_eps:
    value: 1e-06
rope_scaling:
    value: null
rope_theta:
    value: 1000000
run_name:
    value: trainer_output
save_on_each_node:
    value: false
save_only_model:
    value: true
save_safetensors:
    value: true
save_steps:
    value: 500
save_strategy:
    value: steps
save_total_limit:
    value: null
seed:
    value: 42
sep_token_id:
    value: null
skip_memory_metrics:
    value: true
sliding_window:
    value: null
suppress_tokens:
    value: null
task_specific_params:
    value: null
temperature:
    value: 1
tf_legacy_loss:
    value: false
tf32:
    value: true
tie_encoder_decoder:
    value: false
tie_word_embeddings:
    value: false
tokenizer_class:
    value: null
top_k:
    value: 50
top_p:
    value: 1
torch_compile:
    value: false
torch_compile_backend:
    value: null
torch_compile_mode:
    value: null
torch_dtype:
    value: float32
torch_empty_cache_steps:
    value: null
torchdynamo:
    value: null
torchscript:
    value: false
tpu_metrics_debug:
    value: false
tpu_num_cores:
    value: null
transformers_version:
    value: 4.52.3
typical_p:
    value: 1
use_bfloat16:
    value: false
use_cache:
    value: true
use_cpu:
    value: false
use_ipex:
    value: false
use_legacy_prediction_loop:
    value: false
use_liger_kernel:
    value: false
use_mps_device:
    value: false
use_sliding_window:
    value: false
vocab_size:
    value: 151936
warmup_ratio:
    value: 0
warmup_steps:
    value: 0
wb_name:
    value: OpenMathInstruct2_Qwen3-8B
wb_project:
    value: kullm-pro
weight_decay:
    value: 0
